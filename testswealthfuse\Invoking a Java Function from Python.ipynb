{"cells": [{"cell_type": "code", "execution_count": null, "id": "259897c4-aa31-46c7-8e10-46401a40c4a6", "metadata": {}, "outputs": [], "source": ["import jpype\n", "import jpype.imports\n", "from jpype.types import *\n", "\n", "# Start the JVM\n", "jpype.startJVM(classpath=['C:\\_Business Main\\Ventures Main\\_WealthFuse\\Product Factory\\oembacktest.jar'])\n", "\n", "# Import your Java class\n", "from com.example import YourJavaClass\n", "\n", "# Create an instance of the Java class\n", "java_instance = YourJavaClass()\n", "\n", "# Call the Java method\n", "result = java_instance.yourMethod()\n", "\n", "# Print the result\n", "print(result)\n", "\n", "# Shutdown the JVM\n", "jpype.shutdownJVM()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}