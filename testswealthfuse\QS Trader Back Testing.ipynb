{"cells": [{"cell_type": "code", "execution_count": 2, "id": "a7816a28-5dae-4f09-b25b-cd3e16c1c63a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading CSV files into DataFrames...\n", "Loading CSV file for symbol 'EQ:SPY'...\n", "Loading CSV file for symbol 'EQ:AGG'...\n", "Adjusting pricing in CSV files...\n", "Adjusting CSV file for symbol 'EQ:SPY'...\n", "Adjusting CSV file for symbol 'EQ:AGG'...\n", "Initialising simulated broker \"Backtest Simulated Broker Account\"...\n", "(2003-09-30 14:30:00+00:00) - portfolio creation: Portfolio \"000001\" created at broker \"Backtest Simulated Broker Account\"\n", "(2003-09-30 14:30:00+00:00) - subscription: 1000000.00 subscribed to portfolio \"000001\"\n", "Beginning backtest simulation...\n", "(2003-09-30 14:30:00+00:00) - market_open\n", "(2003-09-30 21:00:00+00:00) - market_close\n", "(2003-09-30 21:00:00+00:00) - trading logic and rebalance\n", "(2003-09-30 21:00:00+00:00) - target weights: {'EQ:AGG': 0.4, 'EQ:SPY': 0.6}\n"]}, {"ename": "AttributeError", "evalue": "`np.NaN` was removed in the NumPy 2.0 release. Use `np.nan` instead.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 45\u001b[0m\n\u001b[0;32m     34\u001b[0m strategy_alpha_model \u001b[38;5;241m=\u001b[39m FixedSignalsAlphaModel({\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEQ:SPY\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0.6\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEQ:AGG\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0.4\u001b[39m})\n\u001b[0;32m     35\u001b[0m strategy_backtest \u001b[38;5;241m=\u001b[39m BacktestTradingSession(\n\u001b[0;32m     36\u001b[0m     start_dt,\n\u001b[0;32m     37\u001b[0m     end_dt,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     43\u001b[0m     data_handler\u001b[38;5;241m=\u001b[39mdata_handler\n\u001b[0;32m     44\u001b[0m )\n\u001b[1;32m---> 45\u001b[0m strategy_backtest\u001b[38;5;241m.\u001b[39mrun()\n\u001b[0;32m     47\u001b[0m \u001b[38;5;66;03m# Construct benchmark assets (buy & hold SPY)\u001b[39;00m\n\u001b[0;32m     48\u001b[0m benchmark_assets \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEQ:SPY\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\qstrader\\trading\\backtest.py:415\u001b[0m, in \u001b[0;36mBacktestTradingSession.run\u001b[1;34m(self, results)\u001b[0m\n\u001b[0;32m    410\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m settings\u001b[38;5;241m.\u001b[39mPRINT_EVENTS:\n\u001b[0;32m    411\u001b[0m             \u001b[38;5;28mprint\u001b[39m(\n\u001b[0;32m    412\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m(\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m) - trading logic \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    413\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mand rebalance\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m event\u001b[38;5;241m.\u001b[39mts\n\u001b[0;32m    414\u001b[0m             )\n\u001b[1;32m--> 415\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mqts(dt, stats\u001b[38;5;241m=\u001b[39mstats)\n\u001b[0;32m    417\u001b[0m \u001b[38;5;66;03m# Out of market hours we want a daily\u001b[39;00m\n\u001b[0;32m    418\u001b[0m \u001b[38;5;66;03m# performance update, but only if we\u001b[39;00m\n\u001b[0;32m    419\u001b[0m \u001b[38;5;66;03m# are past the 'burn in' period\u001b[39;00m\n\u001b[0;32m    420\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m event\u001b[38;5;241m.\u001b[39mevent_type \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmarket_close\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\qstrader\\system\\qts.py:172\u001b[0m, in \u001b[0;36mQuantTradingSystem.__call__\u001b[1;34m(self, dt, stats)\u001b[0m\n\u001b[0;32m    155\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    156\u001b[0m \u001b[38;5;124;03mConstruct the portfolio and (optionally) execute the orders\u001b[39;00m\n\u001b[0;32m    157\u001b[0m \u001b[38;5;124;03mwith the broker.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    169\u001b[0m \u001b[38;5;124;03m`None`\u001b[39;00m\n\u001b[0;32m    170\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    171\u001b[0m \u001b[38;5;66;03m# Construct the target portfolio\u001b[39;00m\n\u001b[1;32m--> 172\u001b[0m rebalance_orders \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mportfolio_construction_model(dt, stats\u001b[38;5;241m=\u001b[39mstats)\n\u001b[0;32m    174\u001b[0m \u001b[38;5;66;03m# Execute the orders\u001b[39;00m\n\u001b[0;32m    175\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexecution_handler(dt, rebalance_orders)\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\qstrader\\portcon\\pcm.py:291\u001b[0m, in \u001b[0;36mPortfolioConstructionModel.__call__\u001b[1;34m(self, dt, stats)\u001b[0m\n\u001b[0;32m    288\u001b[0m     stats[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtarget_allocations\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mappend(alloc_dict)\n\u001b[0;32m    290\u001b[0m \u001b[38;5;66;03m# Calculate target portfolio in notional\u001b[39;00m\n\u001b[1;32m--> 291\u001b[0m target_portfolio \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_generate_target_portfolio(dt, full_weights)\n\u001b[0;32m    293\u001b[0m \u001b[38;5;66;03m# Obtain current Broker account portfolio\u001b[39;00m\n\u001b[0;32m    294\u001b[0m current_portfolio \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_obtain_current_portfolio()\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\qstrader\\portcon\\pcm.py:140\u001b[0m, in \u001b[0;36mPortfolioConstructionModel._generate_target_portfolio\u001b[1;34m(self, dt, weights)\u001b[0m\n\u001b[0;32m    122\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_generate_target_portfolio\u001b[39m(\u001b[38;5;28mself\u001b[39m, dt, weights):\n\u001b[0;32m    123\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    124\u001b[0m \u001b[38;5;124;03m    Generate the number of units (shares/lots) per Asset based on the\u001b[39;00m\n\u001b[0;32m    125\u001b[0m \u001b[38;5;124;03m    target weight vector.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    138\u001b[0m \u001b[38;5;124;03m        Target asset quantities in integral units.\u001b[39;00m\n\u001b[0;32m    139\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 140\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39morder_sizer(dt, weights)\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\qstrader\\portcon\\order_sizer\\dollar_weighted.py:159\u001b[0m, in \u001b[0;36mDollarWeightedCashBufferedOrderSizer.__call__\u001b[1;34m(self, dt, weights)\u001b[0m\n\u001b[0;32m    157\u001b[0m \u001b[38;5;66;03m# Calculate integral target asset quantity assuming broker costs\u001b[39;00m\n\u001b[0;32m    158\u001b[0m after_cost_dollar_weight \u001b[38;5;241m=\u001b[39m pre_cost_dollar_weight \u001b[38;5;241m-\u001b[39m est_costs\n\u001b[1;32m--> 159\u001b[0m asset_price \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata_handler\u001b[38;5;241m.\u001b[39mget_asset_latest_ask_price(\n\u001b[0;32m    160\u001b[0m     dt, asset\n\u001b[0;32m    161\u001b[0m )\n\u001b[0;32m    163\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m np\u001b[38;5;241m.\u001b[39misnan(asset_price):\n\u001b[0;32m    164\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    165\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAsset price for \u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m at timestamp \u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m is Not-a-Number (NaN). \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m    166\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mThis can occur if the chosen backtest start date is earlier \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m    167\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mthan the first available price for a particular asset. Try \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m    168\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmodifying the backtest start date and re-running.\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m%\u001b[39m (asset, dt)\n\u001b[0;32m    169\u001b[0m     )\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\qstrader\\data\\backtest_data_handler.py:34\u001b[0m, in \u001b[0;36mBacktestDataHandler.get_asset_latest_ask_price\u001b[1;34m(self, dt, asset_symbol)\u001b[0m\n\u001b[0;32m     31\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     32\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m     33\u001b[0m \u001b[38;5;66;03m# TODO: Check for asset in Universe\u001b[39;00m\n\u001b[1;32m---> 34\u001b[0m ask \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mNaN\n\u001b[0;32m     35\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m ds \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdata_sources:\n\u001b[0;32m     36\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\numpy\\__init__.py:411\u001b[0m, in \u001b[0;36m__getattr__\u001b[1;34m(attr)\u001b[0m\n\u001b[0;32m    408\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(__former_attrs__[attr])\n\u001b[0;32m    410\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m attr \u001b[38;5;129;01min\u001b[39;00m __expired_attributes__:\n\u001b[1;32m--> 411\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\n\u001b[0;32m    412\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`np.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattr\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m` was removed in the NumPy 2.0 release. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    413\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m__expired_attributes__[attr]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    414\u001b[0m     )\n\u001b[0;32m    416\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m attr \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mchararray\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m    417\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[0;32m    418\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`np.chararray` is deprecated and will be removed from \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    419\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mthe main namespace in the future. Use an array with a string \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    420\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mor bytes dtype instead.\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m, stacklevel\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m)\n", "\u001b[1;31mAttributeError\u001b[0m: `np.NaN` was removed in the NumPy 2.0 release. Use `np.nan` instead."]}], "source": ["import os\n", "\n", "import pandas as pd\n", "import pytz\n", "\n", "from qstrader.alpha_model.fixed_signals import FixedSignalsAlphaModel\n", "from qstrader.asset.equity import Equity\n", "from qstrader.asset.universe.static import StaticUniverse\n", "from qstrader.data.backtest_data_handler import BacktestDataHandler\n", "from qstrader.data.daily_bar_csv import CSVDailyBarDataSource\n", "from qstrader.statistics.tearsheet import TearsheetStatistics\n", "from qstrader.trading.backtest import BacktestTradingSession\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    start_dt = pd.Timestamp('2003-09-30 14:30:00', tz=pytz.UTC)\n", "    end_dt = pd.Timestamp('2019-12-31 23:59:00', tz=pytz.UTC)\n", "\n", "    # Construct the symbols and assets necessary for the backtest\n", "    strategy_symbols = ['SPY', 'AGG']\n", "    strategy_assets = ['EQ:%s' % symbol for symbol in strategy_symbols]\n", "    strategy_universe = StaticUniverse(strategy_assets)\n", "\n", "    # To avoid loading all CSV files in the directory, set the\n", "    # data source to load only those provided symbols\n", "    csv_dir = os.environ.get('QSTRADER_CSV_DATA_DIR', '.')\n", "    data_source = CSVDailyBarDataSource(csv_dir, Equity, csv_symbols=strategy_symbols)\n", "    data_handler = BacktestDataHandler(strategy_universe, data_sources=[data_source])\n", "\n", "    # Construct an Alpha Model that simply provides\n", "    # static allocations to a universe of assets\n", "    # In this case 60% SPY ETF, 40% AGG ETF,\n", "    # rebalanced at the end of each month\n", "    strategy_alpha_model = FixedSignalsAlphaModel({'EQ:SPY': 0.6, 'EQ:AGG': 0.4})\n", "    strategy_backtest = BacktestTradingSession(\n", "        start_dt,\n", "        end_dt,\n", "        strategy_universe,\n", "        strategy_alpha_model,\n", "        rebalance='end_of_month',\n", "        long_only=True,\n", "        cash_buffer_percentage=0.01,\n", "        data_handler=data_handler\n", "    )\n", "    strategy_backtest.run()\n", "\n", "    # Construct benchmark assets (buy & hold SPY)\n", "    benchmark_assets = ['EQ:SPY']\n", "    benchmark_universe = StaticUniverse(benchmark_assets)\n", "\n", "    # Construct a benchmark Alpha Model that provides\n", "    # 100% static allocation to the SPY ETF, with no rebalance\n", "    benchmark_alpha_model = FixedSignalsAlphaModel({'EQ:SPY': 1.0})\n", "    benchmark_backtest = BacktestTradingSession(\n", "        start_dt,\n", "        end_dt,\n", "        benchmark_universe,\n", "        benchmark_alpha_model,\n", "        rebalance='buy_and_hold',\n", "        long_only=True,\n", "        cash_buffer_percentage=0.01,\n", "        data_handler=data_handler\n", "    )\n", "    benchmark_backtest.run()\n", "\n", "    # Performance Output\n", "    tearsheet = TearsheetStatistics(\n", "        strategy_equity=strategy_backtest.get_equity_curve(),\n", "        benchmark_equity=benchmark_backtest.get_equity_curve(),\n", "        title='60/40 US Equities/Bonds'\n", "    )\n", "    tearsheet.plot_results()"]}, {"cell_type": "code", "execution_count": null, "id": "5f2c8aec-dadd-4e5b-bc22-0890673052ae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}