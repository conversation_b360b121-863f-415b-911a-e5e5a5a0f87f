1.9.78.123:
  - PR#479 Fix errors for simulated orders
1.9.77.123:
  - PR#472
    - Added posibitity for Black theme for charts
    - Added posibitity to run on matplotlib 3.6+
    - Added posibitity to run on Python 3.9+

1.9.76.123:
  - PR#405 Fix initial Renko bricks
  - Add option to select fixing of initial Renko bricks
  - PR#403 partial order execution iterpending reported incorrectly
  - PR#402 bug fix: #5 fixing writer.py after 1.9.75.123 pull
  - PR#406 trade.py upgraded to be able to be unpickled. (#406)
  - PR#411 [bug fix] frompackages directive functionality seems to be
    broken when using inheritance (#411)
  - Typo corrections PR#409, PR#407

1.9.75.123:
  - Adding extra day before dtcmp calc, as otherwise the extradays
    have no effect (#388)
  - Fixing the issue with TWS API Bust events (err code 10225) (#396)
  - Add support for ASK quotes for CASH assets (#395) plus fixes
  - Remove duplicated note (#386)
  - Fixing time.clock for python>=3.8 (#394)
  - Changed file initiation for WriterFile to make it work under
    multi-process optimization (#397) plus fixes
  - Fixed backend loading if a backend is loaded (Google Collab) and
    backend to use on MacOSX
  - Fix: crumb in feeds.YahooFinanceData (#400)
  - Fix color assignments, ticks line widths and some pep-8 improvements
  - Fix timeframe/compression detection when plotting
  - Fix default value for ticks display format on X-axis
  - Sample with ta-lib SAR test
  - Generic support of multiple "text/*" content types for Yahoo

**********:
  - Correct calculation in haDelta indicator
  - Use initial datalabel for non-overlaid volume plot

**********:
  - Add utility NonZeroDifference indicator
  - Redefine CrossUp, CrossDown and CrossOver indicators using
    NonZeroDifference to cover the case in which the crossing entities
    converge right before crossing up and down
  - PR #382 (Travis: Python 3.7, 3.8-dev travis), PR #383 PivotPoint doc

**********:
  - Cover case in which result in high-level overridden operations have
    multiple lines and wer not be taken into account for minimum period
    calculations
  - Add "Int" variants of percentage based sizers to import
  - Trades observer to show net profit instead of brutto, with parameter
    to control behavior

**********:
  - Improve on indicator legend plotting to overcome matplotlib legend
    reordering
  - Added PercenSizerInt and AllSizerInt which truncate the returned size
    to an int, suited better for stocks/futures

**********:
  - Use opening price for submission check for Market orders when
    cheat-on-open is active
  - Update pnlcomm on all operations and not just profit/loss locking
  - Correct comment for fillalpha and add baralpha for candlestick opacity
  - Merge PR 378 (doc typo) PR 378 (rollover for live feeds and tz use
    in datetime utilities)
  - Use internal dict for data feed presence test and update trade observer

**********:
  - Fix offline Yahoo feed by moving the new adjclose line up to the offline
    feed
  - Adapt the yahoodownload tool to the current status (ex: data not reversed)
  - Redownload all yahoo data feeds

1.9.68.122
  - [PR 376] Fix call to _nextday in TradingCalendar
  - Clean up and rework of Yahoo Data. The data feeds seems to be reliable
    again
  - IBStore Support for IND prices (simplfication of PR 373)

1.9.67.122
  - Fix compression only scenarios when resampling and resampling after
    changes in 1.9.66.122
  - Final correction for rollover fix introduced in 1.9.66.122
  - Cover use case for mininum period calculation when all
    operations/indicators don't use the data feeds directly but lines of it

1.9.66.122
  - Fix regression introduced with 8f537a1c2c271eb5cfc592b373697732597d26d6
    which voids the count of lost trades
  - Allow rollover to distinguish between no values temporarily (with None)
    and no values permanently (with False)
  - Avoid math domain error for negative returns in logarithmic calculations
  - Fix local variable declaration for compound returns
  - Fix typo in date2num tz conversion which shows up in direct usage

1.9.65.122
  - Fix commission info assigment and orderref seeking in OandaStore (PR#367)
  - Add strategy type to OptReturn (PR#364)
  - Fix prepend_constant for OLS_Transformation (PR#368)
  - Fix LogReturnsRolling compression when not specified (PR#369)
  - Have ints instead of bools in some values with 1 Trade in TradeAnalyzer

**********
  - Avoid stage2 comparison using [0] in API methods
  - Support plotname, if given, as name of indicator in csv output

**********
  - Add optimization callbacks when running with 1 Core
  - Correct sell_bracket by removing old append code
  - Correct typo in store.py
  - Pass period from RateOfChange100 to underlying ROC

**********
  - Correct PSAR acceleration capping
  - Enable PandasData line extension without the need to extend datafields

**********
  - Add `_skipnan` to plotlines to allow joining two points with a line
  - buy_bracket/sell_bracket allow suppressing stop/limit orders
  - Add stop-loss approaches sample
  - Correct codes for minutes compression

**********
  - Remove unused files
  - README update, Docstring corrections, documentation corrections
  - Update travis settings

**********
  - Provide default fundmode methods for all brokers
  - Correct order notification if positions exist when starting the broker
    and will be simulated
  - Correct csv values output if object has no length

**********
  - PR #326 Fix set_fundmode in bbroker
  - Synchronize fund history mode with master clock
  - Allow relocation of legend in plotting charts
  - Adapt broker observer to fund mode

**********
  - Handle volume as string null in YahooFinanceData
  - Corrections/Improvements to order history support
  - Add fund history support
  - Increase plotting margin of trade observers

**********
  - Add addorder_history support to replay history of orders
  - Add swapcloses to YahooFinanceXXX family to allow end users to control what
    the adjusted price actually is
  - Some docs and samples updates
  - Change default for _nextforce to False as it should be for most indicators

1.9.54.122
  - Add haDelta indicator
  - Allow indicators to disable runonce
  - Add Renko bricks
  - Rework ix -> iloc pull request and autodetection algorithm in PandasData

1.9.53.121
  - Fix #323 by providing default properties/methods for fundvalue/fundshares
    for all brokers

1.9.52.121
  - Redownload the YahooFinance sample data yhoo-1996-2015
  - Add unstable exception for TALIB SAR
  - Add notes about usage of Hurst exponent and lag_start/lag_end parameters
    to override default lag values
  - Fix #321 by correcting typo in Writer.writelines
  - Add _start/start methods to Observers
  - Add fund tracking mode to the observers
  - Add new observers FundValue/FundShares
  - Adapt observers to fundmode: Value, TimeReturn, LogReturns, DrawDown,
    Benchmark
  - Adapt analyzers to fundmode: DrawDown, Leverage, LogReturnsRolling,
    PeriodStats, LogReturns, Sharpe, TimeReturn, VWR
  - PR #319 for Pandas .ix deprecation (rewritten)

1.9.51.121
  - Fix PSAR calculations for resampled/replayed streams
  - Sample for psar with intraday resampling 5 -> 15 minutes
  - Set the environment of a backfill_from data in master ibdata
  - Add dnames to the strategy documentation
  - Allow plotmaster to point to itself
  - Add plotylimited option to control vertical scaling locking on data plots
  - Add (semi)logarithmic plotlog control to plotinfo
  - Simplify live status detection for IB to allow optimization
  - Keep the observer cycles always synchronized with the strategy cycles
    regardless of running mode
  - Correct arguments for top level cerebro callback for data notifications
  - Add HeikinAshi candles indicator (plotted as lines)
  - Add HeikinAshi as filter to directly modify the data
  - Plot only last close value if lineonclose is plotted and correct high
    printout
  - Add PR #320 with indicators AwesomeOscillator,
    AccelerationDecelerationOscillator, RelativeMomentumIndex
  - Doc corrections and additions, including PR #319
  - PR #315 with rewrite to generalize setting the backend

**********
  - Add TrueStrengthIndicator
  - Port YahooDownload tool to v7 API
  - rewrite tool py3 bytes/str compatibility during write
  - Support internal re-fetching of linetokens in csv based datas
  - Support Yahoo skipping of lines with null values
  - New adaptations to Yahoo new format for adjusted prices
  - Update of data samples in Yahoo format
  - Update of documents and samples to make use of YahooFinanceCSVData
    consistent with chosen data sample

**********
  - Add support for new Yahoo v7 api
  - Quandl: Allow dataset specification, apikey correction and cosmetics

**********
  - Quandl Data Feed Online/Offline (at least for WIKI EOD)
    - Online: bt.feeds.Quandl
    - Offline: bt.feeds.QuandlCSV
  - Add studies category for indicators that draw in the past
    (study events in past price movements)
  - PR #307 Fractal study added to studies/contributions
  - PR #304 Timer corrections for weekdays filter
  - Docs corrections and typos

**********
  - Add PR #303 with hook support for btrun
  - Fix regression introduced with trading calendars for replaying
  - Avoid a DivisionByZeroError in SharpeRatio if not enough returns for the
    calculation

**********
  - Finish timers implementation and documentation
  - Add timers samples and cheat-on-open sample
  - Add a List class to check for containment with __contains__ rather than
    standard list is or __eq__

**********
  - Fix #302 to plot resampled data with non aligned end of sessions
  - PR #297 to save figures to files (refactored to save multiple strategies
    and multiple figures)
  - Ensure a data feed has always a non-empty _name if possible
  - Alias getcash/getvalue to get_cash/get_value in broker subclasses if the
    latter are missing
  - PR #300 Set tools as executables
  - PR #301 Metatrader4 csv format
  - Documentation updates

**********
  - Timer calls implementation
  - Broker support for cheat-on-open
  - Add cheat_on_open to cerebro to allow next_open
  - Finish trading calendar resampling for weeks
  - Support Yahoo download over proxies
  - Doc corrections/additions
  - Support quick broker notifications

**********
  - Oanda support for bracket orders
  - Oanda support for stop trailing order
  - Filling in plotting support numeric values and control of alpha blending
  - Documentation updates (filling, addobservermulti)
  - Fix wrong calling of sizer with fixed isbuy=True after refactoring for
    mixing of buy/sell and order_target_xxx

**********
  - Add tradingcalendar
  - Add tz support for strategies
  - Docs updates
  - Add multi/tradingcalendar samples
  - Add div/floordiv operations to lines
  - Return data references in all cerebro methods adding data stream

1.9.41.116
  - Keep processing orders after create in OandaStore after change to process
    new messages
  - Manage CFDs also as cash data in rqtMktData

1.9.40.116
  - Fix #295 by only managing tf and cp if resample/replay have been specified
  - Correct expire and cancel in OandaStore
  - Correct BollingerBands to use the chosen movav for the StdDev calcs
  - Ensure parameters wit plotinfo and no plotname get a name granted

1.9.39.116
  - Fix #294 which break plotting by plotting with no indicators/observers

1.9.38.116
  - Plotting control options for last value in legend and right hand side
    tag
  - Documentation improvements
  - Support numeric timestamps in CSVGenericData

1.9.37.116
  - Add new samples (OCO, StopTrail/Limit, LRSI, partial-plot, psar,
    future-spot)
  - Add Bracket order support
  - Bracket order for IB and adapted sample
  - Correct cancel order message reception in OandaStore
  - Cosmectic changes to quickstart examples
  - Document bracket, stoptrail/limit, oco, partial-plotting, same axis
    plotting, future-vs-spot

1.9.36.116
  - StopTrail/StopTrailLimit/Oco for Interactive Brokers
  - PR #290 for child OCO orders
  - Oco and other generic parameters passed transparently from any order
    generating method (ex: order_target_size) down to buy and sell
  - Correct pricelimit parameter in ib
  - Use strategy datetime instead of data0 and ensure a complete header in
    Positions analyzer

1.9.35.116
  - Catch limit/stop order creation earlier in Oanda Store
  - StopTrail/StopTrailLimit orders for backtesting

1.9.34.116
  - Docs updates
  - OCO implementation for backtesting

1.9.33.116
  - Make sure sizer is only used if size is not None (default)
  - Doc corrections
  - Improve legend presentation in sameaxis mode

**********
  - Added Calmar, TimeDrawDown and PeriodStats analyzers
  - Reach data by names as dict or dot notation
  - Allow one asset to compensate the positions of another
  - Add more python versions to Travis PR #276
  - Support plotting datas on same y-axis
  - Update sample in contrib pair trading PR #273
  - PR #274 number of tranches to FixedSize Sizer and add FixedSizeTarget
  - Close #280 exception when get pyfolio analyzer agaist multiple data
  - Close #277 (inc PR #277) by entering re-calculation of xstart and xend
    plotting indices

**********
  - Add Indicator HurstExponent (requires numpy)
  - Allow plotting specific date ranges with start and end named arguments
    to plot
  - Address #269 missing last bar backfill_from
  - Fix typo (#271) in frompackages import for InfluxDB feed
  - Add OLS_Slope_InterceptN, OLS_BetaN, OLS_TransformationN and Coint
  - Ensure broker has prices even if tick_xxx is not defined

**********
  - Add LaguerreRSI PR #265
  - Add LaguerreFilter PR #267
  - Doc maintenance (also PR #266)
  - Add ParabolicSAR
  - Add InfluxDB Data feed (PR #257) and Import Tool (#PR268)
  - Add auto-pytz code from IBData to generic feeds to allow passing strings
    Address #262
  - Add support for packages and frompackages
  - Finish import of new sizers
  - Fix #263 - Refresh resample-tickdata to specify timeframe
  - Store module name and not module in talib autogenerated wrapper class
  - pyfolio api change note

**********
  - Correct csvgeneric import

**********
  - Set eos time from param.sessionend in csv timeframes
  - Improve support for timeframe/compression in btrun
  - Add ApplyN indicator (and base for it and variants BaseApplyN)
  - Add PercentSizer and AllInSizer
  - Add DV2 Indicator
  - Add PercentRank Indicator

**********
  - Patch CST timezone name to CST6CDT
  - Support automatic argument wrapping as line objects in CrossOver
  - Initialize attributes before rejection can happen in OandaData
  - Stop considering clones to decide if live feeds have to wait or not to
    avoid cpu hogging
  - Use _mindatas to decide how many from the parent datas to pass if none
    is specified by the user
  - Some doc corrections

**********
  - Adapt order_target_value to short cash semantics in broker
  - Several refinements to resampling to deliver synchronized bars on end of
    session
  - Add exceptions and strategy skip exception
  - IBData - Deactivate code for faster downloads during absence of live data
    to avoid breaking reconnection code
  - Allow selective order based skipping of coc

**********
  - Close #244 by giving feeds the chance to finish initialization by
    themselves, ensuring proper initialization and allowing early data
    download (merged and refactored PR #245)
  - Add support for live data detection and dynamic queue check
    timeouts to avoid pausing on historical traversal when other feeds
    are live
  - Add PR #242 DrawDown length observer
  - Assimilate PR #240 into cash asset
  - Fixes #239 by providing empty values if the data or indicator has
    not produced a value yet
  - New DrawDown Analyzer and refactoring of DrawDown observer
  - Closes #235 by updating PivotPoint Family to make plotting work under new
    sync scheme and automate self-coupling
  - Some usual documentation updates / typo corrections
  - Minor corrections/improvements
  - Address #243 by sorting (timeframe, compression) data feeds internally to
    avoid forcing users to pass smaller timeframes first
  - Add end-of-session calculation, including adding end-of-session to daily
    data from IB


**********
  - Complete TimeFrameAnalyzerBase with a call to _nextstart and children
  - Improve 1st comparison point of benchmark
  - Documentation updates / samples clean-up
  - IBData feed timezone and backfill gap corrections
  - Initial support for CFD products (untested) to request BID and not TRADES
  - Ensure initialization of backfill_from data feeds

**********
  - Benchmark observer will observer after the strategy has reached its
    minimum period
  - Refactoring of TimeFrameAnalyzerBase
  - Ensure NoTimeFrame name is always returned rightly
  - Documentation updates
  - btrun will only load data feeds if they can be imported

**********
  - Improve unleveraged value by not unleveraging profit and loss
  - Doc edits from PR #223, #224
  - Correct refactoring leftover for backfill_from for IBData and OandaData
  - Extend btfd sample with logs
  - Add ZeroDivisionError to SharpeRatio
  - Add automargin to commission info schemes

1.9.21.105
  - Closes #230 by closing the pool on completion rather than waiting for
    garbage collection
  - Default to show unleveraged value and allow retrieval of leveraged
    value
  - Update btfd sample to updated leveraged value
  - Improve order value reporting with leverage
  - Correct dataseries TimeFrame name presentation in writers
  - Doc updates

**********
  - Added pair-trading sample from @remroc: PR #223, #224, #225
  - Some documentation updates
  - Leverage support
  - Closes #227 numfigs type=int in arg parsing
  - Correct no-plotting of datas
  - Correct pandasdata integer addressing issue
  - Correct time comparison when running with runonce=True
  - Update SessionFiller to more stringent standards in modern versions

**********
  - Add time comparison for single line operations
  - Correct plotting error calculations with volume and improve data on data
  - Remove cosmetic comma

**********
  - PR #221 Correct onda candleFormat parameter
  - Allow data on data plotting and no data plotting
  - Remove double labeling on indicators
  - Analyzer LogReturnsRolling
  - Observer LogReturns
  - Improved order management of input for validity
  - Set default end date for online downloads in Yahoo if not set
  - Gold vs SP500 Sample

**********
  - PR #195 make runstrats iterable to allow callbacks
  - Fixes #189 by adding callback during optimization
  - Fixes #205 to avoid errors during unnamed argument usage in strategy
  - Regression correction for no short-cashing

**********:
  - PR #212 added Vortex indicator
  - Closed #215 writer opens file in binary mode
  - Closed #210 missing comma in status definitions lists in feed
  - PR #203 python3 compatiblity for ib (long)
  - Added shortcash parameter to broker to control cash increase/decrease

1.9.15.104:
  - PR #202 to fix import in ibdata
  - PR #196, #198 - doc updates
  - PR #199 delegate notifications in Chainer Data Feed
  - PercentChange indicator request from #192
  - %B BollingerBands from #190
  - Check bar time before market type execution #190

1.9.14.102:
  - Pull Request #187 to improve SQN and test
  - Update some samples
  - Refactor new KST - Closes #183
  - Closes #163 adding interest as commission to correct calculate PNL
  - Improve SignalStrategy overriden methods to avoid impacting user subclasses
  - Closes #168 - Fetching open orders
  - #173 short-circuit calculation sqn in case of no trades
  - strategy selection sample

1.9.13.102:
  - Closes #179 Ichimoku indicator
  - Plotting allows now filling areas and showing the indicator name even with
    plotlinelabels active
  - Use _minperiod in linebuffer.qbuffer for maxlen rather than default 1
  - Closes #169 - Correct DaySteps filter
  - Add ROC100 indicator
  - Add KnowSureThing indicator

1.9.12.99:
  - Improve cheat-on-close to provide exact match price even during replay
  - Allow offsetting resampling bar set by timeframe/compression units

1.9.11.99:
  - Separate resampling from replaying for synchronization purposes
  - Modernize sample to better check #169

1.9.10.99
  - Further use cases coverage for new synchronizatio method and
    resample/replay
  - PR #173 - SharpeRatio returns None if it cannot be calculated
  - PR #173 - SQN returns 0 (instead of raising exception) if no trades have
    been made
  - Cover replay case for cheat-on-close
  - Extra analyzers in VWR Sample and modernized PivotPoint sample
  - Reworked of plotting for datas of different length by matching date indexes
  - Removed old mlen accounting for plotting different timeframes
  - #172 cover extra unwinding of linebuffer and add extra size to qbuffer

1.9.9.99
  - Correct RSI_EMA, RSI_SMA subclassing
  - Add cheat-on-close to the broker
  - Correct own operation bug directly on lines (was fine on line
    actions/operations)
  - Add support for __neg__ operator (-) to lines
  - Adresses #170 by forcing a bool as return
  - Extend signal trigger detection to inverse and any values
  - Support for embedding in a line non-line types
  - Closes #171. Make safepow the default
  - Use DataTrades only if several datas are in place

1.9.8.99
  - Workaround IbPy not converting bytes by passing strings in Python3
  - safepow parameter for StandardDeviation

********
  - Closes #156 by adding LinePlotterIndicator
  - Closes #154 by providing hollow candlesticks
  - Ensure unique name for analyzers to get all printed out by writers
  - Fix installation instructions for plotting

********:
  - Allow defining the datetime format string for the x axis and data points.
    Closes #148
  - Rework plotting to account for datas with different lengths and work with
    auto locators/formatters
  - Improve signals to handle multiple datas and wrap LineIterators (Indicators)
  - Use excess returns for the standard deviation in Sharpe Ratio

********
  - Improve data synchronization behavior
  - Make new DataTrades synchronize to strategy
  - Correct TimeFrameAnalyzerBase to synchronize with strategy

********
  - Add DataTrades Observer to plot the trades of multiple datas independently
  - Make this observer the default in cerebro (old behavior via oldtrades=True)

********:
  - copyas method in data feeds to let a clone data be seen differently in the
    broker
  - Count trades on strategy basis and not main data basis
  - Add RQAlpha link
  - Fixes #153 by closing file descriptors after preloading

********:
  - Correct plotting for multi strategy approach
  - Make Crossover plot like any other indicator

********:
  - Automatic inline plotting if running inside a notebook
  - Correct new plotting code for Python 3

*********:
  - README Updates
  - Improvements to generic Store management and VChartfile
  - Addresses time underflow/overflow in #143

*********:
  - Set annualization factor for days to 252 in SharpeRatio to match the value
    most used in the literature
  - Add Returns analyzer
  - Closes #137 Added VWR (VariabilityWeightedReturn) analyzer
  - Fixes #141. optreturn must only be applied when optimizing
  - Correct getting default value for ptfimeframe in pyfolio2 sample. Fixes #142

*********:
  - Rework SharpeRatio, add annualization and add SharpeRatio_A with default
    annualization
  - Improve data / results message passing during optimization
  - Some documentation improvements/corrections

*********:
  - Add rounding control to YahooFinanceCSVData and update docs. Closes #138
  - Sharpe Ratio external testing sample. Addresses #137
  - order_target_api, sample and cos. Closes #134

*********:
  - Added Any, All, Reduce, function replacements
  - Added AnyN, AllN, ReduceN indicators
  - Aliased Highest -> MaxN, Lowest -> MinN
  - Added VChartfile Store and Feed improving over existing feed
    implementing the store pattern and fetching the basepath location
    from the registry if possible
  - Some docs improvements/corrections
  - Add a generic Store to let stores subclass
  - Add a Chainer, RollOver data feed and sample
  - Add shortcuts for some subpackages: indicators -> ind, observers -> obs
    strategies -> strats, commissions -> comms
  - Add framework for analyzer testing and tests for 2 analyzers: SQN,
    TimeReturn

********:
  - Finalize Oanda integration
  - Allow simulated orders (meant to fetch initial positions from live brokers)

********:
  - Add support for credit interest rate (#125), with update of docs, sample,
    support in broker and btrun
  - urlencode tickers for yahoo downloads (feed and tool)

********:
  - Added indicators (3): Hull MA, ZeroLag Indicator, Dickson MA
  - Added control of object cache to cerebro (default deactivated)
  - Refactored the support for "next" only indicators
  - Typos and Docs updates (also from pull-requests)

********:
  - Refactor bt.signals to bt.signal (keeping compatibility for prev uses)
  - Improve writer to write non-string lists and fetch headers after anylzers
  - Add base bt.Signal strategy class for easier subclassing
  - Update btrun to support signals/slippage/flushing, update feeds and minors
  - Correct writer collections of analyzers parameters
  - Correct reverse overloaded operations in stage2
  - Some docs/docstrings corrections

********:
  - Slippage implementation in broker, documented and with sample
  - Refactoring/File Reordering of broker and volume fillers
  - Documentation updates/corrections/cleanup
  - Merge #120

********:
  - Filters documentation and reference
  - Add pinkfish ohl + o filter
  - Some filter refactoring
  - README Updates

********:
  - Refactoring of pyfolio and children analyzers following #116 to try to
    support future intraday support in *pyfolio*
  - Allow adding a specific signal strategy subclass to cerebro
  - Refactor SignalStrategy to ease up subclassing

********:
  - #106 Oanda Data Feed
  - Adding _dataname to always be able to identify a data by symbol, including
    *resampled/replayed*
  - Address #115 resampling of same ibdata which was losing timezone information
    in cloning
  - Display raw datetime information in ibtest. For same data resample topic
    in #115

********:
  - Addresses #115 - improvement in ib multiple data handling
  - Improvements in vcdata multiple data handling

********:
  - Added signals api
  - Correct value calculation for shorted stocks
  - Add a symbolic margin to commissioninfo if not specified
  - Remove line amonst marker in Trades observers

********:
  - Added getsize to CommissionInfo API to allow, for example, a sizer to
    calculate the size of a trade using percentages
  - Add __btversion__ which is a tuple of ints for easy version comparison
  - Add macd-settings sample

********:
  - Pinkfish challange sample
  - Add stash to feeds to allow filtered output to be resent to filters
  - Restore deprecated setsizing method in FixedSize sizer for old quickstart
    guide
  - Rework quickstart tutorial and samples to use addsizer and deprecate
    setsizing
  - Allow BuySell observer to plot above / below high / low for clarity,
    especially when plotting ohlc/candles bars
  - Add support for observer orders during replay
  - Improve Close order execution logic
  - Fix microsecond precision errors in end of session calculations in order
    and feed
  - Docstrings cosmetic changes

********:
  - Changes to support separate auto-documentation for a branch of an object
    hierarchy
  - ta-lib integration: Closes #53
  - ta-lib documentation
  - Improve sizers internal interface by having a strategy attribute, which
    can be used before resorting to the broker
  - observer and benchmarking documentation update

********:
  - Reworked and published sizers interface (addresses #104) with changes
    in cerebro and Strategy
  - Observers documentation
  - Refactor timereturn analyzer logic for better readability

********:
  - Correct lastvalue update in TimeReturn
  - Closes #111 by annualizing the returns if the rate is not downgraded

********:
  - Closes #89 by adding benchmarking to TimeReturn and new observers
    TimeReturn/Benchmark (sample included)
  - Analyzers can be embedded in observers to share functionality
  - Added TimeFrame.NoTimeFrame
  - ibpy imported in readthedocs for IBStore/IBBroker/IBData doc generation


********:
  - Closes #108 - Plotting documentation
  - Some updates to analyzer docs
  - Further refactoring/improvements/corrections to the analyzers

********:
  - Pyfolio integration
  - Refactoring/reorganization of analyzers

********:
  - Correct filler implementation in the broker to consider order side for the
    value returned from a filler
  - Extend volumefilling sample to cover sell and repetition scenarios

1.5.2.93
  - Added support for volume filling strategies in the broker
  - Added 3 volume fillers: FixedSize, FixedBarPerc, BarPointPerc
  - Added broker and fillers to the docs
  - Added TimeReturn to the Analyzers reference
  - Added DaySteps filter and sample to downsample a day bar in open + rest

1.5.1.93
  - UltimateOscillator added. Requested with ticket #103
  - VisualChart Live Data Feed/Trading integration
  - Add YahooFinanceData (online) to formats supported by btrun

1.5.0.92
  - InteractiveBroker Live Data Feed/Broker
  - Rework of many internals to support live feeds
  - DateTime Management (timezones) support added
  - Extra Rework of Resampler/Replayer to support live feeds and earliest
    possible bar delivery

1.3.3.92
  - Fixes #99 by conditionally importing ib modules

1.3.2.92
  - safediv added to Stochastic from Pull Request #97
  - Initial integration fo ib feed/broker. Can operate but it is not yet fully
    ocmplete
  - Comprehensive ib testing sample
  - Added "store" and "data" notifications to cerebro and strategy for the
    integration of live feeds
  - Internal datetime clarifications
  - Fixes #94 removing leftover decode('utf-8') after removing 'b' from 'rb'
    when opening csv files
  - Fix bug in strategy.close and add plimit support to it
  - Some documentation updates

********
  - Memory saving schemes (exactbars parameter to cerebro) full implemented
  - Add mixing-timeframes to the docs
  - Add memory-savings to the docs
  - Cosmetic corrections to data-resampling sample

********
  - Address #84 #86 by implemting a LinesCoupler lines object which fills longer
    timeframe lines with shorter timeframes
  - Add sample for LinesCoupler
  - New links for readthedocs io domain
  - Detection Improvement for objects supported by writers

********
  - Add 3 new indicators (from #81): PivotPoint, FibonacciPivotPoint,
    DemarkPivotPoint
  - Add new function CmpEx
  - Change plotinit to the intial stages of plotting
  - Add plotinfo information to any LineSeries objects
  - Implement LineActions Cache
  - Implement Indicator Cache
  - Cover resampling across midnight border #81
  - Correct error in docs (concepts) #82
  - Addresses #82 by only advancing indicators in runonce mode if the clock
    has overtaken it
  - Addresses #82 by having LinesOperations define and internal clock which
    may not be the owner

********
  - WeekDaysFiller sample for #76
  - Implement new memory saving schemes. Addresses #74
  - Additions/Refactoring to the intenal api offered to filters and internal
    utils objects and removal of leftover prints
  - Refactoring of replay/resample filters
  - Some testing refactoring
  - Support for cross-plotting across datas of different timeframes
  - PivotPoint sample for #81

********
  - Correct resampling/replaying behavior for calculating the delivery with
    configured compression fator for timeframes ticks and days or larger.
    Addresses #47, #77, #78
  - Adapt resample/replay tests to improved resampling/replaying code which
    delivers the bar 1 tick earlier
  - Sample for bidask data to OHLC. Closes #78

********
  - Fix broken data-multitimeframe sample
  - Address #72 by improving _getsizing method which not also takes data as
    parameter
  - Fixes #77 by correctly calculating when the current session ends for the
    last bar (when next session data is already in)

********
  - Fixes #67 by having the Buy Sell Observer be displayed for all datas in the
    system
  - Improve support of live data feed resampling/replaying. Addresses comments
    in #69 and #44
  - Support safe division by zero RSI calculations. Closes #68
  - Fixes #71. Single Lines (LineOperations in this case) don't get added to
    the indicator mix for writers

********
  - Improved detection in cerebro.resampledata of existing datas before cloning
  - Added detection in cerebro.replaydata of existing datas before cloning

********:
  - Add samples following 'Close' order corrections/improvements for
    testing. Addresses #62
  - Improve 'Close' execution support and correct conflicting behavior
    with method checksubmit. Addresses #62
  - Correct method close of strategy by using kwargs which was not
    taking into account the existence of a plimit parameter in methods
    buy/sell and would pass the execution type as plimit
  - PandasData extension sample and data supporting discussion in
    ticket #65
  - If datas have been passed to cerebro, ensure cerebro has a
    strategy to run against (which can get indicators, analyzers,
    observers and other through the appropriate interface)
  - Addresses #64 by auto-cloning datas in resampledata if the data
    was already in the system
  - Return a list in case cerebro.run is not run due to missing datas

********:
  - Update of bidask sample
  - SessionFiller correction to avoid moving the evaluated bar too early into
    the stack and avoid the previous session to fill into new session

********:
  - Remove old DataReplayer/DataResampler and cerebro resampledata_old and
    replaydata_old which were using them
  - Adapt docs and test to remove DataReplayer/DataResampler and document the
    newer interfaces
  - Add ``linesoverride`` parameter to enable redefining the lines of an object
    at any stage. Allows removing OHLC default support
  - Generalized GenericCSV to use the defined line aliases
  - Generalized tick assignment to use the defined line aliases

*********
  - Closes #61 by checking datamaster against None to prevent operator
    overloading to evaluate the object as False because line 0, contains a value
    of 0 at index 0

1.1.26.88
  - Closes #49 by setting the matplotlib backend to "tkagg" to avoid using other
    non-tested backends
  - io.StringIO instead of internally imported one from py3
  - CSVDataBase unicode/bytes unification and also for YahooFinanceData feed
  - yahoodownlaod tool bytes/unicode clarification and urlopen bug correction

*********
  - Fixes #55 and improves management of CSV subclasses opening a file from
    other sources
  - Sample which tests yahoo online downloading

*********
  - Fixes #51 - a trade may reopen a position but close a trade if overlapping
    (different tradeid) trades are active
  - Address Pull Request #52 by adding Py 2/3 MAXINT compatible "constant" which
    is imported into TradeAnalyzer and used instead of sys.maxint
  - Fixes #50 by correcting open/popen typo in StopLimit order

*********
  - Fixes #46 by adding a default of total.total = 0 to indicate that no trades
    were executed and therefore no statistics
  - Fixes #46 by adding a default of total.total = 0 to indicate that no trades
    were executed and therefore no statistics
  - CalendarDays filter implementation and added sample
  - Removed gitter from README

*********
  - Filters moved to submodule filters
  - Full docstring update for CommInfoBase
  - Small improvements to internal AutoDict/AutoOrdereDict
  - Implementation of Trade history log (#40)
  - Added __bool__, __nonzero__ to Position for position testing
  - Orders support miscellaneous information from end-users (#42)
  - Trades get unique identifier and datetime for opening/closing time (#42, #43)
  - Corrected typo in iteritems (#38)

1.1.21.88
  - Addition of keys, values, items to py2/3 compatibility layer
  - Add getdatanames to strategy
  - Strategy.buy/sell/close take data or name as key for operation
  - Close #37 pannotated typo in "atclose" order type in broker
  - Close #35 adding getpositionbyname, getpositionsbyname, getpositions and the
    associated properties without "get"

1.1.20.88
  - #33 correction of typo added during correction of #33
  - Added getdatabyname and string_types check in buy/sell/close to retrieve
    datas in Strategy

1.1.19.88
  - Fixes #33 by properly adjusting the cash for existing open futures (added
    long comment to explain the logic)
  - TimeReturn analyzer added. Can calculate returns for all timeframes
  - SharpeRatio updated to use TimeReturn including automatic adjustment of
    the (annual) riskfreerate for timeframes days, weeks, months. It can still
    use the legacy AnnualReturn analyzer
  - CommInfoBase added as root of all commission schemes to make commission
    schemes more flexible by not tying margin to commission type deduction
  - Added 4 CommInfoBase derived classes with standard commission schemes
  - Extended broker.setcommission call with parameters to work with the new
    CommInfoBase
  - Implemented the legacy CommissionInfo as a subclass of CommInfoBase, fully
    retaining the existing behavior
  - Some in-code documentation updates

1.1.18.88
  - Fixes #31 - Packaging issue under Python 3.x introduced in 1.1.17.88

1.1.17.88
  - #29 extend commissions to support additional schemes
  - #27 convert iterable in pandas datasource to list before checking len
  - Packaging reordering to suppor introduction of dependencies

1.1.16.88
  - Correct missing super in start some Data Feeds. Closes #27

1.1.15.88
  - DivByZero function included to perform division without triggering
    exceptions
  - SessionFiller completed as data filter
  - Corrections to WriterStringIO
  - Final renaming of data filter API
  - Reset of operators to stage1 to be able to run over same data again withoug
    re-init
  - Update data-replay/resample samples to use new filter API
  - Rework of testcaes to use new filter API and run all combinations of
    runonce/preload

*********
  - Comminfo passed down to trades for multitrade profit and loss calculation
    for issue #226
  - Addition of filters/processors (naming not final) to data sources
  - (Re)Implementation of Resampling/Replaying as Processors - Old
    Implementation still available
  - Changed X axis formatting for Weeks/Months/Years
  - DataFilter/DataFiller implemented as DataSources and also as
    Filters/Processors
  - DataFilter/DataFiller sample
  - Time management improvement to address precision issues when isolating time
    from coded datetime with new functions in LineBuffer

*********
  - Further refactoring of resampling (keeping previous parameter names
    compatible) killing corner case for last bar still having the sub-bar
    timestamp - Close #25
  - Added sessionstart parameter to DataBase to complement sessionend
  - Some module import refactoring to refer to main module
  - Added DataFilter class
  - Close #24 by enabling writer to handle Analyzer dictionaries which carry
    non-string as keys
  - Correct/enhance some of the samples

*********:
  - Refactoring of minute/seconds/microseconds bar compression scheme to allow
    time adjusted bars
  - Added tick_last to datas - alias of tick_close
  - resampledata and replaydata methods added to cerebro
  - Added tick_last to datas - alias of tick_close
  - Added multitrade support and sample
  - Added helper time2num and num2time to complement date2num and num2date
  - RelativeVolumeByBar Sample
  - Corrected fromdate being set at the end of session
  - Refactor some data feeds to use iterators and discard itertools.count
  - Add dm/tm methods to LineBuffer to get numeric parts (int/fraction) of
    numeric datetime representation
  - Added sample datas with volume
  - Corrected _orlogic for "Or" function and bool'ized And and Or
  - Refactored starting points in running strategies
  - Added queue/Queue to py3 compatibility
  - Further rework of minute (and sub-minute) Data Resampling/Replaying
  - Added tia/visualize-wealth/QSTK/TradingWithPython to README
  - Added tick_last to set of tick variables (open/high/low/close)
  - Added resampledata and replaydata to cerebro to avoid having to instantiate
    DataReplayer/DataResampler

*********:
  - Added TimeFrame for Ticks, MicroSeconds and Seconds
  - Plot support for new Ticks, MicroSeconds and Seconds TimeFrames
  - Removed flushing of sys.stdout on Win32 platforms to avoid interactions with
    ipython (fixes #20)
  - Reworked Resampling for TimeFrame Minutes (closes #19) and added Resampling
    for Seconds, MicroSeconds and TickData
  - Sample of plot-on-same-axis added
  - Added pypy/pypy3 tests to Travis and added to to documentation
  - Added sample which resamples tickdata

*********:
  - Small documentation updates
  - Indicators can be plotted on/over other indicators
  - Sample of plot-on-same-axis added

********:
  - Doc/Readme additions for 3.5
  - Removed dangling py3 in writer from six transition
  - Added writer testcase

********:
  - Added Python 3.5 to Travis CI
  - Removed 2.6 and added 3.5 from setup.py
  - Refactored bt-run.py to internal function and added btrun executable to
    installation
  - Added cerebro parameters and writers support to btrun
  - Fixed duplicate writers next call in "next" mode
  - Improved LineSeries objects name printing in WriterFile and changed "csv"
    to False
  - Correct sign of "closed" if a long/short position if a position is reduced:
    closes #18
  - Removed six dependency through small internal Py2/Py3 module and updated
    docs and setup.py
  - Removed nose-exclude from test requirements
  - Implement current order status in broker
  - 0 can be passed as number of maxcpus for optimization (same as None)
  - SQN and TradeAnalyzer documented

1.1.7.88:
  - Drop Python 2.6 support (also removing internal OrderedDict) after adding
    nexbars which needs collections.deque with maxlen (>= 2.7)
  - First Writer Implemenatation for CSV Output
  - TradeAnalyzer implementation
  - SystemQualityNumber (SQN) implementation

1.1.6.88:
  - Broker reworked to check margin/cost limits on order submission/execution
  - Broker fix to avoid having the wrong sign on short "Trades"
  - Rework Trades commission deduction
  - Additions to Position, Order to support broker new checks
  - Add missing analyzers loop call to "_next"
  - Observers loop handled in Strategy now (only object holding them)
  - Observers reachable in strategy via new alias "observers" (in addition to
    "stats")
  - Cosmetic changes to analyer pprint
  - Correction to Position.__len__ to work with negative sizes (short positions)
  - Crossover defaults to true for plotting just like any other indicator
  - "Exactbars" mode added which limits the amounts of bars to those needed by
    each indicator. Disables runonce, preloading and plotting. It uses a
    ringbuffer method
  - Documentation/Samples directory (and hence doc fixes) rework
  - Documentationn rework for direct execution of scripts against sample datas
    #16
  - Multiple Data Strategy added as Sample
  - Automatic import of flushfile
  - Added LineForward as complement to LineDelay
  - Correct double call to Analyzer._next
  - Cover case in which a line from a data is directly assigned, avoiding the
    binding to kick-in too early
  - Correction in Accum indicator (typo line -> lines) and super addition to
    WilliamsAD

********:
  - Added reversion to stage1 operator behavior when the strategy backtesting is
    over
  - Refactoring of minimum period calculation in LineIterator
  - Refactoring of strategy minimum period calculation to allow indicator
    injection
  - Cerebro support for addition of indictors to inject into strategies
  - bt-run rework to support multiple strategies (o none), observers, indicators
    and analyzers with individual kwargs per entry
  - bt-run rework of plotting to single argument with kwargs
  - Corrected ill behavior when separatin multiple line objects passed as single
    argument to an indicator which lead to multi-owner management for the 2nd
    line and posterior
  - Analyzer defines stubs for print pprint and get_analysis
  - Addion of LineDelay opposite: LineForward to support positive (look/write
    backwards) arguments in the line(period) notation
  - Added datas and data alias in Analyzers

********:
  - Thorough documentation rework
  - Corner case for multiple timeframe datas when the larger timeframe doesn't
    contribute to minimum period with indicators
  - Correction of data resampling which affected same timeframe (which is valid
    because compression can be different)
  - Built-In Strategies auto-documentation added
  - Blaze data support and Pandas Datafeed with only numeric indices support
  - bt-run accepts kwargs per loaded object (strategy, observer, analyzer) and
    can load the default Strategy object if none is specified

********:
  - Automation bt-run.py script added
  - Pandas Dataframe support
  - Improvements to OrderedDict imports for Python 2.6 compatibility
  - Default reference price for orders is bar closing price if not set like in
    Market orders
  - Analyzers added: non-lines objects offering in-run/post-run statistics
  - Analyzers added: SharpeRatio and AnnualReturn
  - Improved Observers which now support (like Indicators/Strategies)
    prenext/nextstart
  - Simplified cerebro return values for run: single list if not optimizing and
    list of lists if optimizing
  - Order Execution Sample script added
  - SMA_CrosssOver Strategy included in submodule backtrader.strategies

********:
  - Generic Data Feed Development Documentation
  - Observers Documentation
  - Support for last tick values in data feeds (data.tick_xxx with xxx being,
    open, high, low, close, volume, openinterest. Unless a real-time feed is
    used or a replay is done, the values will be those of the regular bar
  - Replayer support filling up the last used tick_xxx values
  - Orders have new attribute with the next end of session after the order
  - Broker uses the tick prices for order execution supporting with it the same
    logic in replay and regular mode
  - Fixes #11: On Market Close Orders new logic including end of session check
    support
  - VisualChart binary file direct support

********:
  - Quickstart documentation update to use Trades
  - Issue #3 setcash before the run corrected
  - Addition of GenericCSVData (following #6)
  - Documentation on DataFeeds
  - SierraChartCSVData added
  - Documentation on DataFeed development
  - #8 to address valid for order limited in time
  - Improved to order creation (via buy/sell) from the strategy
  - Corrected plimit typo in order execution
  - Corrected redefinition of enum for order execution types Stop/StopLimit
  - Order cloning and unique id per order to allow same order notified
    twice in same interval with different events
  - Added missing notification for order.accept
  - Broker refactoring on BuyOrder detection and price naming for limit
  - Documentation on order creation and execution

********:
  - Added Gitter stuff to README.rst
  - Documentation updates
  - Moved operations calculations to strategy with extra P&L information from
    the broker (with an updated CommissionInfo profitandloss method) and
    simplified Operations observer along the way
  - Removal of the analyzer paradigm, refactoring the introduction of observers,
    which now can be done through Cerebro to make them really usable as
    statistics generators. Default observers get added from Cerebro unless
    explicitly indicatoed not to do so
  - notify renamed to notify_order (patch support included)
  - notify_operation renamed to notify_trade
  - All "Operation" references changed to "Trade"
  - Minor version bump due to the "Operation" and "Observer" refactoring
  - Addition of a drawdown observer

1.0.10.88:
  - Further corrections for more "unpickable" cases

********:
  - Multicore support for optimization
  - Corrected quickstart samples to change Yahoo "reversed" to "reverse" and
    change the value from True to False
  - Changes needed to support pickling: adding dynamic classes to modules,
    assigning unique names to dynamic classes, not keeping instance methods in
    variables and removing lambda definitions for functions defined at module
    level
  - Changes to testcommon and test_strategy_optimized to avoid nosetests errors
    with multiprocessing

********:
  - Correction to yahoodownload from landscape.io check when exception is raised
  - alias plotname assignment done before the alias variable is overwritten to
    avoid plotname from just being the 2nd letter of the alias
  - Added incminperiod to increase minperiods with non further calculations
  - Notation relaxation: indicators may not indicate on which data they operate
    and the data of the owner will be used automatically
  - zlema now calles super on init
  - Cosmetic corrections to moving averages to not use aliased names
  - Corner minimum period calculation case covered in
    ExponentialSmoothingDynamic in which a passed line as a parameter is not being
    considered in any calculation because there is no line assignment in the
    indicator
  - Corrections to FeedBase to avoid passing "dataname" twice
  - Added a crosshairs cursor to the charts using modified MultiCursor from
    matplotlib (submitted to Matplotlib)
  - Moving Average Refactoring into separated files
  - Indicators (88): Trix/TrixSignal (w doc/test)

1.0.7.86
  - Import Indicator and functions into the indicators package to enable
    indicators to do a "from ." import
  - Improvements to class alias definition
  - Indicators (74): basicops receives Average, WeightedAverage, ExpSmoothing,
    ExpSmoothingDynamic
  - Indicator (75): ZLEMA with tests and documentation
  - Refactored MovingAverage placeholder and MovingAverages to use basic
    operations and autoregister in the placeholder
  - Refactored DEMA, TEMA, ZLEMA to subclasses of MovingAverageBase for
    autoregistrattion
  - Refactored envelope to automatically create envelopes from all
    auto-registered MovingAverages
  - Refactored oscillator to automatically create envelopes from all
    auto-registered MovingAverages
  - Indicators (77): ZLEMAEnvelope, ZLEMAOscillator added
  - Indicators (79): TrueLow, TrueHigh added and TrueRange refactored to use them
  - Indicators (81): UpDayBool, DownDayBool as specialized versions of UpDay and
    DownDay
  - Refactored all indicators to do a relative "." import for Indicator and
    functions
  - Removed docstring code from LineSeries to move it to a sphinx extension
  - Added sphinx etension to automate documentation of indicators
  - Removed previous indicator documentation and added "indautoref" own
    directive for autodocumentation
  - indicators autoregister with Indicator (for things like autodocumentation)
  - Avoid automatically generated Envelope/Oscillator from MovingAverages to
    register to avoid "EnvelopeOscillator" subclasses
  - Indicators receiving only 1 data get the 2nd and later lines as extras (use
    case: a crossover uses line 0 and 1 automatically)
  - Indicators (85): PriceOscillator, PercentagePriceOscillator,
    PercentagePriceOscillatorShort, PrettyGoodOscillator added
  - Indicators (86) - Williams Accumulation/Distribution (WilliamsAD) added

1.0.6.70
  - Correction of bug which prevented lines in different indicators to have the
    same name and different index at the same hierarchy level
  - Added AroonUpDown, AroonOscillator, AroonUp, AroonDown,
    AroonUpDownOscillator (with tests and docs)
  - Added basic indicators FindFirstIndex, FindFirstIndexHighest,
    FindFirstIndexLowest (with test and docs)
  - Added basic indicators FindLastIndex, FindLastIndexHighest,
    FindLastIndexLowest (with test and docs)
  - Documented OperationN (so anyone can subclass it if wished)
  - Removed old MaxN and MinN (same as Highest and Lowest)
  - Made RSI_SMA the class and RSI_Cutler the alias
  - Added support in plot and lineiterator to put plot specific code (like
    dynamically setting plothlines) in a separate method to fully separate
    indicator logic from any plotting logic
  - Fully specified Python versions supported in setup.py and some PEP8 changes
  - Changed test case generation string printing to simplify operations (Python
    3.2 doesn't support 'u')
  - Existing indicators updated to use new plot/indicator code logic separation
  - Improvements to envelope object hierarchy with method to prepare periods
  - Changed (previously unused) behavior of assignment to lines[x],
    allowing establishing line bindings without knowing the alias
  - Subclass OperationN from new PeriodN to allow for subclasses of
    basic PeriodN with no need to define "func"
  - LineSeries objects "lines" can be mixed with objects holding "lines"
    attributes
  - MetaParams objects can be mixed with other objects containing "params"
  - MetaLineSeries support for alias definition and autodocumentation of alias,
    lines, parameters, plotinfo and plotlines
  - Correction to AutoInfoClass._getdefaults to correctly return a list under
    Py3
  - Refactored Moving Averages to be "formulated" objects rather than next/once
    based to allow for easy mixin/subclassing
  - Refactored and simplified envelope indicators
  - Refactored indicators to use alias and semi-autodocumentation facilities
    from LineSeries
  - Indicators (60): DEMA, TEMA (with tests and docs)
  - Indicators (62): DEMAEnvelope, TEMAEnvelope (with tests and docs)
  - Indicators (70): Oscillator, SMAOsc, EMAOsc, SMMAOsc, WMAOsc, DEMAOsc and
    TEMAOsc (with testcases and docs) added (MixIn also documented)
  - Testcase for Envelope added
  - Plot bug correccted which could prevent indicators (on same plot as data) on
    indicators from being plotted
  - Plot support for plotlines properties to be specified as lines

********
  - CCI Plotting labels improved
  - WilliamsR plotname/plotlines names improved
  - Stochastic plotlines names improved
  - Momentum plotting labels improved
  - DirectionalMovement plotting labels improved
  - XXXDeviations plotting labels improved
  - Changes (__hash__ in lineroot and list(xxx.values) when plotting) for Python
    3.4 compatibility
  - test_strategy_optimized import xrange from six for Python 3 and travis.yml
    updated to runn with Python 3.4 too
  - OrderedDict recipe added for Python 2.6 compatibility
  - Continuous integration check under Travis added for 2.6/3.2/3.3
  - Updated Readme and docs about Python compatibility

********
  - Tests for strategy optimized/not optimized added
  - Cosmetic change to "triggered" parameter initialization in StopLimitOrders
  - Test added for "Operation"
  - Test for "Position"
  - All indicators changed to used absolute imports for clarity and possible
    independence
  - Added indicator MeanDeviation (and doc)
  - Added indicator CommodityChannelIndex (CCI) (docs and test)
  - Reordered StdDeviation/MeanDeviation into own module and doc sub-section
  - Plot support for lines having a name different than the class alias (ex:
    plusDI can be plotted as +DI)
  - Update docs badge link to project, add direct link to indicators in docs and
    clarify installation from sources with header
  - Refactoring of UpDays/DownDays to UpDay/DownDay for RSI
  - DirectionalMove Indicators (+tests/docs): DI, +DI, -DI, ADX, ADXR, DMI, DM

********
  - Wikipedia link for DetrendedPriceOscillator
  - Renaming of Stochastic and Williams lines to include "perc" (originally %)
  - Removal of specific plotnames in MovingAverages
  - Williams renamed to WilliamsR for accuracy and line renamed to percR
  - Stochastic lines renamed to percK and percD from kperc and dperc for
    accuracy
  - StochasticFull added (3 lines)
  - CrossOver, CrossUp, CrossDown indicators and documentation
  - Correct broker usage in "close" operation
  - Operations observer plotting style changed to "full"
  - BuySell observer plotting style changed to full and buy color changed to
    lime for visibility
  - Broker correction of initial commission assigment. Introduced error when
    adding support for optimization
  - Added indicators: Envelope, SMAEnvelope, EMAEnvelope, SMMAEnvelope,
    WMAEnvelope, KAMAEnvelope (tests and docs included)
  - Corrected label plotting when a LineSeries object is passed as label
  - Documentation and test for CommissionInfo

1.0.2.26
  - Correction to minperiod calculation to correctly calculate and take into
    account indicator on indicator/single lines minperiods together with
    multi-timeframe datas
  - Extra plotting defaults to lineiterator to simplify plotting code
  - Added plotforce to force plotting of an indicator which relies on
    non-plotted/plottable data/clock sources
  - Plotting support for indicator on indicator respecting above/below order
  - Support plotting indicators which don't have a data/indicator clock by
    looking up the chain
  - Add badges' alternative test and add a badge for the documentation
  - KAMA sets plotname to override inherited one from SimpleMovingAverage
  - Williams %R indicator and test
  - Momentum, RateOfChange, MomentumOscillator and tests

1.0.1.22
  - Reordering and addition of sample datas
  - Addition of samples limited to 2014 and 2006
  - Independent Yahoo Online Download Tool
  - TrueRange formula improvement
  - Changed LineSeries "array" access to property
  - data_0 references changed to more generic data
  - Added AdaptiveMovingAverage
  - AdaptiveMovingAverage added to the docs
  - YahooCSV "reversed" parameter changed to reverse (and inverted default to
    False
  - Changes to make online downloads Py3 compatible
  - Multi-Timeframe datas which are exhausted will return empty bars
  - Improvements in VChartCSVData for name and timeframe recognition
  - Added own simple csv format for sample
  - Reordering/Addition of data samples
  - Addition of nosetest testcases covering indicators, data multi timeframe
    and resampling
  - Travis-ci integration
  - Extra minperiod check in LineIterator postinit hook to account for
    indicators with calculations in __init__ not applied directly to line
    assignments

1.0.0.21
  - First tagged and documented release
