#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
###############################################################################
#
# Copyright (C) 2015-2023 <PERSON>
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
#
###############################################################################
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import collections
import datetime as dt

import backtrader as bt


class TaxLotAnalyzer(bt.Analyzer):
    '''This analyzer provides basic tax lot reporting functionality
    
    It tracks tax lots for each position and provides summary information
    about lot composition, cost basis, and basic analytics.
    
    Designed for European markets - no holding period distinctions.
    
    Params:
    
      - headers (default: ``False``)
        Add headers to the analysis results
        
      - lot_details (default: ``True``)
        Include detailed lot information in the analysis
        
      - summary_only (default: ``False``)
        Only provide summary information, not detailed lot data
        
      - include_zero_positions (default: ``False``)
        Include positions with zero quantity in the analysis
    
    Methods:
    
      - get_analysis
        Returns a dictionary with tax lot information
        
    The analysis results include:
    - Position summaries with lot count and average cost basis
    - Individual lot details (if enabled)
    - Basic analytics (total lots, total value, etc.)
    '''
    
    params = (
        ('headers', False),
        ('lot_details', True),
        ('summary_only', False),
        ('include_zero_positions', False),
    )
    
    def start(self):
        super(TaxLotAnalyzer, self).start()
        
        # Initialize results structure
        self.rets['positions'] = collections.OrderedDict()
        self.rets['summary'] = collections.OrderedDict()
        
        if self.p.headers:
            self.rets['headers'] = {
                'positions': ['symbol', 'total_qty', 'lot_count', 'avg_cost_basis', 'total_value'],
                'lots': ['symbol', 'lot_qty', 'lot_price', 'lot_datetime', 'lot_value']
            }
    
    def stop(self):
        '''Generate final analysis at the end of the strategy'''
        super(TaxLotAnalyzer, self).stop()
        
        # Get final position and lot information
        self._analyze_final_positions()
        
        # Generate summary statistics
        self._generate_summary()
    
    def _analyze_final_positions(self):
        '''Analyze tax lots for all positions at strategy end'''
        
        positions_analysis = collections.OrderedDict()
        
        for i, data in enumerate(self.strategy.datas):
            data_name = data._name or f'Data{i}'
            
            # Get position from strategy
            position = self.strategy.getposition(data)
            
            # Skip zero positions unless explicitly requested
            if not self.p.include_zero_positions and position.size == 0:
                continue
            
            # Get tax lots if available
            tax_lots = []
            if hasattr(position, 'get_taxlots'):
                tax_lots = position.get_taxlots()
            
            # Create position analysis
            position_analysis = {
                'symbol': data_name,
                'total_qty': position.size,
                'lot_count': len(tax_lots),
                'avg_cost_basis': position.price if position.size != 0 else 0.0,
                'total_value': position.size * position.price if position.size != 0 else 0.0,
                'lots': []
            }
            
            # Add detailed lot information if requested
            if self.p.lot_details and not self.p.summary_only:
                for lot in tax_lots:
                    lot_info = {
                        'symbol': data_name,
                        'lot_qty': lot.remaining_qty,
                        'lot_price': lot.price,
                        'lot_datetime': lot.datetime,
                        'lot_value': lot.remaining_qty * lot.price,
                        'original_qty': lot.qty,
                        'days_held': self._calculate_days_held(lot.datetime)
                    }
                    position_analysis['lots'].append(lot_info)
            
            positions_analysis[data_name] = position_analysis
        
        self.rets['positions'] = positions_analysis
    
    def _generate_summary(self):
        '''Generate summary statistics across all positions'''
        
        total_positions = len(self.rets['positions'])
        total_lots = 0
        total_value = 0.0
        total_long_positions = 0
        total_short_positions = 0
        
        for pos_name, pos_data in self.rets['positions'].items():
            total_lots += pos_data['lot_count']
            total_value += pos_data['total_value']
            
            if pos_data['total_qty'] > 0:
                total_long_positions += 1
            elif pos_data['total_qty'] < 0:
                total_short_positions += 1
        
        # Get current backtest datetime (not system time!)
        analysis_dt = None
        try:
            if hasattr(self.strategy, 'datetime') and hasattr(self.strategy.datetime, 'datetime'):
                analysis_dt = self.strategy.datetime.datetime()
            elif len(self.strategy.datas) > 0 and hasattr(self.strategy.datas[0], 'datetime'):
                analysis_dt = self.strategy.datas[0].datetime.datetime()
        except (AttributeError, IndexError):
            analysis_dt = None
        
        self.rets['summary'] = {
            'total_positions': total_positions,
            'total_lots': total_lots,
            'total_value': total_value,
            'long_positions': total_long_positions,
            'short_positions': total_short_positions,
            'zero_positions': total_positions - total_long_positions - total_short_positions,
            'avg_lots_per_position': total_lots / total_positions if total_positions > 0 else 0,
            'analysis_datetime': analysis_dt
        }
    
    def _calculate_days_held(self, lot_datetime):
        '''Calculate days held for a tax lot (European-friendly, no holding period categories)
        
        Uses backtest datetime, NOT system current time - critical for backtesting accuracy!
        '''
        if lot_datetime is None:
            return 0
        
        try:
            # Get current backtest datetime (never use system time!)
            current_dt = None
            if hasattr(self.strategy, 'datetime') and hasattr(self.strategy.datetime, 'datetime'):
                current_dt = self.strategy.datetime.datetime()
            elif len(self.strategy.datas) > 0 and hasattr(self.strategy.datas[0], 'datetime'):
                current_dt = self.strategy.datas[0].datetime.datetime()
            
            if current_dt is None:
                # Cannot determine backtest datetime, return 0 rather than wrong calculation
                return 0
            
            # Convert lot_datetime to datetime if needed
            if hasattr(lot_datetime, 'date'):
                lot_dt = lot_datetime
            else:
                # Assume it's a date object, convert to datetime
                lot_dt = dt.datetime.combine(lot_datetime, dt.time())
            
            # Calculate days held in backtest context
            delta = current_dt - lot_dt
            return max(0, delta.days)  # Ensure non-negative days
            
        except (AttributeError, TypeError, ValueError):
            return 0
    
    def get_lots_summary(self):
        '''Convenience method to get just the lot summary information'''
        return self.rets.get('summary', {})
    
    def get_position_lots(self, data_name=None):
        '''Get tax lot information for a specific position or all positions'''
        if data_name is None:
            return self.rets.get('positions', {})
        else:
            return self.rets.get('positions', {}).get(data_name, {})
    
    def get_total_lot_count(self):
        '''Get total number of tax lots across all positions'''
        return self.rets.get('summary', {}).get('total_lots', 0)
    
    def get_total_position_value(self):
        '''Get total value of all positions'''
        return self.rets.get('summary', {}).get('total_value', 0.0)


__all__ = ['TaxLotAnalyzer'] 