{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d61d91ab-431a-426c-a16c-3695a137a857", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed\n"]}, {"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (fig.ws.readyState == 1 && width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '\" + msg_type + \"' message type: \",\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_\" + msg_type + \"' callback:\",\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "function getModifiers(event) {\n", "    var mods = [];\n", "    if (event.ctrlKey) {\n", "        mods.push('ctrl');\n", "    }\n", "    if (event.altKey) {\n", "        mods.push('alt');\n", "    }\n", "    if (event.shiftKey) {\n", "        mods.push('shift');\n", "    }\n", "    if (event.metaKey) {\n", "        mods.push('meta');\n", "    }\n", "    return mods;\n", "}\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        modifiers: getModifiers(event),\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div id='64f7dcd7-350a-4039-9676-dda13cca7a82'></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[[<Figure size 640x480 with 8 Axes>]]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import backtrader as bt\n", "import yfinance as yf\n", "import pandas as pd\n", "\n", "class TaxLossHarvestingStrategy(bt.Strategy):\n", "    params = (\n", "        ('tax_loss_threshold', -0.05),  # Threshold for tax loss harvesting (e.g., -5%)\n", "        ('rebuy_days', 31),  # Days to wait before rebuying the same stock (wash sale rule)\n", "    )\n", "\n", "    def __init__(self):\n", "        self.rebuy_dict = {}\n", "\n", "    def next(self):\n", "        for stock in self.datas:\n", "            position = self.getposition(stock)\n", "            if position.size:\n", "                # Calculate the percentage loss from the cost basis\n", "                cost_basis = position.price\n", "                current_price = stock.close[0]\n", "                pnl_percent = (current_price - cost_basis) / cost_basis\n", "\n", "                # If loss exceeds the threshold, sell\n", "                if pnl_percent <= self.params.tax_loss_threshold:\n", "                    self.sell(stock)\n", "                    # Log the day we sold\n", "                    self.rebuy_dict[stock._name] = len(self)\n", "\n", "            # Check if we can rebuy a stock\n", "            if stock._name in self.rebuy_dict:\n", "                if len(self) >= self.rebuy_dict[stock._name] + self.params.rebuy_days:\n", "                    self.buy(stock)\n", "                    del self.rebuy_dict[stock._name]\n", "\n", "# Load S&P 500 data (for simplicity, let's use only a few stocks)\n", "tickers = ['AAPL', 'MSFT', 'GOOGL']  # Replace with more tickers as needed\n", "data_dict = {}\n", "for ticker in tickers:\n", "    data = bt.feeds.PandasData(dataname=yf.download(ticker, '2021-01-01', '2023-01-01'))\n", "    data_dict[ticker] = data\n", "\n", "# Initialize cerebro engine\n", "cerebro = bt.<PERSON><PERSON><PERSON>()\n", "cerebro.addstrategy(TaxLossHarvestingStrategy)\n", "\n", "# Add data feeds\n", "for ticker, data in data_dict.items():\n", "    cerebro.adddata(data, name=ticker)\n", "\n", "# Set starting cash\n", "cerebro.broker.setcash(1000000)\n", "\n", "# Set commission (optional)\n", "cerebro.broker.setcommission(commission=0.001)  # 0.1% commission\n", "\n", "# Run the strategy\n", "cerebro.run()\n", "\n", "# Plot the results\n", "cerebro.plot()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "d5a25d0b-556b-4bd9-a612-8dbf34409310", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[[<Figure size 640x480 with 5 Axes>]]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import matplotlib.pyplot as plt\n", "plt.show()\n", "\n", "%matplotlib inline\n", "cerebro.plot(iplot=False, volume=False)"]}, {"cell_type": "code", "execution_count": null, "id": "58bebb5c-0199-46f3-b503-f5e4e7e4e0e6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}