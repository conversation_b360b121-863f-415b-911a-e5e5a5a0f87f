#!/usr/bin/env python3
"""
Realistic MiFID II Compliance Support with Backtrader Tax Lot Tracking

This demo shows what backtrader can ACTUALLY provide for MiFID II compliance:
- Enhanced tax lot tracking with execution metadata
- Post-trade compliance reporting capabilities
- Cost basis tracking for regulatory reporting
- Audit trail maintenance

What this does NOT do (requires external systems):
- Venue selection or smart order routing
- Live venue connectivity
- Real-time best execution decisions
- Order fragmentation across venues

What this DOES provide:
- Enhanced position tracking with regulatory metadata
- Compliance reporting from execution data
- Tax lot tracking for accurate cost basis
- Framework for storing venue execution details
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from backtrader.position import Position, TaxLot
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Optional, List, Dict
from enum import Enum


class VenueType(Enum):
    """MiFID II venue types for compliance tracking"""
    RM = "Regulated Market"
    MTF = "Multilateral Trading Facility"
    OTF = "Organised Trading Facility"
    SI = "Systematic Internaliser"
    DARK_POOL = "Dark Pool"


class LiquidityIndicator(Enum):
    """Liquidity provision indicator"""
    MAKER = "Added Liquidity"
    TAKER = "Removed Liquidity"
    UNKNOWN = "Unknown"


@dataclass
class ExecutionMetadata:
    """Metadata that would come from external execution system"""
    venue: str = "UNKNOWN"
    venue_type: VenueType = VenueType.RM
    order_id: str = ""
    execution_id: str = ""
    liquidity_indicator: LiquidityIndicator = LiquidityIndicator.UNKNOWN
    commission: float = 0.0
    venue_fee: float = 0.0
    regulatory_fee: float = 0.0


class ComplianceTaxLot(TaxLot):
    """Enhanced TaxLot with regulatory compliance metadata"""
    
    def __init__(self, qty: float, price: float, datetime: datetime, 
                 metadata: ExecutionMetadata = None):
        super().__init__(qty, price, datetime)
        self.metadata = metadata or ExecutionMetadata()
    
    def __str__(self):
        return (f"ComplianceTaxLot({self.remaining_qty} @ {self.price:.4f}, "
                f"Venue: {self.metadata.venue})")


class CompliancePosition(Position):
    """Enhanced Position class with compliance tracking"""
    
    def __init__(self):
        super().__init__()
        self.compliance_lots: List[ComplianceTaxLot] = []
    
    def update_with_metadata(self, qty: float, price: float, datetime: datetime,
                           metadata: ExecutionMetadata = None):
        """Update position with execution metadata (from external system)"""
        
        # Create compliance tax lot with metadata
        compliance_lot = ComplianceTaxLot(qty, price, datetime, metadata)
        self.compliance_lots.append(compliance_lot)
        
        # Update standard position tracking
        self.update(qty, price, datetime)
    
    def get_compliance_report(self) -> Dict:
        """Generate compliance report from tracked executions"""
        if not self.compliance_lots:
            return {}
        
        # Aggregate data
        total_commission = sum(lot.metadata.commission for lot in self.compliance_lots)
        total_venue_fees = sum(lot.metadata.venue_fee for lot in self.compliance_lots)
        total_regulatory_fees = sum(lot.metadata.regulatory_fee for lot in self.compliance_lots)
        
        # Venue breakdown
        venues = {}
        for lot in self.compliance_lots:
            venue = lot.metadata.venue
            if venue not in venues:
                venues[venue] = {
                    'venue_type': lot.metadata.venue_type.value,
                    'quantity': 0,
                    'value': 0,
                    'executions': 0,
                    'commission': 0,
                    'fees': 0
                }
            
            venues[venue]['quantity'] += lot.qty
            venues[venue]['value'] += lot.qty * lot.price
            venues[venue]['executions'] += 1
            venues[venue]['commission'] += lot.metadata.commission
            venues[venue]['fees'] += lot.metadata.venue_fee + lot.metadata.regulatory_fee
        
        return {
            'total_quantity': self.size,
            'total_value': sum(lot.qty * lot.price for lot in self.compliance_lots),
            'average_price': self.price,
            'total_commission': total_commission,
            'total_venue_fees': total_venue_fees,
            'total_regulatory_fees': total_regulatory_fees,
            'venue_breakdown': venues,
            'execution_count': len(self.compliance_lots),
            'first_execution': self.compliance_lots[0].datetime if self.compliance_lots else None,
            'last_execution': self.compliance_lots[-1].datetime if self.compliance_lots else None
        }


def demo_realistic_single_venue():
    """Demonstrate realistic single venue execution tracking"""
    
    print("\n" + "=" * 70)
    print("REALISTIC BACKTRADER COMPLIANCE TRACKING")
    print("=" * 70)
    print("What backtrader CAN do: Track executions with compliance metadata")
    print("What backtrader CANNOT do: Venue selection, order routing, live execution")
    
    position = CompliancePosition()
    
    print(f"\nSimulating executions from external broker/EMS:")
    print("-" * 50)
    
    # Realistic scenario: All executions come from one broker
    # (e.g., Interactive Brokers routes to different venues)
    executions = [
        # qty, price, time_offset, venue (from broker's routing), commission, fees
        (500, 50.10, 0, "LSE", 2.50, 0.25),
        (300, 50.05, 30, "LSE", 1.50, 0.15),
        (200, 49.98, 60, "DARK_POOL", 1.00, 0.10),  # Broker routed to dark pool
        (400, 50.02, 120, "LSE", 2.00, 0.20),
    ]
    
    base_time = datetime(2023, 12, 1, 10, 0, 0)
    
    for i, (qty, price, delay, venue, commission, fees) in enumerate(executions):
        exec_time = base_time + timedelta(seconds=delay)
        
        # This metadata would come from broker's execution report
        metadata = ExecutionMetadata(
            venue=venue,
            venue_type=VenueType.DARK_POOL if "DARK" in venue else VenueType.RM,
            order_id=f"ORD_{exec_time.strftime('%Y%m%d_%H%M%S')}",
            execution_id=f"EXE_{i+1:03d}",
            commission=commission,
            venue_fee=fees * 0.6,  # Venue portion
            regulatory_fee=fees * 0.4  # Regulatory portion
        )
        
        position.update_with_metadata(qty, price, exec_time, metadata)
        
        print(f"{exec_time.strftime('%H:%M:%S')}: {qty:,} @ {price:.2f} "
              f"on {venue} (Commission: {commission:.2f})")
    
    # Generate compliance report
    report = position.get_compliance_report()
    
    print(f"\nCOMPLIANCE REPORT:")
    print("=" * 40)
    print(f"Total Position: {report['total_quantity']:,} shares @ {report['average_price']:.4f}")
    print(f"Total Value: {report['total_value']:,.2f}")
    print(f"Period: {report['first_execution'].strftime('%Y-%m-%d %H:%M')} to "
          f"{report['last_execution'].strftime('%H:%M')}")
    
    print(f"\nCOST BREAKDOWN:")
    print("-" * 20)
    print(f"Commission: {report['total_commission']:.2f}")
    print(f"Venue Fees: {report['total_venue_fees']:.2f}")
    print(f"Regulatory Fees: {report['total_regulatory_fees']:.2f}")
    print(f"Total Costs: {report['total_commission'] + report['total_venue_fees'] + report['total_regulatory_fees']:.2f}")
    
    print(f"\nVENUE BREAKDOWN:")
    print("-" * 20)
    for venue, data in report['venue_breakdown'].items():
        pct = (data['quantity'] / report['total_quantity']) * 100
        avg_price = data['value'] / data['quantity']
        print(f"{venue}: {data['quantity']:,} shares ({pct:.1f}%) @ {avg_price:.4f}")
        print(f"  Type: {data['venue_type']}")
        print(f"  Executions: {data['executions']}")
        print(f"  Costs: {data['commission'] + data['fees']:.2f}")


def demo_tax_lot_fifo_compliance():
    """Demonstrate FIFO tax lot compliance for regulatory reporting"""
    
    print(f"\n" + "=" * 70)
    print("TAX LOT FIFO COMPLIANCE")
    print("=" * 70)
    print("Backtrader provides accurate FIFO cost basis for regulatory reporting")
    
    position = CompliancePosition()
    
    # Build position over time
    print(f"\nBuilding position with different cost bases:")
    print("-" * 50)
    
    purchases = [
        (100, 45.00, "2023-11-01", "LSE", 0.50),
        (200, 50.00, "2023-11-15", "LSE", 1.00),
        (150, 48.00, "2023-11-30", "DARK_POOL", 0.75),
    ]
    
    for qty, price, date_str, venue, commission in purchases:
        exec_date = datetime.strptime(date_str, "%Y-%m-%d")
        metadata = ExecutionMetadata(
            venue=venue,
            venue_type=VenueType.DARK_POOL if "DARK" in venue else VenueType.RM,
            commission=commission
        )
        
        position.update_with_metadata(qty, price, exec_date, metadata)
        print(f"{date_str}: BUY {qty:,} @ {price:.2f} on {venue}")
    
    print(f"\nCurrent Position Tax Lots (FIFO order):")
    print("-" * 40)
    for i, lot in enumerate(position.get_taxlots(), 1):
        days_held = (datetime.now() - lot.datetime).days
        print(f"Lot {i}: {lot.remaining_qty:,} @ {lot.price:.2f} "
              f"({days_held} days old)")
    
    # Simulate partial sale
    print(f"\nSimulating SALE of 250 shares at 52.00:")
    print("-" * 40)
    
    # This would use backtrader's built-in FIFO logic
    lots_before = [(lot.remaining_qty, lot.price) for lot in position.get_taxlots()]
    
    # Reduce lots using FIFO
    position.reduce_taxlots_fifo(250)
    
    lots_after = [(lot.remaining_qty, lot.price) for lot in position.get_taxlots() if lot.remaining_qty > 0]
    
    print("FIFO Sale Allocation:")
    sale_price = 52.00
    remaining_to_sell = 250
    total_cost_basis = 0
    total_proceeds = 250 * sale_price
    
    for i, (qty_before, price) in enumerate(lots_before):
        if remaining_to_sell <= 0:
            break
        
        qty_sold = min(remaining_to_sell, qty_before)
        cost_basis = qty_sold * price
        total_cost_basis += cost_basis
        
        print(f"  Lot {i+1}: Sold {qty_sold:,} @ {price:.2f} cost basis")
        remaining_to_sell -= qty_sold
    
    capital_gain = total_proceeds - total_cost_basis
    print(f"\nREGULATORY REPORTING:")
    print(f"Proceeds: {total_proceeds:,.2f}")
    print(f"Cost Basis (FIFO): {total_cost_basis:,.2f}")
    print(f"Capital Gain/Loss: {capital_gain:,.2f}")
    
    print(f"\nRemaining Position Tax Lots:")
    for i, lot in enumerate(position.get_taxlots(), 1):
        if lot.remaining_qty > 0:
            print(f"Lot {i}: {lot.remaining_qty:,} @ {lot.price:.2f}")


def demo_limitations_and_requirements():
    """Clearly explain what backtrader provides vs what's needed externally"""
    
    print(f"\n" + "=" * 70)
    print("BACKTRADER CAPABILITIES vs EXTERNAL REQUIREMENTS")
    print("=" * 70)
    
    print(f"\n✅ WHAT BACKTRADER PROVIDES:")
    print("-" * 40)
    print("• Enhanced tax lot tracking with metadata storage")
    print("• FIFO cost basis calculations")
    print("• Position tracking with execution details")
    print("• Compliance reporting framework")
    print("• Audit trail of all transactions")
    print("• Performance optimized for backtesting")
    
    print(f"\n❌ WHAT REQUIRES EXTERNAL SYSTEMS:")
    print("-" * 40)
    print("• Live venue connectivity")
    print("• Smart order routing algorithms")
    print("• Real-time venue selection")
    print("• Order fragmentation logic")
    print("• Market data from multiple venues")
    print("• FIX protocol implementations")
    print("• Regulatory submission systems")
    
    print(f"\n🔧 TYPICAL INTEGRATION ARCHITECTURE:")
    print("-" * 40)
    print("1. Execution Management System (EMS) → Routes orders to venues")
    print("2. Order Management System (OMS) → Manages order lifecycle")
    print("3. Backtrader Enhanced Position → Tracks tax lots + metadata")
    print("4. Compliance System → Generates regulatory reports")
    print("5. Risk Management → Pre/post-trade risk checks")
    
    print(f"\n📊 PRACTICAL USAGE:")
    print("-" * 40)
    print("• Use backtrader for accurate position & cost basis tracking")
    print("• Integrate execution metadata from your broker/EMS")
    print("• Generate compliance reports from tracked data")
    print("• Maintain audit trails for regulatory requirements")
    print("• Backtest strategies with realistic cost models")


if __name__ == '__main__':
    demo_realistic_single_venue()
    demo_tax_lot_fifo_compliance() 
    demo_limitations_and_requirements()
    
    print("\n" + "=" * 70)
    print("REALISTIC MIFID II COMPLIANCE SUPPORT COMPLETE")
    print("=" * 70)
    print("\nSUMMARY:")
    print("• Backtrader provides the foundation for compliance tracking")
    print("• Enhanced tax lot system supports regulatory requirements")
    print("• Integration with external execution systems required")
    print("• Focus on what backtrader does best: accurate position tracking")
    print("• Compliance reporting built on solid tax lot foundation") 