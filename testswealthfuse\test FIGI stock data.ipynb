{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8953de8a-261e-4c99-9863-fa361ee61c8a", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "import json\n", "from dotenv import load_dotenv\n", "\n", "# API key for OpenFIGI\n", "load_dotenv()\n", "figi_api_key = os.getenv('FIGI_API_KEY')\n", "\n", "# Headers for the API request, including the API key\n", "HEADERS = {\n", "    'Content-Type': 'application/json',\n", "    'X-OPENFIGI-APIKEY': figi_api_key\n", "}\n", "# Function to query the OpenFIGI API for a specific stock, e.g. AAPL\n", "def get_stock_data(ticker: str, exchange_code: str = \"US\"):\n", "    \"\"\"\n", "    Retrieve complete information for a given stock ticker from OpenFIGI.\n", "    :param ticker: The ticker symbol of the stock (e.g. \"AAPL\")\n", "    :param exchange_code: The exchange code (default is \"US\" for American exchanges)\n", "    :return: A dictionary containing the stock data\n", "    \"\"\"\n", "    # API endpoint\n", "    url = \"https://api.openfigi.com/v3/mapping\"\n", "\n", "    # Data payload for the API request\n", "    data = [{\n", "        \"idType\": \"TICKER\",  # We're querying by Ticker symbol\n", "        \"idValue\": ticker,   # The actual ticker (e.g. \"AAPL\")\n", "        \"exchCode\": exchange_code  # Exchange code, such as \"US\" for NASDAQ/NYSE\n", "    }]\n", "\n", "    # Make the API request\n", "    response = requests.post(url, headers=HEADERS, data=json.dumps(data))\n", "\n", "    # Check if the request was successful\n", "    if response.status_code == 200:\n", "        # Parse and return the response JSON data\n", "        stock_data = response.json()\n", "        return stock_data\n", "    else:\n", "        # Print the error message if the request failed\n", "        print(f\"Error {response.status_code}: {response.text}\")\n", "        return None\n", "def get_ticker_from_openfigi(figi_id, exchange_code: str = \"US\"):\n", "    url = 'https://api.openfigi.com/v3/mapping'\n", "    payload = [{\n", "        \"idType\": \"ID_BB_GLOBAL\",\n", "        \"exchCode\": exchange_code,\n", "        \"idValue\": figi_id\n", "    }]\n", "    \n", "    response = requests.post(url, json=payload, headers=HEADERS)\n", "    \n", "    if response.status_code == 200:\n", "        data = response.json()\n", "        if data and 'data' in data[0]:\n", "            ticker_info = data[0]['data'][0].get('ticker', 'N/A')\n", "            return ticker_info\n", "        else:\n", "            return 'N/A'\n", "        return data    \n", "    else:\n", "        print(f\"Error fetching data for ID {figi_id}: {response.status_code}\")\n", "        return 'Error'\n", "\n", "def get_info_from_openfigi_all(figi_id, id_type ='ID_BB_GLOBAL'):\n", "    url = 'https://api.openfigi.com/v3/mapping'\n", "    payload = [{\n", "        \"idType\": id_type,\n", "        \"idValue\": figi_id\n", "    }]\n", "    \n", "    response = requests.post(url, json=payload, headers=HEADERS)\n", "    \n", "    if response.status_code == 200:\n", "        data = response.json()\n", "        return data\n", "    else:\n", "        print(f\"Error fetching data for ID {figi_id}: {response.status_code}\")\n", "        return 'Error'\n", "\n", "def get_ticker_from_openfigi_all(figi_id):\n", "    url = 'https://api.openfigi.com/v3/mapping'\n", "    payload = [{\n", "        \"idType\": \"ID_BB_GLOBAL\",\n", "        \"idValue\": figi_id\n", "    }]\n", "    \n", "    response = requests.post(url, json=payload, headers=HEADERS)\n", "    \n", "    if response.status_code == 200:\n", "        data = response.json()\n", "        if data and 'data' in data[0]:\n", "            ticker_info = data[0]['data'][0].get('ticker', 'N/A')\n", "            return ticker_info\n", "        else:\n", "            return 'N/A'\n", "    else:\n", "        print(f\"Error fetching data for ID {figi_id}: {response.status_code}\")\n", "        return 'Error'\n", "        \n", "def normalize_ticker(ticker):\n", "    # Replace / with - to normalize tickers\n", "    return ticker.replace('/', '-')"]}, {"cell_type": "code", "execution_count": 4, "id": "4fa9baa8-fe23-4942-8f91-b38bd23b9fa5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'N/A'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["get_ticker_from_openfigi('BBG000DY3DH0')"]}, {"cell_type": "code", "execution_count": 5, "id": "0d8ca68c-2c5d-43de-8748-f6c44e7ae302", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Complete information for the stock:\n", "[\n", "    {\n", "        \"data\": [\n", "            {\n", "                \"figi\": \"BBG000DY28W5\",\n", "                \"name\": \"BIO-RAD LABORATORIES-A\",\n", "                \"ticker\": \"BIO\",\n", "                \"exchCode\": \"US\",\n", "                \"compositeFIGI\": \"BBG000DY28W5\",\n", "                \"securityType\": \"Common Stock\",\n", "                \"marketSector\": \"Equity\",\n", "                \"shareClassFIGI\": \"BBG001S625W4\",\n", "                \"securityType2\": \"Common Stock\",\n", "                \"securityDescription\": \"BIO\"\n", "            }\n", "        ]\n", "    }\n", "]\n"]}], "source": ["# Example usage: retrieve data for AAPL\n", "ticker = \"BIO\"\n", "stock_info = get_stock_data(ticker)\n", "\n", "# Check if stock information was retrieved\n", "if stock_info:\n", "    print(\"Complete information for the stock:\")\n", "    print(json.dumps(stock_info, indent=4))  # Pretty print the JSON data\n", "else:\n", "    print(f\"No data available for {ticker}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c85a85a9-63b7-42d7-a541-b95be40d696f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}