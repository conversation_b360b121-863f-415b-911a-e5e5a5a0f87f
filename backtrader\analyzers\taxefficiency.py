#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import datetime
from collections import defaultdict

from backtrader import Analyzer
from backtrader.utils import AutoDict

__all__ = ['TaxEfficiency']


class TaxEfficiency(Analyzer):
    """
    Tax Efficiency Analyzer - Measures tax drag and trading efficiency
    
    This analyzer calculates the impact of taxes on portfolio performance,
    measuring how efficiently a strategy minimizes tax costs while maximizing
    after-tax returns. Designed for European markets with FIFO accounting.
    
    Key Metrics:
    - Tax drag percentage (performance lost to taxes)
    - Tax cost ratio (taxes as % of portfolio value)
    - Portfolio turnover rate (trading frequency impact)
    - FIFO efficiency analysis
    - Tax-adjusted returns and alpha
    
    European Market Features:
    - No holding period distinctions (no long-term vs short-term)
    - FIFO cost basis accounting (standard European practice)
    - Configurable capital gains tax rates by country
    - No wash sale considerations (US-only concept)
    
    Parameters:
    - capital_gains_tax_rate: Tax rate on capital gains (default: 0.20 = 20%)
    - include_transaction_costs: Include trading costs in efficiency calculation
    - benchmark_strategy: Compare against buy-and-hold baseline
    - european_compliance: Ensure European market compliance (default: True)
    """
    
    params = (
        ('capital_gains_tax_rate', 0.20),  # 20% capital gains tax rate
        ('include_transaction_costs', True),
        ('benchmark_strategy', 'buy_and_hold'),
        ('european_compliance', True),
        ('headers', True),
        ('detailed_analysis', True),
    )
    
    def __init__(self):
        super(TaxEfficiency, self).__init__()
        
        # Validate parameters
        self._validate_parameters()
        
        # Core tracking variables
        self.start_value = 0.0
        self.end_value = 0.0
        self.portfolio_values = []
        self.total_realized_gains = 0.0
        self.total_realized_losses = 0.0
        self.total_unrealized_gains = 0.0
        self.total_trade_value = 0.0
        self.total_trades = 0
        self.total_tax_costs = 0.0
        self.total_transaction_costs = 0.0
        
        # FIFO analysis tracking
        self.fifo_cost_basis_total = 0.0
        self.average_cost_basis_total = 0.0
        self.fifo_advantage = 0.0
        
        # Trading behavior tracking
        self.trade_history = []
        self.position_changes = []
        self.realization_events = []
        
        # Time tracking for proper datetime handling
        self.start_date = None
        self.end_date = None
        self.trading_days = 0
        
        # European compliance validation
        if self.p.european_compliance:
            self._validate_european_compliance()
    
    def _validate_parameters(self):
        """Validate analyzer parameters"""
        # Validate capital gains tax rate
        if self.p.capital_gains_tax_rate < 0.0:
            raise ValueError("Capital gains tax rate cannot be negative")
        if self.p.capital_gains_tax_rate > 1.0:
            raise ValueError("Capital gains tax rate cannot exceed 100%")
    
    def _validate_european_compliance(self):
        """Validate that analyzer configuration complies with European markets"""
        # Ensure no US-specific features are enabled
        if hasattr(self.p, 'holding_period_advantage'):
            raise ValueError("European markets do not have holding period advantages")
        if hasattr(self.p, 'wash_sale_rules'):
            raise ValueError("European markets do not have wash sale rules")
    
    def start(self):
        """Initialize analyzer at strategy start"""
        super(TaxEfficiency, self).start()
        self.start_value = self.strategy.broker.getvalue()
        self.start_date = self._get_current_datetime()
        
        # Initialize portfolio value tracking
        self.portfolio_values.append({
            'date': self.start_date,
            'value': self.start_value,
            'cash': self.strategy.broker.getcash()
        })
    
    def _get_current_datetime(self):
        """Get current strategy datetime (backtesting-safe)"""
        # Use strategy datetime for backtesting accuracy
        if hasattr(self.strategy, 'datetime') and len(self.strategy.datas) > 0:
            return self.strategy.datas[0].datetime.datetime()
        elif hasattr(self.strategy, 'datas') and len(self.strategy.datas) > 0:
            return self.strategy.datas[0].datetime.datetime()
        else:
            # Fallback to system time (live trading only)
            return datetime.datetime.now()
    
    def notify_trade(self, trade):
        """Track completed trades for tax efficiency analysis"""
        if trade.isclosed:
            current_dt = self._get_current_datetime()
            
            # Calculate realized gain/loss
            realized_pnl = trade.pnl
            trade_value = abs(trade.size * trade.price)
            
            # Estimate tax cost (only on gains)
            tax_cost = 0.0
            if realized_pnl > 0:
                tax_cost = realized_pnl * self.p.capital_gains_tax_rate
                self.total_realized_gains += realized_pnl
            else:
                self.total_realized_losses += abs(realized_pnl)
            
            # Track transaction costs if applicable
            transaction_cost = 0.0
            if self.p.include_transaction_costs and hasattr(trade, 'commission'):
                transaction_cost = trade.commission
                self.total_transaction_costs += transaction_cost
            
            # Record trade details
            trade_record = {
                'date': current_dt,
                'size': trade.size,
                'price': trade.price,
                'value': trade_value,
                'pnl': realized_pnl,
                'tax_cost': tax_cost,
                'transaction_cost': transaction_cost,
                'data_name': trade.data._name if hasattr(trade.data, '_name') else 'Unknown'
            }
            
            self.trade_history.append(trade_record)
            self.total_trade_value += trade_value
            self.total_trades += 1
            self.total_tax_costs += tax_cost
    
    def notify_order(self, order):
        """Track order execution for position change analysis"""
        if order.status in [order.Completed]:
            current_dt = self._get_current_datetime()
            
            position_change = {
                'date': current_dt,
                'action': 'BUY' if order.size > 0 else 'SELL',
                'size': abs(order.size),
                'price': order.executed.price,
                'value': abs(order.size * order.executed.price)
            }
            
            self.position_changes.append(position_change)
    
    def next(self):
        """Track portfolio value changes daily"""
        current_dt = self._get_current_datetime()
        current_value = self.strategy.broker.getvalue()
        current_cash = self.strategy.broker.getcash()
        
        # Record daily portfolio values
        self.portfolio_values.append({
            'date': current_dt,
            'value': current_value,
            'cash': current_cash
        })
        
        self.trading_days += 1
        
        # Calculate unrealized gains from current positions
        self._update_unrealized_gains()
    
    def _update_unrealized_gains(self):
        """Calculate current unrealized gains using tax lot information"""
        total_unrealized = 0.0
        fifo_cost_total = 0.0
        average_cost_total = 0.0
        
        # Iterate through all positions to calculate unrealized gains
        for data in self.strategy.datas:
            position = self.strategy.getposition(data)
            if position.size != 0:
                current_price = data.close[0]
                current_value = abs(position.size * current_price)
                
                # Calculate unrealized gain using position average cost
                position_cost = abs(position.size * position.price)
                unrealized_gain = current_value - position_cost
                total_unrealized += unrealized_gain
                
                # Track FIFO vs average cost basis if tax lots available
                if hasattr(position, 'taxlots') and position.taxlots:
                    # FIFO cost basis (actual lot costs)
                    fifo_cost = sum(lot.remaining_qty * lot.price for lot in position.taxlots)
                    fifo_cost_total += fifo_cost
                    
                    # Average cost basis (position average)
                    average_cost_total += position_cost
                else:
                    # No tax lots, use position cost as both
                    fifo_cost_total += position_cost
                    average_cost_total += position_cost
        
        self.total_unrealized_gains = total_unrealized
        self.fifo_cost_basis_total = fifo_cost_total
        self.average_cost_basis_total = average_cost_total
        self.fifo_advantage = average_cost_total - fifo_cost_total
    
    def stop(self):
        """Finalize analysis at strategy completion"""
        super(TaxEfficiency, self).stop()
        self.end_value = self.strategy.broker.getvalue()
        self.end_date = self._get_current_datetime()
        
        # Final unrealized gains calculation
        self._update_unrealized_gains()
    
    def get_analysis(self):
        """Return comprehensive tax efficiency analysis"""
        
        # Calculate core performance metrics
        total_return = (self.end_value - self.start_value) / self.start_value if self.start_value > 0 else 0.0
        gross_pnl = self.total_realized_gains + self.total_realized_losses + self.total_unrealized_gains
        net_after_tax_pnl = gross_pnl - self.total_tax_costs
        
        # Calculate tax efficiency metrics
        tax_drag = self.total_tax_costs / self.start_value if self.start_value > 0 else 0.0
        tax_drag_percentage = (self.total_tax_costs / gross_pnl) if gross_pnl > 0 else 0.0
        
        # Calculate portfolio turnover
        average_portfolio_value = sum(pv['value'] for pv in self.portfolio_values) / len(self.portfolio_values) if self.portfolio_values else self.start_value
        portfolio_turnover = (self.total_trade_value / 2) / average_portfolio_value if average_portfolio_value > 0 else 0.0
        
        # Calculate realization rate
        total_gains = self.total_realized_gains + self.total_unrealized_gains
        realization_rate = self.total_realized_gains / total_gains if total_gains > 0 else 0.0
        
        # Calculate FIFO efficiency
        fifo_tax_savings = 0.0
        fifo_efficiency_score = 0.0
        if self.fifo_advantage > 0:
            fifo_tax_savings = self.fifo_advantage * self.p.capital_gains_tax_rate
            fifo_efficiency_score = (self.fifo_advantage / self.average_cost_basis_total) * 100 if self.average_cost_basis_total > 0 else 0.0
        
        # Calculate tax efficiency score (0-100)
        # Lower turnover, lower realization rate, higher FIFO advantage = higher score
        base_score = 100.0
        turnover_penalty = min(portfolio_turnover * 50, 30.0)  # Max 30 point penalty
        realization_penalty = realization_rate * 40.0  # Max 40 point penalty
        fifo_bonus = min(fifo_efficiency_score / 2, 10.0)  # Max 10 point bonus
        
        tax_efficiency_score = max(0.0, base_score - turnover_penalty - realization_penalty + fifo_bonus)
        
        # Build comprehensive analysis
        analysis = AutoDict()
        
        # Summary information
        analysis.summary.analyzer_type = 'TaxEfficiency'
        analysis.summary.european_compliance = self.p.european_compliance
        analysis.summary.capital_gains_tax_rate = self.p.capital_gains_tax_rate
        analysis.summary.period_start = self.start_date
        analysis.summary.period_end = self.end_date
        analysis.summary.trading_days = self.trading_days
        
        # Core performance metrics
        analysis.performance.total_return = total_return
        analysis.performance.gross_pnl = gross_pnl
        analysis.performance.net_after_tax_pnl = net_after_tax_pnl
        analysis.performance.start_value = self.start_value
        analysis.performance.end_value = self.end_value
        
        # Tax efficiency metrics
        analysis.tax_efficiency.tax_drag = tax_drag
        analysis.tax_efficiency.tax_drag_percentage = tax_drag_percentage
        analysis.tax_efficiency.total_tax_costs = self.total_tax_costs
        analysis.tax_efficiency.tax_cost_ratio = self.total_tax_costs / average_portfolio_value if average_portfolio_value > 0 else 0.0
        analysis.tax_efficiency.efficiency_score = tax_efficiency_score
        
        # Trading behavior metrics
        analysis.trading_behavior.portfolio_turnover = portfolio_turnover
        analysis.trading_behavior.total_trades = self.total_trades
        analysis.trading_behavior.total_trade_value = self.total_trade_value
        analysis.trading_behavior.average_portfolio_value = average_portfolio_value
        analysis.trading_behavior.realization_rate = realization_rate
        
        # Gains analysis
        analysis.gains_analysis.realized_gains = self.total_realized_gains
        analysis.gains_analysis.realized_losses = self.total_realized_losses
        analysis.gains_analysis.unrealized_gains = self.total_unrealized_gains
        analysis.gains_analysis.total_gains = total_gains
        
        # FIFO efficiency analysis
        analysis.fifo_analysis.fifo_cost_basis = self.fifo_cost_basis_total
        analysis.fifo_analysis.average_cost_basis = self.average_cost_basis_total
        analysis.fifo_analysis.fifo_advantage = self.fifo_advantage
        analysis.fifo_analysis.fifo_tax_savings = fifo_tax_savings
        analysis.fifo_analysis.fifo_efficiency_score = fifo_efficiency_score
        
        # Transaction costs (if included)
        if self.p.include_transaction_costs:
            analysis.transaction_costs.total_transaction_costs = self.total_transaction_costs
            analysis.transaction_costs.transaction_cost_ratio = self.total_transaction_costs / average_portfolio_value if average_portfolio_value > 0 else 0.0
        
        # Trade details (if detailed analysis requested)
        if self.p.detailed_analysis:
            analysis.trade_details = self.trade_history
            analysis.position_changes = self.position_changes
        
        return analysis
    
    def print_analysis(self):
        """Print formatted tax efficiency analysis"""
        analysis = self.get_analysis()
        
        print("=" * 80)
        print("TAX EFFICIENCY ANALYSIS")
        print("=" * 80)
        
        # Header information
        if self.p.headers:
            print(f"Analysis Type: {analysis.summary.analyzer_type}")
            print(f"European Market Compliance: {analysis.summary.european_compliance}")
            print(f"Capital Gains Tax Rate: {analysis.summary.capital_gains_tax_rate:.1%}")
            print(f"Period: {analysis.summary.period_start.strftime('%Y-%m-%d')} to {analysis.summary.period_end.strftime('%Y-%m-%d')}")
            print(f"Trading Days: {analysis.summary.trading_days}")
            print()
        
        # Performance overview
        print("--- Performance Overview ---")
        print(f"Portfolio Start Value: ${analysis.performance.start_value:,.2f}")
        print(f"Portfolio End Value: ${analysis.performance.end_value:,.2f}")
        print(f"Total Return: {analysis.performance.total_return:.2%}")
        print(f"Gross P&L: ${analysis.performance.gross_pnl:,.2f}")
        print(f"Net After-Tax P&L: ${analysis.performance.net_after_tax_pnl:,.2f}")
        print()
        
        # Tax efficiency metrics
        print("--- Tax Efficiency Metrics ---")
        print(f"Tax Drag: {analysis.tax_efficiency.tax_drag:.2%} of portfolio value")
        print(f"Tax Drag: {analysis.tax_efficiency.tax_drag_percentage:.1%} of gross returns")
        print(f"Total Tax Costs: ${analysis.tax_efficiency.total_tax_costs:,.2f}")
        print(f"Tax Cost Ratio: {analysis.tax_efficiency.tax_cost_ratio:.2%}")
        print(f"Tax Efficiency Score: {analysis.tax_efficiency.efficiency_score:.1f}/100")
        print()
        
        # Trading behavior
        print("--- Trading Behavior Analysis ---")
        print(f"Portfolio Turnover Rate: {analysis.trading_behavior.portfolio_turnover:.1%}")
        print(f"Total Trades: {analysis.trading_behavior.total_trades}")
        print(f"Total Trade Value: ${analysis.trading_behavior.total_trade_value:,.2f}")
        print(f"Realization Rate: {analysis.trading_behavior.realization_rate:.1%}")
        print()
        
        # Gains breakdown
        print("--- Gains Analysis ---")
        print(f"Realized Gains: ${analysis.gains_analysis.realized_gains:,.2f}")
        print(f"Realized Losses: ${analysis.gains_analysis.realized_losses:,.2f}")
        print(f"Unrealized Gains: ${analysis.gains_analysis.unrealized_gains:,.2f}")
        print(f"Total Gains: ${analysis.gains_analysis.total_gains:,.2f}")
        print()
        
        # FIFO efficiency
        print("--- FIFO Efficiency Analysis ---")
        print(f"FIFO Cost Basis: ${analysis.fifo_analysis.fifo_cost_basis:,.2f}")
        print(f"Average Cost Basis: ${analysis.fifo_analysis.average_cost_basis:,.2f}")
        print(f"FIFO Advantage: ${analysis.fifo_analysis.fifo_advantage:,.2f}")
        print(f"FIFO Tax Savings: ${analysis.fifo_analysis.fifo_tax_savings:,.2f}")
        print(f"FIFO Efficiency Score: {analysis.fifo_analysis.fifo_efficiency_score:.1f}%")
        print()
        
        # Transaction costs (if included)
        if self.p.include_transaction_costs and hasattr(analysis, 'transaction_costs'):
            print("--- Transaction Costs ---")
            total_costs = float(analysis.transaction_costs.total_transaction_costs)
            cost_ratio = float(analysis.transaction_costs.transaction_cost_ratio)
            print(f"Total Transaction Costs: ${total_costs:,.2f}")
            print(f"Transaction Cost Ratio: {cost_ratio:.2%}")
            print()
        
        # Efficiency interpretation
        print("--- Tax Efficiency Interpretation ---")
        score = analysis.tax_efficiency.efficiency_score
        if score >= 80:
            efficiency_rating = "Excellent"
            interpretation = "Highly tax-efficient strategy with minimal tax drag"
        elif score >= 65:
            efficiency_rating = "Good"
            interpretation = "Well-managed tax efficiency with room for improvement"
        elif score >= 50:
            efficiency_rating = "Fair"
            interpretation = "Moderate tax efficiency, consider reducing turnover or timing"
        else:
            efficiency_rating = "Poor"
            interpretation = "High tax drag, strategy needs tax optimization"
        
        print(f"Tax Efficiency Rating: {efficiency_rating}")
        print(f"Interpretation: {interpretation}")
        print("=" * 80)
    
    def get_efficiency_score(self):
        """Quick access to tax efficiency score"""
        analysis = self.get_analysis()
        return analysis.tax_efficiency.efficiency_score
    
    def get_tax_drag(self):
        """Quick access to tax drag percentage"""
        analysis = self.get_analysis()
        return analysis.tax_efficiency.tax_drag 