
import datetime

class TaxLot:
    def __init__(self, qty, price, datetime_):
        self.qty = qty
        self.price = price
        self.datetime = datetime_
        self.remaining_qty = qty

    def __str__(self):
        return f"TaxLot(qty={self.qty}, price={self.price}, datetime={self.datetime}, remaining={self.remaining_qty})"

    def reduce_qty(self, qty):
        if qty > self.remaining_qty:
            raise ValueError("Reduce qty exceeds remaining")
        self.remaining_qty -= qty

class PositionWithTaxLots(Position):
    def __init__(self, size=0, price=0.0):
        super().__init__(size, price)
        self.taxlots = []

    def add_taxlot(self, qty, price, datetime_):
        lot = TaxLot(qty, price, datetime_)
        self.taxlots.append(lot)
        return lot

    def get_taxlots(self):
        return self.taxlots

    def __str__(self):
        base_str = super().__str__()
        lots_str = "\n".join("  " + str(lot) for lot in self.taxlots)
        return f"{base_str}\nTax Lots:\n{lots_str}"

import backtrader.position as origpos
Position = origpos.Position
class Position(PositionWithTaxLots):
    pass
