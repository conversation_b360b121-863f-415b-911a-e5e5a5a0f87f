#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
"""
Test PyFolio Compatibility with Tax Lot Tracking - Phase 2.2.3 Final Task

This test verifies that the PyFolio analyzer continues to work correctly
with our enhanced Transactions analyzer that includes tax lot integration.

Key areas tested:
1. PyFolio basic functionality with tax lot enhanced transactions
2. Data structure compatibility with pandas conversion  
3. No breaking changes to PyFolio's expected data format
4. Performance impact assessment
5. European market compliance with PyFolio workflows
"""

import unittest
import datetime
import sys
import backtrader as bt


class PyFolioCompatibilityStrategy(bt.Strategy):
    """Test strategy for PyFolio compatibility testing"""
    
    params = (
        ('trade_size', 100),
        ('num_trades', 5),
    )
    
    def __init__(self):
        self.order_count = 0
        self.max_trades = self.p.num_trades * 2  # Buy and sell trades
        
    def next(self):
        if self.order_count >= self.max_trades:
            return
            
        current_bar = len(self.data)
        
        # Create a mix of buy and sell orders to generate transactions
        if current_bar % 10 == 5:  # Buy every 10 bars
            if self.order_count < self.max_trades:
                self.buy(size=self.p.trade_size)
                self.order_count += 1
                
        elif current_bar % 10 == 15:  # Sell after buying
            if self.position.size > 0 and self.order_count < self.max_trades:
                self.sell(size=min(self.p.trade_size // 2, self.position.size))
                self.order_count += 1


class TestPyFolioCompatibility(unittest.TestCase):
    """Test PyFolio compatibility with tax lot enhanced transactions"""
    
    def setUp(self):
        """Set up test environment"""
        self.cerebro = bt.Cerebro()
        
        # Add data feed
        data = bt.feeds.PandasData(
            dataname=self._create_test_data(),
            timeframe=bt.TimeFrame.Days
        )
        self.cerebro.adddata(data)
        
        # Set broker parameters
        self.cerebro.broker.setcash(100000.0)
        self.cerebro.broker.setcommission(commission=0.001)
        
    def _create_test_data(self):
        """Create test data for backtesting"""
        import pandas as pd
        
        # Create 30 days of test data
        dates = pd.date_range('2023-01-01', periods=30, freq='D')
        prices = [100.0 + i * 0.5 + (i % 3) * 2.0 for i in range(30)]  # Trending prices
        
        data = pd.DataFrame({
            'open': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices], 
            'close': prices,
            'volume': [10000] * 30
        }, index=dates)
        
        return data
    
    def test_pyfolio_basic_functionality(self):
        """Test that PyFolio analyzer works with our enhanced transactions"""
        # Add strategy
        self.cerebro.addstrategy(PyFolioCompatibilityStrategy, 
                                trade_size=100, num_trades=3)
        
        # Add PyFolio analyzer
        self.cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
        
        # Run backtest
        results = self.cerebro.run()
        strategy = results[0]
        
        # Verify PyFolio analyzer was created
        self.assertTrue(hasattr(strategy.analyzers, 'pyfolio'))
        
        # Get PyFolio analysis
        pyfolio_analysis = strategy.analyzers.pyfolio.get_analysis()
        
        # Verify expected data structure
        self.assertIn('returns', pyfolio_analysis)
        self.assertIn('positions', pyfolio_analysis)
        self.assertIn('transactions', pyfolio_analysis)
        self.assertIn('gross_lev', pyfolio_analysis)
        
        print("✅ PyFolio basic functionality test passed")
        
    def test_pyfolio_with_enhanced_transactions(self):
        """Test PyFolio specifically with our enhanced Transactions analyzer"""
        # Add strategy
        self.cerebro.addstrategy(PyFolioCompatibilityStrategy,
                                trade_size=150, num_trades=4)
        
        # Add PyFolio analyzer
        self.cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
        
        # Also add enhanced Transactions analyzer to verify no conflicts
        self.cerebro.addanalyzer(bt.analyzers.Transactions, 
                                include_taxlot_details=True,
                                headers=True, 
                                _name='enhanced_transactions')
        
        # Run backtest
        results = self.cerebro.run()
        strategy = results[0]
        
        # Get both analyses
        pyfolio_analysis = strategy.analyzers.pyfolio.get_analysis()
        enhanced_transactions = strategy.analyzers.enhanced_transactions.get_analysis()
        
        # Verify PyFolio transactions data exists and is properly formatted
        pyfolio_transactions = pyfolio_analysis['transactions']
        self.assertIsInstance(pyfolio_transactions, dict)
        
        # Verify enhanced transactions work alongside PyFolio
        self.assertIsInstance(enhanced_transactions, (dict, list))
        
        # Check that both contain transaction data
        if pyfolio_transactions:
            print(f"PyFolio recorded {len(pyfolio_transactions)} transaction dates")
        if enhanced_transactions:
            print(f"Enhanced Transactions recorded transaction data")
            
        print("✅ PyFolio with enhanced transactions test passed")
        
    def test_pyfolio_data_structure_integrity(self):
        """Test that PyFolio data structures remain intact"""
        # Add strategy
        self.cerebro.addstrategy(PyFolioCompatibilityStrategy,
                                trade_size=200, num_trades=3)
        
        # Add PyFolio analyzer
        self.cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
        
        # Run backtest
        results = self.cerebro.run()
        strategy = results[0]
        
        # Get analysis
        analysis = strategy.analyzers.pyfolio.get_analysis()
        
        # Test data structure integrity
        for key in ['returns', 'positions', 'transactions', 'gross_lev']:
            self.assertIn(key, analysis)
            
        # Verify transactions structure (should be dict with date keys)
        transactions = analysis['transactions']
        self.assertIsInstance(transactions, dict)
        
        # If we have transactions, verify their structure
        if transactions:
            # Get a sample transaction to check structure
            sample_date = next(iter(transactions.keys()))
            sample_transactions = transactions[sample_date]
            
            # Should be a list of transaction records
            self.assertIsInstance(sample_transactions, list)
            
            if sample_transactions:
                # Each transaction should be a list/tuple of values
                sample_transaction = sample_transactions[0]
                self.assertIsInstance(sample_transaction, (list, tuple))
                
                # Should have the expected number of fields
                # [symbol, amount, price, sid, value, commission]
                self.assertGreaterEqual(len(sample_transaction), 5)
                
        print("✅ PyFolio data structure integrity test passed")
        
    def test_pyfolio_pandas_conversion(self):
        """Test PyFolio's pandas conversion functionality"""
        # Only run if pandas is available
        try:
            import pandas as pd
        except ImportError:
            self.skipTest("Pandas not available - skipping pandas conversion test")
            
        # Add strategy  
        self.cerebro.addstrategy(PyFolioCompatibilityStrategy,
                                trade_size=100, num_trades=2)
        
        # Add PyFolio analyzer
        self.cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
        
        # Run backtest
        results = self.cerebro.run()
        strategy = results[0]
        
        # Test pandas conversion
        try:
            pyfolio_analyzer = strategy.analyzers.pyfolio
            returns, positions, transactions, gross_lev = pyfolio_analyzer.get_pf_items()
            
            # Verify we got pandas objects
            self.assertIsInstance(returns, pd.Series)
            self.assertIsInstance(positions, pd.DataFrame)
            self.assertIsInstance(transactions, pd.DataFrame)
            self.assertIsInstance(gross_lev, pd.Series)
            
            print("✅ PyFolio pandas conversion test passed")
            
        except Exception as e:
            self.fail(f"PyFolio pandas conversion failed: {e}")
            
    def test_pyfolio_no_transactions_scenario(self):
        """Test PyFolio behavior when no transactions occur"""
        # Use a strategy that doesn't trade
        class NoTradeStrategy(bt.Strategy):
            def next(self):
                pass  # No trading
                
        # Clear previous strategy and add no-trade strategy
        self.cerebro = bt.Cerebro()
        data = bt.feeds.PandasData(
            dataname=self._create_test_data(),
            timeframe=bt.TimeFrame.Days
        )
        self.cerebro.adddata(data)
        self.cerebro.broker.setcash(100000.0)
        self.cerebro.addstrategy(NoTradeStrategy)
        
        # Add PyFolio analyzer
        self.cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
        
        # Run backtest
        results = self.cerebro.run()
        strategy = results[0]
        
        # Verify PyFolio works even with no transactions
        analysis = strategy.analyzers.pyfolio.get_analysis()
        
        self.assertIn('transactions', analysis)
        transactions = analysis['transactions']
        
        # Transactions should be empty or have no data
        self.assertIsInstance(transactions, dict)
        
        print("✅ PyFolio no transactions scenario test passed")
        
    def test_pyfolio_performance_impact(self):
        """Test performance impact of tax lot enhanced transactions on PyFolio"""
        import time
        
        # Test with larger trade volume
        class HighVolumeStrategy(PyFolioCompatibilityStrategy):
            params = (('trade_size', 50), ('num_trades', 8))
            
        # Clear and setup for performance test
        self.cerebro = bt.Cerebro()
        data = bt.feeds.PandasData(
            dataname=self._create_test_data(),
            timeframe=bt.TimeFrame.Days
        )
        self.cerebro.adddata(data)
        self.cerebro.broker.setcash(100000.0)
        self.cerebro.addstrategy(HighVolumeStrategy)
        
        # Add PyFolio analyzer
        self.cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
        
        # Time the execution
        start_time = time.time()
        results = self.cerebro.run()
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # Verify it completes in reasonable time (should be under 1 second)
        self.assertLess(execution_time, 1.0)
        
        # Verify results
        strategy = results[0]
        analysis = strategy.analyzers.pyfolio.get_analysis()
        self.assertIn('transactions', analysis)
        
        print(f"✅ PyFolio performance test passed (execution time: {execution_time:.3f}s)")
        
    def test_pyfolio_european_market_compliance(self):
        """Test PyFolio works with European market tax lot features"""
        # Add strategy
        self.cerebro.addstrategy(PyFolioCompatibilityStrategy,
                                trade_size=100, num_trades=3)
        
        # Add PyFolio with enhanced transactions (European style)
        self.cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
        self.cerebro.addanalyzer(bt.analyzers.Transactions,
                                include_taxlot_details=True,
                                headers=True,
                                _name='european_transactions')
        
        # Run backtest
        results = self.cerebro.run()
        strategy = results[0]
        
        # Verify both analyzers work together
        pyfolio_analysis = strategy.analyzers.pyfolio.get_analysis()
        european_analysis = strategy.analyzers.european_transactions.get_analysis()
        
        # Both should contain valid data
        self.assertIn('transactions', pyfolio_analysis)
        
        # European transactions should work without US-specific features
        if european_analysis:
            # Should be a list of transaction records or dict
            self.assertIsInstance(european_analysis, (list, dict))
            
        print("✅ PyFolio European market compliance test passed")


def run_tests():
    """Run PyFolio compatibility tests"""
    print("=== PyFolio Compatibility Tests - Phase 2.2.3 Final Task ===")
    print("Testing PyFolio analyzer compatibility with tax lot enhanced transactions")
    print()
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPyFolioCompatibility)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n=== Test Results ===")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, failure in result.failures:
            print(f"  - {test}: {failure}")
            
    if result.errors:
        print("\nErrors:")
        for test, error in result.errors:
            print(f"  - {test}: {error}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n✅ PyFolio Compatibility: {'PASSED' if success else 'FAILED'}")
    
    if success:
        print("\n🎉 Phase 2.2.3 Complete - PyFolio compatibility verified!")
        print("Ready to move to Phase 2.2.4 - Order Execution Enhancement")
    
    return success


if __name__ == '__main__':
    run_tests() 