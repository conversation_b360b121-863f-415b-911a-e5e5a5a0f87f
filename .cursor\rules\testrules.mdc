---
description: 
globs: 
alwaysApply: true
---
# This rule to appy when designig test cases or scenarios only, ignore in other cases

## Test Comprehensive Coverage

Ensure tests cover typical cases, edge cases, invalid inputs, and error conditions.

Include scenarios for all method behaviors to guarantee robustness.

## Test Readability and Clarity

Use clear, descriptive test names (e.g., test_functionality_with_valid_input).

Prefer concise and expressive assertions (e.g., assertEqual, assertTrue).

Keep test code simple and avoid duplication.

## Test Logical Structure

Organize tests inside classes derived from unittest.TestCase.

Use helper functions or dynamic test generation to reduce repetitive code.

Group related tests using class or method naming conventions.

## Test Data Management

Use fixtures or factory methods to set up test data.

Minimize shared state between tests to ensure independence.

## Isolation and Independence

Each test should be independent and not rely on side effects from others.

Use mocks or stubs for external dependencies where appropriate.

## Dynamic Test Generation (Example)

Dynamically generate tests using functions that create test methods and attach them to test classes. This improves maintainability and scalability:

<Example>
python
import unittest

class DynamicTests(unittest.TestCase):
    pass

def make_test(name, input_val, expected):
    def test(self):
        self.assertEqual(input_val, expected, f"Failed test: {name}")
    return test

test_cases = {
    'test_case_1': (1, 1),
    'test_case_2': (2, 3),  # This will fail
    'test_case_3': (5, 5),
}

for name, (inp, exp) in test_cases.items():
    test_func = make_test(name, inp, exp)
    setattr(DynamicTests, name, test_func)

if __name__ == '__main__':
    unittest.main()

</Example>

## Test report generation 
The following items to print
1. Input Data Overview
2. Expected Output Structure
Complete analysis structure with all expected fields
Validation rules and mathematical bounds
Data type requirements and constraints
3. Live Sample Analysis
Shows actual vs expected results in real-time
Validates structure, metrics, and compliance
Performance benchmarking (execution time)
4. Detailed Execution Results
Test category performance breakdown
Coverage analysis by functional area
Quantitative success metrics
Performance grading system
5. Comprehensive Assessment
Failure analysis with specific issues
Overall validation coverage achieved
Production readiness assessment
Clear action items if issues found