{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["########################################<br>\n", "                                       #<br>\n", " eTrade tax loss harvesting script     #<br>\n", "                                       #<br>\n", " <PERSON>, danguetta.github.io    #<br>\n", "                                       #<br>\n", "######################################## "]}, {"cell_type": "markdown", "metadata": {}, "source": ["=======================<br>\n", "=   Import packages   =<br>\n", "======================="]}, {"cell_type": "markdown", "metadata": {}, "source": ["Logging"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "from logging.handlers import RotatingFileHandler"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Configuration parser"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import configparser"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Packages for authentication"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from rauth import OAuth1Service\n", "import webbrowser"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Progress bars for loops"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pandas, numpy, random numbers"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import random"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Date and time"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import pytz\n", "import holidays\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["json processing"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Files"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Jupyter display"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Markdown, HTML\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Optimizer"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import scipy.optimize as opt"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import jinja2\n", "assert jinja2.__version__ >= '3.0.0', 'Please upgrade Pandas to a version >= 3.0.0. You can do this by running pip install jinja2 --upgrade'"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["assert pd.__version__ >= '1.5.1', 'Please upgrade Pandas to a version >= 1.5.1. You can do this by running pip install pandas --upgrade'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["=================<br>\n", "=   Constants   =<br>\n", "================="]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["TOLERANCE = 0.02\n", "CASH_NAME = 'Cash'\n", "OTHER_NAME = 'Other'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["=====================<br>\n", "=   Error Classes   =<br>\n", "====================="]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["class ApiQueryError(Exception):\n", "    '''Exception raised when the eTrade API returns an error\n", "    \n", "    Attributes\n", "    ----------\n", "      - url          : the URL for the API request that was sent\n", "      - data         : the data that was sent to the request\n", "      - status_code  : the status code returned from the request\n", "      - response     : the full response from the API\n", "    '''\n", "    \n", "    def __init__(self, url, data, status_code, response):\n", "        '''Initializes API query errors\n", "        \n", "        Arguments\n", "        ---------\n", "          See class attributes\n", "        '''\n", "        \n", "        self.url         = url\n", "        self.data        = data\n", "        self.status_code = status_code\n", "        self.response    = response\n", "        \n", "        super().__init__('Something wrong happened with the eTrade request; the '\n", "                         'log file will have the full request and its response; '\n", "                         'alternatively, see the documentation for this error to '\n", "                         'access this data.')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["class ValidationError(Exception):\n", "    '''Exception raised when input data is incorrect\n", "    \n", "    Attributes\n", "    ----------\n", "      - input_vaue : the value that was input\n", "      - message    : the message to display\n", "    '''\n", "    \n", "    def __init__(self, message, input_value=None):\n", "        '''Initializes validation errors\n", "        \n", "        Arguments\n", "        ---------\n", "          See class attributes\n", "        '''\n", "        \n", "        self.input_value = input_value\n", "        self.message     = message + (f'(input value: {input_value})' if input_value is not None else '')\n", "        \n", "        super().__init__(self.message)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["class ExpectedCostError(Exception):\n", "    '''Exception raised when the expected cost of an order is far from the estimated cost\n", "    \n", "    Attributes\n", "    ----------\n", "      - expected_cost\n", "      - estimated_cost\n", "    '''\n", "    \n", "    def __init__(self, expected_cost, estimated_cost):\n", "        '''Initialize expected cost errors\n", "        \n", "        Arguments\n", "        ---------\n", "          See class attributes\n", "        '''\n", "        \n", "        self.expected_cost = expected_cost\n", "        self.estimated_cost = estimated_cost\n", "        \n", "        super().__init__('The expected cost for this order was too far from the estimated '\n", "                            f'cost (expected cost: ${expected_cost:,.2f}, estimated cost: '\n", "                            f'${estimated_cost:,.2f})')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["class OrderPreviewError(Exception):\n", "    '''Exception raised when we attempt to place an order with an invalid preview\n", "    \n", "    Attributes\n", "    ----------\n", "      - message\n", "    '''\n", "    \n", "    def __init__(self, message):\n", "        '''Initialize order preview errors\n", "        \n", "        Argument\n", "        --------\n", "          See class attributes\n", "        '''\n", "        \n", "        self.message = message\n", "        \n", "        super().__init__(message)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["class RebalancerError(Exception):\n", "    '''Exception raised when the rebalancer errors out\n", "    \n", "    Attributes\n", "    ----------\n", "      - message\n", "    '''\n", "    \n", "    def __init__(self, message):\n", "        '''Initialize order preview errors\n", "        \n", "        Argument\n", "        --------\n", "          See class attributes\n", "        '''\n", "        \n", "        self.message = message\n", "        \n", "        super().__init__(message)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def market_open():\n", "    '''Determines whether the markets are currently open\n", "    \n", "    This isn't foolproof (for example, it doesn't include the early market close\n", "    on the day after thanksgiving)    \n", "    '''\n", "    \n", "    cur_date = datetime.datetime.now(pytz.timezone('America/New_York'))\n", "    \n", "    if (cur_date.hour <= 8) or ((cur_date.hour == 9) and (cur_date.minute < 30)) or (cur_date.hour >= 16):\n", "        return False\n", "    \n", "    if (cur_date.weekday() >= 5):\n", "        return False\n", "    \n", "    if (cur_date in holidays.NYSE()):\n", "        return False\n", "    \n", "    return True\n", "    \n", "# ===============\n", "# =   Classes   =\n", "# ==============="]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["class TargetAssetclass:\n", "    '''Specifies a target asset class in a portfolio (eg: domestic stocks)\n", "    \n", "    Attributes\n", "    ----------\n", "      - target (float) : a number between 0 and 100, specifying the percentage\n", "                         of the portfolio that should comprise this asset class\n", "      - name (string)  : a plain English description of this asset class (eg:\n", "                         \"domestic stocks\")\n", "      \n", "    Methods\n", "    -------\n", "      - securities : a method that, given a badness level, returns a list of stock\n", "                     tickers with that badness level, in the order they were added\n", "                     to the class\n", "    '''\n", "    \n", "    def __init__(self, target, name, securities, badness_scores):\n", "        '''Initialize a target asset class\n", "        \n", "        Arguments\n", "        ---------\n", "          - target (float)    : a number between 0 and 100, specifying the percentage\n", "                                of the portfolio that should comprise this asset class.\n", "                                This number should be an integer, to avoid Python float\n", "                                errors\n", "          - name (string)     : a plain English description of the asset class\n", "          - securities (list) : a list of strings, each of which is a ticker for one\n", "                                of several correlated securities that can be used to\n", "                                meet this asset class\n", "          - badness_scores (list) : a list of integers >= 1, at least one of which\n", "                                should be 1, with the same length as securities. Each\n", "                                entry specifies how \"undesirable\" the security in\n", "                                question is. The algorithm will always try to sell\n", "                                securities with higher badness scores and buy\n", "                                securities with lower badness scores (see the\n", "                                description of the algorithm for details). These\n", "                                badness scores should be consecutive integers.\n", "        '''\n", "        \n", "        assert 0 < target <= 100, ('The target for this asset class should be greater '\n", "                                                    'than 0 and less than or equal to 100' )\n", "        \n", "        assert int(target) == target, 'The target for this asset class should be an integer'\n", "        \n", "        assert len(badness_scores) == len(securities), ( 'Every security should have an '\n", "                                                'associated badness score' )\n", "        \n", "        assert all([i is None or i == int(i) for i in badness_scores]), ( 'Badness scores must all be '\n", "                                                'integers' )\n", "        \n", "        assert all([i is None or i >= 1 for i in badness_scores]), ( 'Badness scores should all be >= 1' )\n", "        \n", "        assert any([i == 1 for i in badness_scores]), ( 'At least one badness score must be == 1' )\n", "        \n", "        # Check the badness scores are consecutive\n", "        unique_scores = sorted(list(set(badness_scores) - set([None])))\n", "        assert all([unique_scores[i+1] - unique_scores[i] == 1\n", "                                    for i in range(len(unique_scores)-1)]), 'Badness scores should be consecutive'\n", "        \n", "        if name in [OTHER_NAME, CASH_NAME]:\n", "            raise ValidationError(f'\"{OTHER_NAME}\" and \"{CASH_NAME}\" are reserved names; please '\n", "                                                                'call your asset class something else.')\n", "        \n", "        self.target          = target\n", "        self.name            = name\n", "        self._securities     = [i.upper() for i in securities]\n", "        self._badness_scores = badness_scores\n", "    \n", "    def securities(self, badness_score=None):\n", "        '''Returns the securities in this asset class with a specific badness score\n", "        \n", "        Arguments\n", "        ---------\n", "          - badness_score (int) : the badness score to extract; if None, all securities\n", "                                  in this asset class are returned\n", "        \n", "        Returns\n", "        -------\n", "           A list of strings, each containing a ticker for a security in that\n", "           asset class with the badness score specified.\n", "           \n", "           If no securities in this asset class have the specified badness scores,\n", "           returns an empty list.\n", "        '''\n", "        \n", "        return [i for i, j in zip(self._securities, self._badness_scores)\n", "                                    if (badness_score is None) or (j == badness_score)]\n", "    \n", "    @property\n", "    def badness_scores(self):\n", "        '''Returns a list of badness scores for all securities in this asset class\n", "        \n", "        A copy is returned to make sure we don't inadvertently modify it\n", "        '''\n", "        \n", "        return list(self._badness_scores)\n", "    \n", "class TargetPortfolio:\n", "    '''Specifies a target portfolio\n", "    \n", "    Properties\n", "    ----------\n", "      - target_assetclasses : a dictionary in which each key is the name of a target asset class,\n", "                              and the value is a TargetAssetclass object\n", "    \n", "    Methods\n", "    -------\n", "      - add_assetclass : method to add an asset class to the portfolio\n", "    '''\n", "    \n", "    def __init__(self):\n", "        '''Initialize a TargetPortfolio\n", "        \n", "        No arguments\n", "        '''\n", "        \n", "        self._validated = False\n", "        self._target_assetclasses = {}\n", "    \n", "    def add_assetclass(self, *args, **kwargs):\n", "        '''Add an asset class to the portfolio\n", "        \n", "        If the target portfolio has been validated, do not allow extra asset classes\n", "        to be added to it\n", "        \n", "        Arguments\n", "        ---------\n", "          See the __init__ method of the TargetAssetclass class\n", "        '''\n", "        \n", "        assert not self._validated, 'Target portfolio has been validated; no further asset classes can be added'\n", "        \n", "        target_assetclass = TargetAssetclass(*args, **kwargs)\n", "        \n", "        # Ensure the name doesn't exist yet\n", "        assert target_assetclass.name not in self._target_assetclasses, 'Target asset class name already exists'\n", "        \n", "        self._target_assetclasses[target_assetclass.name] = target_assetclass\n", "        \n", "    def validate(self):\n", "        '''Validate a target portfolio\n", "        \n", "        Will raise an error if the asset classes in the target portfolio do not\n", "        sum to 1, or if some securities appear in more than one asset class\n", "        '''\n", "        \n", "        # Ensure target asset classes sum to 100\n", "        assert sum([a.target for a in self._target_assetclasses.values()]) == 100, 'Targets must sum to 100 across asset classes'\n", "        \n", "        # Ensure no security exists in more than one asset class\n", "        all_securities = set()\n", "        for a in self._target_assetclasses.values():\n", "            if len(all_securities.intersection(a.securities())) > 0:\n", "                raise 'Some securities exist in more than one asset class; please fix'\n", "            \n", "            all_securities.update(a.securities())\n", "            \n", "        self._validated = True\n", "    \n", "    @property\n", "    def target_assetclasses(self):\n", "        '''Will return the list of target asset classes. WARNING: a mutable object is returned; do not modify\n", "        \n", "        Can only be accessed once self.validate has been run\n", "        '''\n", "        \n", "        assert self._validated, 'Please validate the target portfolio before using it.'\n", "        \n", "        return self._target_assetclasses\n", "    \n", "    def find_security(self, symbol):\n", "        '''Identify the asset class a security is part of, and its badness in that asset class\n", "        \n", "        Arguments\n", "        ---------\n", "          - symbol (string) : the stock ticker for that security\n", "        \n", "        Returns\n", "        -------\n", "          A dictionary with two entries\n", "            - assetclass  : the asset class the security is a part of\n", "            - badness     : its badness\n", "          If the security is not found in the target portfolio,\n", "            {'assetclass' : <PERSON>TH<PERSON>_NAME, 'badness' : None}\n", "          will be returned\n", "        '''\n", "        \n", "        for a_name, a in self.target_assetclasses.items():\n", "            if symbol in a.securities():\n", "                return {'assetclass' : a_name, 'badness' : a.badness_scores[a.securities().index(symbol)]}\n", "        \n", "        return {'assetclass' : OTHER_NAME, 'badness' : None}\n", "        \n", "class EtradeConnection:\n", "    '''Connection to the eTrade API\n", "    \n", "    This class contains methods for all the requests we will make of the\n", "    eTrade API.\n", "    \n", "    Most of the code in this class was adapted from the sample Python code\n", "    provided here: https://developer.etrade.com/home\n", "    \n", "    Methods\n", "    -------\n", "      - list_accounts      : list all eTrade accounts\n", "      - get_cash_balance   : get the cash balance for a specific account\n", "      - get_positions      : get all positions in a specific account\n", "      - get_lots           : get all the tax lots that make up on specific\n", "                             position\n", "      - get_recent_trades  : get a list of all securities bought and sold\n", "                             in the last 30 days\n", "      - get_current_price  : get the current price of a security\n", "      - execute_order      : execute a buy or sell order\n", "    '''\n", "\n", "    # Save the base URL that should be prepended to all API requests\n", "    _base_url = 'https://api.etrade.com'\n", "    \n", "    def __init__(self, config_file, log_file = None):\n", "        '''Create a connection to the eTrade API and authenticate\n", "        \n", "        This function will launch a browser to authenticate with eTrade; the\n", "        relevant webpage will ask you to verify access and give you a code\n", "        which Python will prompt you for.\n", "        \n", "        Arguments\n", "        ---------\n", "          - config_file (string) : the filename, or path, to a configuration\n", "                                   file, which includes CONSUMER_KEY and\n", "                                   CONSUMER_SECRET for the eTrade API. See\n", "                                   the docs to this code for details\n", "          - log_file (string)    : every request to the eTrade API will be\n", "                                   logged; this argument determines how the\n", "                                   logging is done\n", "                                     * If None, logging will be done to a file\n", "                                       named using today's date and time in a\n", "                                       \"logs\" folder\n", "                                     * If \"screen\", messages will be printed\n", "                                       to the screen\n", "                                     * If any other string, messages will be\n", "                                       logged to a file with that name\n", "        '''\n", "            \n", "        # Set up a logger if required\n", "        # ---------------------------\n", "        if log_file is None:\n", "            if not os.path.exists('logs'):\n", "                os.mkdir('logs')\n", "                \n", "            log_file = 'logs/' + datetime.datetime.now().strftime('%y-%m-%d %H%M') + '.log'\n", "                \n", "        self._log_file = log_file\n", "        \n", "        if log_file != 'screen':\n", "            self._logger = logging.getLogger('my_logger')\n", "            self._logger.setLevel(logging.DEBUG)\n", "            handler = RotatingFileHandler(log_file, maxBytes=5*1024*1024, backupCount=3)\n", "            FORMAT = \"%(asctime)-15s %(message)s\"\n", "            fmt = logging.Formatter(FORMAT, datefmt='%m/%d/%Y %I:%M:%S %p')\n", "            handler.<PERSON><PERSON><PERSON><PERSON><PERSON>(fmt)\n", "            self._logger.addHandler(handler)   \n", "    \n", "        # Save the config file name\n", "        # -------------------------\n", "        self._config_file = config_file\n", "    \n", "        # Connect to the eTrade API\n", "        # -------------------------\n", "        etrade = OAuth1Service(name              = \"etrade\",\n", "                               consumer_key      = self._config['CONSUMER_KEY'],\n", "                               consumer_secret   = self._config['CONSUMER_SECRET'],\n", "                               request_token_url = \"https://api.etrade.com/oauth/request_token\",\n", "                               access_token_url  = \"https://api.etrade.com/oauth/access_token\",\n", "                               authorize_url     = \"https://us.etrade.com/e/t/etws/authorize?key={}&token={}\",\n", "                               base_url          = \"https://api.etrade.com\")\n", "        \n", "        # Step 1 - Request a token\n", "        request_token, request_token_secret = (\n", "                etrade.get_request_token(params={\"oauth_callback\": \"oob\", \"format\": \"json\"}))\n", "                \n", "        # Step 2 - get the code from the etrade website\n", "        authorize_url = etrade.authorize_url.format(etrade.consumer_key, request_token)\n", "        webbrowser.open(authorize_url)\n", "        text_code = input(\"Please accept agreement and enter text code from browser: \")\n", "        \n", "        # Step 3 - Exchange the authorized request token for an authenticated OAuth 1 session\n", "        self._session = etrade.get_auth_session(request_token,\n", "                                               request_token_secret,\n", "                                               params={\"oauth_verifier\": text_code})\n", "    \n", "    @property\n", "    def _config(self):\n", "        '''Extract the consumer key and secret from a configuration file\n", "        \n", "        These values are not saved in the class to ensure they cannot be\n", "        printed inadvertently\n", "        \n", "        Returns\n", "        -------\n", "          A dictionary with two entries - CONSUMER_KEY and CONSUMER_SECRET\n", "        '''\n", "        \n", "        config = configparser.ConfigParser()\n", "        config.read(self._config_file)\n", "        \n", "        return {'CONSUMER_KEY'    : config[\"DEFAULT\"][\"CONSUMER_KEY\"],\n", "                'CONSUMER_SECRET' : config[\"DEFAULT\"][\"CONSUMER_SECRET\"]}\n", "    \n", "    def _log_message(self, m):\n", "        '''Log a message\n", "        \n", "        Depending on how the class was initiated, this will be to the screen\n", "        or to the file; see __init__ function\n", "        \n", "        Arguments\n", "        ---------\n", "          - m (string) : the message to be logged\n", "        '''\n", "        \n", "        if self._log_file == 'screen':\n", "            print(m)\n", "        else:\n", "            self._logger.debug(m)\n", "        \n", "    def _query(self, url, method, **kwargs):\n", "        '''Run a query against the eTrade API\n", "        \n", "        This function will look the request, and its response, to the log file\n", "        \n", "        Arguments\n", "        ---------\n", "          - url (string) : the URL for the query WITHOUT the prefix; for\n", "                           example: \"/v1/accounts/list.json\"\n", "          - method (string) : GET or POST\n", "          - Any other arguments will be passed directly to the request\n", "        \n", "        Returns\n", "        -------\n", "          A dictionary with the response from eTrade\n", "        '''\n", "        \n", "        # Add the prefix to the URL\n", "        url = self._base_url + url\n", "        \n", "        # Find the relevant method\n", "        assert method in ['GET', 'POST'], 'Only GET and POST methods are supported'\n", "        \n", "        if method == 'GET':\n", "            method = self._session.get\n", "        elif method == 'POST':\n", "            method = self._session.post\n", "        \n", "        # Log the request\n", "        self._log_message(f'Request URL: {url}')\n", "        self._log_message(f'Request method: {method}')\n", "        self._log_message(f'kwargs: {kwargs}')\n", "        \n", "        # Run the request\n", "        response = method(url, header_auth=True, **kwargs)\n", "        \n", "        # Log the response\n", "        self._log_message(f'Request response: {response.text}')\n", "        self._log_message('-----------------------------------------')\n", "        \n", "        # Make sure the response was successful\n", "        if response.status_code not in [200, 204]:\n", "            raise ApiQueryError(url, kwargs, response.status_code, response.text)\n", "            \n", "        # Return the response in json form; make the error more legible if the json\n", "        # can't be read\n", "        try:\n", "            if response.status_code == 204:\n", "                return None\n", "            else:\n", "                return response.json()\n", "        except:\n", "            raise 'Something wrong happened with the eTrade request; see the log file'\n", "    \n", "    @staticmethod\n", "    def _dict_to_xml(o):\n", "        '''\n", "        This function takes a Python object, and converts it to xml format. It accepts a\n", "        single argument, the object to be converted, and returns the xml version\n", "        '''\n", "        \n", "        out = []\n", "        \n", "        if type(o) == list:\n", "            out = [EtradeConnection._dict_to_xml(i) for i in o]\n", "        elif type(o) == dict:\n", "            for i in o:\n", "                this_item = EtradeConnection._dict_to_xml(o[i])\n", "                \n", "                if '\\n' not in this_item:\n", "                    out.append(f'<{i}>{this_item}</{i}>')\n", "                else:\n", "                    out.append(f'<{i}>\\n' + '\\n'.join(['    ' + ii for ii in this_item.split('\\n')]) + f'\\n</{i}>')\n", "        else:\n", "            out.append(str(o))\n", "        \n", "        return '\\n'.join(out)\n", "    def list_accounts(self):\n", "        '''Returns all accounts for this eTrade user\n", "        \n", "        eTrade API docs: https://apisb.etrade.com/docs/api/account/api-account-v1.html\n", "        \n", "        Returns\n", "        -------\n", "          A list of dictionaries, each containing one eTrade account. Each dictionary\n", "          will include the following elements\n", "            - account_number : the account number, as visible on the front-end\n", "            - account_id_key : the account ID key, which will be used to identify the\n", "                               account on the back-end of the API\n", "        '''\n", "        \n", "        # Run the request\n", "        response = self._query(url='/v1/accounts/list.json', method='GET')\n", "        \n", "        # Get the accounts\n", "        response = response['AccountListResponse']['Accounts']['Account']\n", "        \n", "        # Return\n", "        return [{'account_number' : str(i['accountId']),\n", "                 'account_id_key' : str(i['accountIdKey'])}\n", "                                            for i in response]\n", "    \n", "    def get_cash_balance(self, account_id_key):\n", "        '''Returns the cash balance in a specified account\n", "        \n", "        eTrade API docs: https://apisb.etrade.com/docs/api/account/api-balance-v1.html\n", "        \n", "        Arguments\n", "        ---------\n", "          - account_id_key (string) : an account ID key, obtained from\n", "                                      list_accounts\n", "          \n", "        Returns\n", "        -------\n", "          The cash balance in the said account\n", "        '''\n", "        \n", "        # Run the request\n", "        response = self._query(url    = '/v1/accounts/' + account_id_key + \"/balance.json\",\n", "                               method = 'GET',\n", "                               params = {'instType':'BROKERAGE', 'realTimeNAV':True})\n", "        \n", "        # Return\n", "        return response['BalanceResponse']['Computed']['cashAvailableForInvestment']\n", "    def get_positions(self, account_id_key):\n", "        '''Returns all positions in a specified account\n", "        \n", "        eTrade API docs: https://apisb.etrade.com/docs/api/account/api-portfolio-v1.html\n", "        \n", "        This function does *not* return lot details, or any information about\n", "        purchase date, purchase prices, etc... This is because in portfolios with\n", "        *many* positions, it will take a long time to download all lot data. If we\n", "        were to do all that in this function, and something goes wrong during the\n", "        process, all the data would be lost. Instead, we provide a get_lots function,\n", "        which can be called for each position in \n", "        \n", "        Note that by default, this function errors out if it finds any securities\n", "        that are not equities, and any positions that are not LONG positions. This\n", "        is because these are the only securities I had in my portfolio, and\n", "        therefore the only securities I could test the script on.\n", "        \n", "        Arguments\n", "        ---------\n", "          - account_id_key (string) : an account ID key, obtained from\n", "                                      list_accounts\n", "        \n", "        Returns\n", "        -------\n", "          A list of dictionaries, each containing one position. Each dictionary\n", "          will include the following elements\n", "            - position_id      : the eTrade position ID to identify the position\n", "            - symbol           : the stock ticker\n", "            - market_value     : the current market value of the position\n", "            - quantity         : the number of shares owned\n", "            - sub_type         : the securitySubType from the eTrade API\n", "            - lot_details_url  : an eTrade API link to obtain details of all lots for\n", "                                 this position\n", "          \n", "          Note that the dictionary does not contain any information about the\n", "          purchase price, purchase date, etc..., because there may be multiple\n", "          tax lots in each positions. These lot details have to be obtained\n", "          separately using get_lots.\n", "        '''\n", "        \n", "        positions = []\n", "        \n", "        # Download all transactions; eTrade uses a \"paging\" system; if the response\n", "        # contains a nextPageNo element, there's another page\n", "        response = {'nextPageNo':1}\n", "        while response.get('nextPageNo', None):\n", "            response = self._query(url    = '/v1/accounts/' + account_id_key + '/portfolio.json',\n", "                                   method = 'GET',\n", "                                   params = {'pageNumber' : response['nextPageNo']})\n", "            \n", "            response = response['PortfolioResponse']['AccountPortfolio'][0]\n", "            \n", "            for position in response['Position']:\n", "                assert position['positionType']            == 'LONG'\n", "                assert position['Product']['securityType'] == 'EQ'\n", "                \n", "                # Ensure the lotsDetails ulr begins with self._base_url, and remove\n", "                # it from the start of the request, because it will be added back\n", "                # later when we run it through .query\n", "                assert position['lotsDetails'].startswith(self._base_url), ( 'lotsDetails API '\n", "                                      'url does not begin with the base URL given in the class.' )\n", "                lots_details = position['lotsDetails'][len(self._base_url):]\n", "                \n", "                positions.append({'position_id'     : str(position['positionId']),\n", "                                  'symbol'          : position['symbolDescription'],\n", "                                  'market_value'    : position['marketValue'],\n", "                                  'quantity'        : position['quantity'],\n", "                                  'sub_type'        : position['Product'].get('securitySubType', None),\n", "                                  'lot_details_url' : lots_details})\n", "        \n", "        return positions\n", "            \n", "    def get_lots(self, lot_details_url):\n", "        '''Returns all lots making up a specific position\n", "        \n", "        eTrade API docs: https://apisb.etrade.com/docs/api/account/api-portfolio-v1.html#/definitions/PositionLot\n", "        \n", "        Arguments\n", "        ---------\n", "          - lot_details_url : a URL for the specific API request to get the lots,\n", "                              from get_positions\n", "        \n", "        Returns\n", "        -------\n", "          A list of dictionaries, each containing one lot. Each dictionary\n", "          will include the following elements\n", "            - position_id     : the ID of the position this lot is part of\n", "            - position_lot_id : the ID of the lot\n", "            - symbol          : the stock ticker for the security in question\n", "            - price           : the unit price at which securities in this tax lot were\n", "                                bought\n", "            - market_value    : the current market value of the tax lot\n", "            - quantity        : the quantity of the security in the lot\n", "            - acquired_date   : the date on which the securities in this lot were acquired;\n", "                                I've found this is sometimes missing when <PERSON><PERSON><PERSON> doesn't\n", "                                have this data\n", "        \n", "        '''\n", "        \n", "        lots = []\n", "        \n", "        response = self._query(url    = lot_details_url,\n", "                               method = 'GET')['PositionLotsResponse']['PositionLot']\n", "        \n", "        for l in response:\n", "            lots.append({'position_id'      : str(l['positionId']),\n", "                         'position_lot_id'  : str(l['positionLotId']),\n", "                         'price'            : l['price'],\n", "                         'market_value'     : l['marketValue'],\n", "                         'quantity'         : l['remainingQty'],\n", "                         'acquired_date'    : l.get('acquiredDate', None)})\n", "        \n", "        return lots\n", "    \n", "    def get_recent_trades(self, account_id_key, window = 31):\n", "        '''Retrieves all securities bought or sold in the last window days\n", "        \n", "        eTrade API docs: https://apisb.etrade.com/docs/api/order/api-order-v1.html#/definition/getOrders\n", "        \n", "        Arguments\n", "        ---------\n", "          - account_id_key (string) : an account ID key, obtained from\n", "                                      list_accounts\n", "          - window (integer)        : the number of days over which to download\n", "                                      orders. By default, this will be 31, to retrieve\n", "                                      all orders that are relevant in determining\n", "                                      whether we have a wash sale\n", "        \n", "        Returns\n", "        -------\n", "          A dictionary with two entries\n", "            - bought : a list of all security tickers that were bought in the\n", "                       window\n", "            - sold   : a list of all security tickers that were sold in the window\n", "        '''\n", "        \n", "        out = {'bought':[], 'sold':[]}\n", "        \n", "        # Create a parameters dictionary\n", "        params =  {'count'    : 100,\n", "                  'fromDate' : (datetime.datetime.today() - datetime.timedelta(days=window)).strftime('%m%d%Y'),\n", "                  'toDate'   : (datetime.datetime.today() + datetime.timedelta(days=1)).strftime('%m%d%Y')}\n", "\n", "        # Download all orders; eTrade uses a \"paging\" system; if the response\n", "        # contains a marker element, there's another page\n", "        response = {'marker':0}\n", "        while 'marker' in response:\n", "            params['marker'] = response['marker']\n", "            response = self._query(f'/v1/accounts/{account_id_key}/orders.json',\n", "                                   method = 'GET',\n", "                                   params = params)\n", "            \n", "            if response is not None:\n", "                response = response['OrdersResponse']\n", "                for t in response['Order']:\n", "                    if t['OrderDetail'][0]['status'] != 'CANCELLED':\n", "                        order_action = t['OrderDetail'][0]['Instrument'][0]['orderAction']\n", "                        symbol = t['OrderDetail'][0]['Instrument'][0]['Product']['symbol']\n", "                        \n", "                        if order_action == 'BUY':\n", "                            out['bought'].append(symbol)\n", "                        elif order_action == 'SELL':\n", "                            out['sold'].append(symbol)\n", "                        else:\n", "                            raise 'Unknown order action found'\n", "            else:\n", "                response = {}\n", "                \n", "        # Remove duplicates\n", "        out = {i:list(set(out[i])) for i in out}\n", "        \n", "        return out\n", "    \n", "    def get_current_price(self, symbol):\n", "        '''Gets the current price (average of bid and ask) for a security\n", "        \n", "        eTrade API docs: https://apisb.etrade.com/docs/api/market/api-quote-v1.html\n", "        \n", "        Arguments\n", "        ---------\n", "          - symbol (string) : the ticker for the security\n", "       \n", "        Returns\n", "        -------\n", "          A single float, with the price\n", "        '''\n", "        \n", "        response = ( self._query(url    = f'/v1/market/quote/{symbol}.json',\n", "                                 method = 'GET')\n", "                                 ['QuoteResponse']['QuoteData'][0]['All'] )\n", "        \n", "        return (( response['bid'] + response['ask'] )/2)\n", "    def execute_order(self, account_id_key, symbol, price_type, order_action, quantity,\n", "                            expected_cost = None, limit_price = None, lots = None,\n", "                                preview_only=False, preview_result=None):\n", "        '''Executes an order, WARNING: this by-passes eTrade's requirement to preview orders unless preview_only=True\n", "        \n", "        eTrade API docs: https://apisb.etrade.com/docs/api/order/api-order-v1.html#/definition/orderPreview\n", "                         https://apisb.etrade.com/docs/api/order/api-order-v1.html#/definition/orderPlace\n", "        \n", "        This function will take an order, preview it using the eTrade API, and\n", "        then immediately execute it.\n", "        \n", "        If preview_only=False, the order will be placed immediately. If preview_only=True,\n", "        the order will be previewed in the eTrade API, and the estimated proceeds will be\n", "        returned as well as a preview ID that can then be used to execute the order\n", "        \n", "        *Important note* : In an ideal world, it'd be possible to place multiple orders\n", "        using ONE API call, to reduce latency between orders and reduce the chance\n", "        market conditions could change between calls. I spent a while trying to figure\n", "        out if that was possible, but failed; it's unclear to me whether this is because\n", "        the API doesn't allow it, or because I can't figure out the syntax. Here's\n", "        evidence in each direction\n", "          - Evidence it might be possible: the API states that the \"instrument\" argument\n", "            is an array, and the BUY vs. SELL instruction is inside that instrument\n", "            object; in other words it might be possible to include two instruments with\n", "            two different shares, one buy, one sell\n", "          - Evidence it might not be possible: the limit_price argument is *outside* that\n", "            instrument array, so the limit_price would have to be shared across all\n", "            instruments\n", "        \n", "        Arguments\n", "        ---------\n", "          - symbol (string)       : the ticker of the security to trade\n", "          - price_type (string)   : MARKET or LIMIT\n", "          - order_action (string) : BUY or SELL\n", "          - quantity (integer)    : the number of securities to buy\n", "          - expected_cost (float) : the expected cost of this order; positive if this is a\n", "                                    buy, negative if this is a sell. If the estimated proceeds\n", "                                    deviate by more than a fraction TOLERANCE from this number,\n", "                                    an exception is raised. If this is omitted, no checks will\n", "                                    be done\n", "          - limit_price (float)   : limit price, if the order is LIMIT\n", "          - lots (list)           : for sell orders only, a dictionary indicating the tax lots\n", "                                    to sell from. Each of these dictionaries will contain two\n", "                                    entries; position_lot_id and quantity. The sum of quantities\n", "                                    in the dictionaries should be equal to the overall quantity.\n", "                                    If omitted, eTrades default selling priority will be used to\n", "                                    decide what lots to sell from.\n", "          - preview_only (bool)   : If True, the order is previewed, but not executed\n", "          - preview_result (dict) : If this order was previously previewed and now needs to be\n", "                                    executed, this argument should contain the preview result\n", "                                    that was returned when the order was previewed\n", "        \n", "        Returns\n", "        -------\n", "          A dictionary with one or more elements:\n", "            - estimated_cost : the estimated cost of this order (negative for a sale)\n", "                               returned by eTrade\n", "            - messages       : Only returned if preview_only=False. Any messages in the\n", "                               eTrade response\n", "            - outcome        : Only returned if preview_only=False. Equal to 'PLACED' if\n", "                               the order was successfully placed, 'QUEUED' if the order was\n", "                               queued (for example, if the order was placed outside market\n", "                               hours), and None if we can't determine what happened\n", "            - preview_result : Only returned if preview_only=True; this will contain\n", "                               the preview result which can then be used to execute the\n", "                               trade\n", "                               \n", "        '''\n", "        \n", "        # Validate inputs\n", "        # ---------------\n", "        if order_action not in ['BUY', 'SELL']:\n", "            raise ValidationError('order_action must be BUY or SELL', order_action)\n", "        if price_type not in ['MARKET', 'LIMIT']:\n", "            raise ValidationError('price_type must be MARKET or LIMIT', price_type)\n", "        if (price_type == 'LIMIT') and (limit_price is not None):\n", "            raise ValidationError('limit_price must be provided for a limit order', limit_price)\n", "        if ( (order_action == 'SELL')\n", "                    and (lots is not None)\n", "                        and quantity != sum([i['quantity'] for i in lots]) ) :\n", "            raise ValidationError('Quantity provided in lots does not match total quantity', lots)\n", "        \n", "        # Step 1; create the order payload\n", "        # --------------------------------\n", "        \n", "        # Create a random order ID, unless it was already generated during\n", "        # a preview\n", "        if preview_result is not None:\n", "            client_order_id = preview_result[1]\n", "        else:\n", "            client_order_id = str(random.randint(1000000000, 9999999999))[:8]\n", "        \n", "        order = {'allOrNone'     : 'true',\n", "                 'priceType'     : price_type,\n", "                 'orderTerm'     : 'GOOD_FOR_DAY',\n", "                 'marketSession' : 'REGULAR',\n", "                 'stopPrice'     : None,\n", "                 'limitPrice'    : limit_price,\n", "                 'Instrument'    : {'Product'      : {'securityType' : 'EQ', 'symbol' : symbol},\n", "                                     'orderAction'  : order_action,\n", "                                     'quantityType' : 'QUANTITY',\n", "                                     'quantity'     : quantity} }\n", "        \n", "        if (order_action == 'SELL') and (lots is not None):\n", "            order['Instrument']['Lots'] = [{'Lot':{'id':i['position_lot_id'], 'size':i['quantity']}}\n", "                                                                                        for i in lots]\n", "               \n", "        order_payload =  {'orderType'     : 'EQ',\n", "                          'clientOrderId' : client_order_id,\n", "                          'Order'         : order}       \n", "        \n", "        # Create query headers\n", "        headers = {'Content-Type': 'application/xml', 'consumerKey': self._config['CONSUMER_KEY']}\n", "        \n", "        # Step 2; preview the order\n", "        # -------------------------\n", "        \n", "        # Build the preview payload; note that in theory, we should be able to pass\n", "        # the dictionary directly to the request, but for some reason it doesn't\n", "        # seem to work and the eTrade API is like a surly teenager - it doesn't tell\n", "        # you *what* went wrong, it just tells you there's an error. I tried as hard\n", "        # as possible to debug this, but I was quickly losing the will to live and\n", "        # gave up - instead, I'm first converting the dictionary to XML, and then\n", "        # passing that to <PERSON><PERSON><PERSON>, which seems to work beter\n", "    \n", "        # Convert the json to XML\n", "        xml_payload = self._dict_to_xml({'PreviewOrderRequest' : order_payload})\n", "    \n", "        if preview_result is None:\n", "            # Run the query\n", "            try:\n", "                response = self._query(url     = '/v1/accounts/' + account_id_key + '/orders/preview.json',\n", "                                       method  = 'POST',\n", "                                       headers = headers,\n", "                                       data    = xml_payload)\n", "            except <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "                # Check whether the error arises from lack of funds\n", "                try:\n", "                    e_json = json.loads(e.response)\n", "                    \n", "                    code = e_json['Error']['code']\n", "                    message = e_json['Error']['message']\n", "                except:\n", "                    raise e\n", "                    \n", "                if (code == 8400) and ('This order cannot be accepted due to insufficient funds in your account' in message):\n", "                    raise OrderPreviewError('The account does not contain enough funds to preview this order')\n", "                else:\n", "                    raise e\n", "            \n", "            # Get the estimated cost and preview ID\n", "            estimated_cost = response['PreviewOrderResponse']['Order'][0]['estimatedTotalAmount']\n", "            preview_id = response[\"PreviewOrderResponse\"][\"PreviewIds\"][0]['previewId']\n", "            \n", "            # If an expected_cost was provided, check it matches the estimated cost\n", "            if expected_cost is not None:\n", "                if np.abs(expected_cost - estimated_cost)/np.abs(expected_cost) > TOLERANCE:\n", "                    raise ExpectedCostError(expected_cost, estimated_cost)\n", "            \n", "            # If we're looking for a preview only, return\n", "            if preview_only:\n", "                # Create a preview_result, a tuple in which the first entry is\n", "                # the preview_id, the second is the order_id, and the third is the\n", "                # xml_payload; the reason we need to do this is that - astonishingly \n", "                # - when a preview ID is used to place an order, eTrade doesn't\n", "                # check that the order that was used to *generate* the preview ID is\n", "                # the same as the order that is *now being placed*. As such, we\n", "                # return both the preview ID and the order that was used to preview\n", "                # it, and then we'll check it later\n", "                preview_result = (preview_id, client_order_id, xml_payload)\n", "            \n", "                return {'estimated_cost' : estimated_cost,\n", "                        'preview_result' : preview_result}\n", "        \n", "        else:\n", "            # Check that the preview provided was created based on the same\n", "            # order (see the comment above)\n", "            if preview_result[2] != xml_payload:\n", "                raise OrderPreviewError('The preview result was obtained using a different order')\n", "            \n", "            preview_id = preview_result[0]\n", "        \n", "        # Step 3; execute the order\n", "        # -------------------------\n", "        \n", "        # Add the preview ID to the order to allow eTrade to execute it\n", "        order_payload['PreviewIds'] = {'previewId' : preview_id}\n", "        \n", "        # Create the payload\n", "        xml_payload = self._dict_to_xml({'PlaceOrderRequest' : order_payload})\n", "        \n", "        # Run it\n", "        try:\n", "            response = self._query(url     =  '/v1/accounts/' + account_id_key + '/orders/place.json',\n", "                                   method  = 'POST',\n", "                                   headers = headers,\n", "                                   data    = xml_payload)\n", "        except <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "            # Check whether the error arises from the preview having timed out\n", "            try:\n", "                e_json = json.loads(e.response)\n", "                \n", "                code = e_json['Error']['code']\n", "                message = e_json['Error']['message']\n", "            except:\n", "                raise e\n", "            \n", "            if (code == 1033) and ('we have timed out your original' in message):\n", "                raise OrderPreviewError('The preview has timed out; the order cannot be placed')\n", "            else:\n", "                raise e\n", "        \n", "        # Step 4; return\n", "        # --------------\n", "        messages = response['PlaceOrderResponse']['Order'][0]['messages']['Message']\n", "        message_codes = [m['code'] for m in messages]\n", "        message_descs = [m['description'] for m in messages]\n", "        \n", "        # Attempt to figure out what happened\n", "        if 1026 in message_codes:\n", "            outcome = 'PLACED'\n", "        elif 1027 in message_codes:\n", "            outcome = 'QUEUED'\n", "        else:\n", "            outcome = None\n", "        \n", "        # Return\n", "        return {'estimated_cost' : response['PlaceOrderResponse']['Order'][0]['estimatedTotalAmount'],\n", "                'messages'       : ' & '.join(message_descs),\n", "                'outcome'        : outcome}\n", "        \n", "class Account:\n", "    '''eTrade account\n", "    \n", "    This class contains all the positions and lot details for a specific\n", "    eTrade account.\n", "    \n", "    It should be initialized with a EtradeConnection object, which will be\n", "    used to download all the account information from the api.\n", "    \n", "    Properties\n", "    ----------\n", "      - df_positions   : a DataFrame of all positions in the account\n", "      - df_lots        : detailed tax lots information for all positions in the\n", "                         acccount\n", "      - cash           : amount of cash in the account\n", "      - recent_trades  : securities bought and sold in the last 30 days\n", "    '''\n", "    \n", "    def __init__(self, account_number = None, conn = None, validation_folder=None):\n", "        '''Initialize an eTrade account and download all the securities therein\n", "        \n", "        I've found the API to - very rarely - be buggy, so to ensure portfolio\n", "        information is accurate, it can be compared to a downloaded version\n", "        from the eTrade front end (obtained as described below) as an additional\n", "        integrity check, to make sure the data we're working with is accurate.\n", "        \n", "        Arguments\n", "        ---------\n", "          - account_number (string)    : the account number to be retrieved\n", "          - conn (EtradeConnection)    : an EtradeConnection object that can be used to run\n", "                                         queries against the Etrade API\n", "          - validation_folder (string) : the path of a folder containing CSVs downloaded from the eTrade\n", "                                         front-end by clicking on \"View full portfolio\" on the overview\n", "                                         page, and clicking on the down arrow at the top-right-hand corner\n", "                                         of the resulting page. The portfolio obtained through the API will\n", "                                         be compared to the last file in that folder alphabetically by file\n", "                                         name (so for example, naming files using the convention\n", "                                         YYYY-MM-DD HH-MM.csv will always lead the last file to be the latest\n", "                                         file) and if any of the assets or quantities are different, an error\n", "                                         will be thrown. Also, if any of the market values diverge by more\n", "                                         than 2*TOLERANCE, an error will be thrown.\n", "                                         \n", "                                         This is added in as an extra check. No check will be done if this\n", "                                         argument is None\n", "        '''\n", "                \n", "        self._account_number = account_number\n", "        \n", "        if account_number is None:\n", "            print('Using the sample portfolio')\n", "            return\n", "        \n", "        self._conn = conn\n", "        self._validation_folder = validation_folder\n", "        \n", "        # Step 1; find the account\n", "        # ------------------------\n", "        print('Finding account')\n", "        \n", "        account = [i for i in self._conn.list_accounts() if i['account_number'] == self._account_number]\n", "        assert len(account) == 1, 'Account number provided was not found'\n", "        self._account_id_key = account[0]['account_id_key']\n", "        \n", "        # Step 2; download all positions and lots\n", "        # ---------------------------------------\n", "        print('Downloading positions and lots')\n", "        \n", "        self._download_data()\n", "        \n", "        # Step 3; parse positions and lots\n", "        # --------------------------------\n", "        print('Parsing lots and positions data')\n", "        \n", "        self._parse_data()\n", "        \n", "        # Step 4; validate with downloaded portfolios\n", "        # -------------------------------------------\n", "        \n", "        if self._validation_folder is not None:\n", "            print('Comparing to downloaded portfolio')\n", "                \n", "            self._validate_data()\n", "        \n", "        # Done\n", "        print('Portfolio downloaded and parsed')\n", "        \n", "    def _download_data(self):\n", "        '''Download position and lot data for this account\n", "        \n", "        This function takes no arguments and returns nothing, but it sets the\n", "        self._raw_positions, self._raw_lots, and self._recent_trades variables\n", "        '''\n", "        \n", "        self._raw_positions = self._conn.get_positions(self._account_id_key)\n", "        \n", "        self._raw_lots = []\n", "        \n", "        for p in tqdm(self._raw_positions):\n", "            self._raw_lots.extend(self._conn.get_lots(p['lot_details_url']))\n", "        self._recent_trades = self._conn.get_recent_trades(self._account_id_key)\n", "        \n", "        self._cash = self._conn.get_cash_balance(self._account_id_key)\n", "    def _parse_data(self):\n", "        '''Parse downloaded position data for this account\n", "        \n", "        This function takes no arguments and returns nothing, but it sets the\n", "        self._df_lots and  self._df_positions variables\n", "        '''\n", "    \n", "        # Step 1 - convert data to Pandas\n", "        # -------------------------------\n", "        self._df_positions = pd.DataFrame(self._raw_positions).drop(columns='lot_details_url')\n", "        self._df_lots = pd.DataFrame(self._raw_lots).assign(gain = lambda x : x.market_value - (x.price*x.quantity))\n", "        \n", "        # Convert the unix timestamps to dates (note: the field returned by the API\n", "        # only provides date, not time, so we strip time)\n", "        self._df_lots.acquired_date = pd.to_datetime(self._df_lots.acquired_date, unit='ms').dt.date\n", "                        \n", "        # Step 2 - add a symbol column to the lots\n", "        # ----------------------------------------\n", "        assert self._df_positions.position_id.duplicated().sum() == 0, 'Some position IDs were downloaded twice'\n", "        \n", "        n_lots = len(self._df_lots)\n", "        self._df_lots = pd.merge(self._df_lots,\n", "                                 self._df_positions[['position_id', 'symbol']],\n", "                                 how      = 'left',\n", "                                 validate = 'many_to_one')\n", "        assert len(self._df_lots) == n_lots, 'Error adding symbol to lots; rows created or dropped'\n", "        \n", "        # Step 3 - ensure the sum of the lots matches the positions\n", "        # ---------------------------------------------------------\n", "        aggregated_lots = self._df_lots.groupby('position_id')[['quantity', 'market_value']].sum().reset_index()\n", "        \n", "        compare = pd.merge(self._df_positions[['position_id', 'market_value', 'quantity']],\n", "                           aggregated_lots.rename(columns={'quantity':'lot_quantity', 'market_value':'lot_market_value'}),\n", "                           how='outer',\n", "                           validate = 'one_to_one')\n", "       \n", "        # Ensure nothing was missing on either side\n", "        assert compare.market_value.isnull().sum() + compare.lot_market_value.isnull().sum() == 0, 'Aggregated lot comparisson failed'\n", "        \n", "        # Ensure the quantities match\n", "        assert (compare.quantity != compare.lot_quantity).sum() == 0, 'Some lot quantities do not match positions'\n", "        assert (((compare.market_value - compare.lot_market_value).abs() / compare.market_value) > 0.01).sum() == 0, 'Some lot values do not match positions'\n", "        \n", "        # Step 4 - add gain and loss summaries to the positions\n", "        # -----------------------------------------------------\n", "        for col_name, query in (('lots_loss', 'gain < 0'), ('lots_gain', 'gain > 0')):\n", "            self._df_positions = pd.merge(self._df_positions,\n", "                                          self._df_lots\n", "                                              .query(query)\n", "                                              .groupby('position_id')\n", "                                              .gain\n", "                                              .sum()\n", "                                              .reset_index()\n", "                                              .rename(columns = {'gain':col_name}),\n", "                                          how      = 'left',\n", "                                          validate = 'one_to_one')\n", "            \n", "            self._df_positions[col_name] = self._df_positions[col_name].fillna(0)\n", "        \n", "        # Step 5 - re-order columns and set indexes\n", "        # -----------------------------------------\n", "        self._df_positions = self._df_positions[['position_id', 'symbol', 'market_value',\n", "                                                            'quantity', 'sub_type', 'lots_loss',\n", "                                                                'lots_gain']].set_index('symbol').copy()\n", "        self._df_lots = self._df_lots[['position_id', 'position_lot_id', 'symbol', 'acquired_date',\n", "                                                            'price', 'market_value', 'quantity', 'gain']].copy()\n", "        \n", "        # Warn about fractional lots\n", "        # --------------------------\n", "        if (self._df_lots.quantity != self._df_lots.quantity.astype(int)).sum() > 0:\n", "            display(self._df_lots[self._df_lots.quantity != self._df_lots.quantity.astype(int)])\n", "            print('WARNING: some of your lots contain a fractional number of shares. eTrade does not'  '\\n'\n", "                  '         allow the purchase of fractional shares, so these probably come from a'    '\\n'\n", "                  '         transfer from another broker. eTrade apparently only allows the sale of'   '\\n'\n", "                  '         fractional shares when the entire position is liquidated, so you may'      '\\n'\n", "                  '         encounter some trouble when the algorithm tries to trade.'               '\\n\\n'\n", "                  '         See the comment in the code for potential ways to get rid of these lots.')\n", "            \n", "            # Potential way to get rid of fractional lots\n", "            #   - If you are able to bunch together multiple lots in a way that the sum of quantities\n", "            #     is an integer, you can sell that integer number of shares from the front-end and\n", "            #     AFTER \"previewing\" the order, you can select the fractional lots as the ones to\n", "            #     share\n", "            #   - If the sum of all the fractional lots is fractional, you're out of luck. The only\n", "            #     option I can think of (I haven't tried myself) is to transfer those fractional lots\n", "            #     to a broker that does allow the sale\n", "    \n", "    def _validate_data(self):\n", "        '''Compare API data to data downloaded from the front-end\n", "        \n", "        See __init__ function for a description of this comparisson\n", "        \n", "        Will raise an error if the API portfolio deviates from the front-end portfolio in\n", "        ways described in __init__\n", "        '''\n", "        \n", "        # Identify the latest file in the folder\n", "        f_name = os.path.join(self._validation_folder, max(os.listdir(self._validation_folder)))\n", "        \n", "        # Step 1 - read the file\n", "        # ----------------------\n", "        # The format is super weird, but the code below reads it (hopefully) correctly\n", "        \n", "        with open(f_name, 'r') as f:\n", "            # Read the file, skipping the first 10 lines, and splitting each row\n", "            # on commas\n", "            file = [i.split(',') for i in f.read().split('\\n')[10:]]\n", "        parsed_file = []\n", "        headers     = file[0]\n", "        for i in file[1:]:\n", "            # While we have 10 columns, we have holdings. The last row has 11\n", "            # columns and it contains cash holdings\n", "            if len(i) == 10:\n", "                parsed_file.append(i)\n", "            else:\n", "                # Ensure this row contains cash holdings\n", "                assert i[0] == 'CASH'\n", "                cash_holdings = float(i[-2])\n", "                break\n", "        \n", "        # Put the raw data into a DataFrame\n", "        df_web = ( pd.DataFrame(parsed_file, columns=headers)\n", "                     .rename(columns = {'Symbol'       : 'symbol',\n", "                                        'Quantity'     : 'quantity_web',\n", "                                        'Value $'      : 'market_value_web'})\n", "                     [['symbol', 'quantity_web', 'market_value_web']] )\n", "        \n", "        for col in ['quantity_web', 'market_value_web']:\n", "            df_web[col] = df_web[col].astype(float)\n", "\n", "        # Step 2 - compare to the downloaded data\n", "        # ---------------------------------------\n", "        \n", "        # Ensure no position is missing from either side\n", "        if set(self.df_positions.index) != set(df_web.symbol):\n", "            print(f'Securities present in the API but not on the front end: {set(self.df_positions.symbol) - set(df_web.symbol)}')\n", "            print(f'Securities present in the front end but not on the API: {set(df_web.symbol) - set(self.df_positions.symbol)}')\n", "            raise\n", "            \n", "        # Compare the quantities\n", "        comp = pd.merge(self.df_positions.reset_index(),\n", "                        df_web,\n", "                        how      = 'inner',\n", "                        validate = 'one_to_one')\n", "        \n", "        quantity_mismatch = (comp.quantity != comp.quantity_web)\n", "        value_mismatch = ((comp.market_value - comp.market_value_web).abs()/comp.market_value_web >= TOLERANCE*2)\n", "        \n", "        if quantity_mismatch.sum() > 0:\n", "            print('Some quantities do not match in the front-end and the API')\n", "            print(comp[quantity_mismatch])\n", "            raise\n", "            \n", "        if value_mismatch.sum() > 0:\n", "            print('Some market values deviate by more than 2% between the front-end and the API')\n", "            print(comp[value_mismatch])\n", "            raise\n", "    \n", "    @property\n", "    def df_positions(self):\n", "        '''Returns a DataFrame containing the positions in this portfolio\n", "        \n", "        Note: a copy is returned to avoid inadvertent modification\n", "        \n", "        Columns\n", "        -------\n", "            INDEX          : the ticker for the security\n", "          - position_id    : unique identifier for the position\n", "          - market_value\n", "          - quantity\n", "          - sub_type       : eTrade subtype (eg: \"ETF\")\n", "          - lots_loss      : the sum of losses over all losing tax lots\n", "          - lots_gain      : the sum of gains over all tax lots that experienced a gain\n", "        '''\n", "        \n", "        if self._account_number is None:\n", "            df = pd.DataFrame(eval(\"{'position_id': {'IEMG': '************', 'VIG': '************', 'VO': '************', 'VTEB': '************', 'VV': '************', 'VEA': '************', 'SUB': '************', 'SCHA': '************', 'SCHD': '************', 'SCHF': '************', 'SCHH': '************', 'VWO': '************'}, 'market_value': {'IEMG': 33403.7197, 'VIG': 28958.38, 'VO': 145373.***********, 'VTEB': 74972.838, 'VV': 470786.57, 'VEA': 41470.************, 'SUB': 30534.57, 'SCHA': 59653.4757, 'SCHD': 13809.************, 'SCHF': 251951.68, 'SCHH': 39863.2, 'VWO': 11938.************}, 'quantity': {'IEMG': 743, 'VIG': 194, 'VO': 713, 'VTEB': 1570, 'VV': 2699, 'VEA': 1032, 'SUB': 297, 'SCHA': 1443, 'SCHD': 184, 'SCHF': 8117, 'SCHH': 2080, 'VWO': 318}, 'sub_type': {'IEMG': 'ETF', 'VIG': 'ETF', 'VO': 'ETF', 'VTEB': 'ETF', 'VV': 'ETF', 'VEA': 'ETF', 'SUB': 'ETF', 'SCHA': 'ETF', 'SCHD': 'ETF', 'SCHF': 'ETF', 'SCHH': 'ETF', 'VWO': 'ETF'}, 'lots_loss': {'IEMG': -283.448699999999, 'VIG': -178.71999999999989, 'VO': 0.0, 'VTEB': -1101.5119999999974, 'VV': 0.0, 'VEA': 0.0, 'SUB': -551.1618000000001, 'SCHA': 0.0, 'SCHD': 0.0, 'SCHF': 0.0, 'SCHH': 0.0, 'VWO': -721.1167194444447}, 'lots_gain': {'IEMG': 1022.7101999999986, 'VIG': 9816.759999999998, 'VO': 8279.99758096829, 'VTEB': 0.0, 'VV': 33691.91149999999, 'VEA': 5650.3730000000005, 'SUB': 0.0, 'SCHA': 3580.2272999999996, 'SCHD': 3022.5441041666663, 'SCHF': 22087.80650000002, 'SCHH': 2277.8079999999986, 'VWO': 131.79452464646496}}\"))\n", "            \n", "            df.index.name = 'symbol'\n", "            \n", "            return df\n", "        \n", "        return self._df_positions.copy()\n", "    @property\n", "    def df_lots(self):\n", "        '''Returns a DataFrame containing the lots in this portfolio\n", "        \n", "        Note: a copy is returned to avoid inadvertent modification\n", "        \n", "        Columns\n", "        -------\n", "          - position_id     : matches to a specific position in df_positions\n", "          - position_lot_id : lot ID\n", "          - symbol\n", "          - acquired_date   : date acquired\n", "          - price           : unit price when purchased\n", "          - market_value    : total market value of the lot today\n", "          - quantity        : quantity in lot\n", "          - gain            : total gain (+ve) or loss (-ve) on lot\n", "          \n", "        \n", "        '''\n", "        \n", "        if self._account_number is None:\n", "            return pd.<PERSON><PERSON><PERSON><PERSON>(eval(\"{'position_id': {0: '************', 1: '************', 2: '************', 3: '************', 4: '************', 5: '************', 6: '************', 7: '************', 8: '************', 9: '************', 10: '************', 11: '************', 12: '************', 13: '************', 14: '************', 15: '************', 16: '************', 17: '************', 18: '************', 19: '************', 20: '************', 21: '************', 22: '************', 23: '************', 24: '************', 25: '************', 26: '************', 27: '************', 28: '************', 29: '************', 30: '************', 31: '************', 32: '************', 33: '************', 34: '************', 35: '************', 36: '************', 37: '************', 38: '************', 39: '************', 40: '************', 41: '************', 42: '************', 43: '************', 44: '************', 45: '************', 46: '************', 47: '************', 48: '************', 49: '************', 50: '************', 51: '************', 52: '************', 53: '************', 54: '************', 55: '************', 56: '************', 57: '************', 58: '************', 59: '************', 60: '************', 61: '************', 62: '************', 63: '************', 64: '************', 65: '************', 66: '************', 67: '************', 68: '************', 69: '************', 70: '************', 71: '************', 72: '************', 73: '************', 74: '************', 75: '************', 76: '************', 77: '************', 78: '************', 79: '************', 80: '************', 81: '************', 82: '************', 83: '************', 84: '************'}, 'position_lot_id': {0: '494715020211', 1: '153710106948', 2: '458239829796', 3: '764195865158', 4: '264242557407', 5: '257906580532', 6: '578396236457', 7: '578644828386', 8: '670960862695', 9: '864488614669', 10: '752009792373', 11: '649921159609', 12: '750199044312', 13: '390663022467', 14: '425609790059', 15: '305436907790', 16: '364342641749', 17: '667878511468', 18: '182894445950', 19: '490331055411', 20: '487776486996', 21: '544316587884', 22: '483247261265', 23: '381035100674', 24: '483716176266', 25: '904050246804', 26: '949744016382', 27: '551653008295', 28: '661557656612', 29: '204056555571', 30: '385556933637', 31: '473343590757', 32: '879678242094', 33: '325409828856', 34: '534730837835', 35: '987003807048', 36: '567536607333', 37: '651605073186', 38: '208565799391', 39: '843706720455', 40: '642754115569', 41: '590561205819', 42: '408487450396', 43: '373708710124', 44: '475319989921', 45: '713170689212', 46: '887911157614', 47: '559380103729', 48: '702382404665', 49: '627342897305', 50: '662413151885', 51: '707220145888', 52: '858108193857', 53: '174875489499', 54: '787314557298', 55: '319299737082', 56: '274800664520', 57: '615211261741', 58: '186141264951', 59: '896794143646', 60: '664524074845', 61: '751074722370', 62: '114516286025', 63: '634988691499', 64: '601106673154', 65: '243063679730', 66: '237763463612', 67: '725976575893', 68: '386889783743', 69: '722773265977', 70: '598944924745', 71: '450055516710', 72: '932619240651', 73: '857502997220', 74: '421657810014', 75: '139232317419', 76: '374291266069', 77: '458367113725', 78: '734462947405', 79: '995822633829', 80: '420323379145', 81: '786293032406', 82: '633859224905', 83: '722531618829', 84: '236014707113'}, 'symbol': {0: 'IEMG', 1: 'IEMG', 2: 'IEMG', 3: 'IEMG', 4: 'IEMG', 5: 'IEMG', 6: 'IEMG', 7: 'IEMG', 8: 'IEMG', 9: 'IEMG', 10: 'IEMG', 11: 'IEMG', 12: 'VIG', 13: 'VIG', 14: 'VIG', 15: 'VIG', 16: 'VIG', 17: 'VIG', 18: 'VIG', 19: 'VIG', 20: 'VIG', 21: 'VIG', 22: 'VIG', 23: 'VIG', 24: 'VIG', 25: 'VIG', 26: 'VO', 27: 'VTEB', 28: 'VV', 29: 'VV', 30: 'VV', 31: 'VEA', 32: 'VEA', 33: 'VEA', 34: 'SUB', 35: 'SUB', 36: 'SUB', 37: 'SUB', 38: 'SCHA', 39: 'SCHD', 40: 'SCHD', 41: 'SCHD', 42: 'SCHD', 43: 'SCHD', 44: 'SCHD', 45: 'SCHD', 46: 'SCHD', 47: 'SCHD', 48: 'SCHD', 49: 'SCHD', 50: 'SCHD', 51: 'SCHD', 52: 'SCHD', 53: 'SCHD', 54: 'SCHD', 55: 'SCHD', 56: 'SCHD', 57: 'SCHD', 58: 'SCHD', 59: 'SCHD', 60: 'SCHD', 61: 'SCHD', 62: 'SCHD', 63: 'SCHD', 64: 'SCHF', 65: 'SCHF', 66: 'SCHF', 67: 'SCHF', 68: 'SCHF', 69: 'SCHF', 70: 'SCHF', 71: 'SCHH', 72: 'VWO', 73: 'VWO', 74: 'VWO', 75: 'VWO', 76: 'VWO', 77: 'VWO', 78: 'VWO', 79: 'VWO', 80: 'VWO', 81: 'VWO', 82: 'VWO', 83: 'VWO', 84: 'VWO'}, 'acquired_date': {0: datetime.date(2016, 12, 17), 1: datetime.date(2017, 1, 2), 2: datetime.date(2017, 1, 2), 3: datetime.date(2017, 3, 13), 4: datetime.date(2018, 12, 26), 5: datetime.date(2020, 4, 12), 6: datetime.date(2020, 4, 13), 7: datetime.date(2020, 4, 22), 8: datetime.date(2020, 4, 23), 9: datetime.date(2020, 4, 29), 10: datetime.date(2020, 4, 30), 11: datetime.date(2020, 5, 11), 12: datetime.date(2016, 10, 12), 13: datetime.date(2016, 10, 21), 14: datetime.date(2017, 5, 14), 15: datetime.date(2017, 5, 15), 16: datetime.date(2020, 6, 6), 17: datetime.date(2020, 6, 26), 18: datetime.date(2020, 6, 28), 19: datetime.date(2020, 7, 1), 20: datetime.date(2021, 4, 2), 21: datetime.date(2021, 4, 8), 22: datetime.date(2021, 4, 15), 23: datetime.date(2021, 5, 1), 24: datetime.date(2021, 10, 29), 25: datetime.date(2021, 11, 1), 26: datetime.date(2022, 10, 8), 27: datetime.date(2022, 10, 12), 28: datetime.date(2017, 5, 20), 29: datetime.date(2018, 8, 14), 30: datetime.date(2022, 10, 7), 31: datetime.date(2020, 4, 23), 32: datetime.date(2020, 5, 4), 33: datetime.date(2020, 5, 15), 34: datetime.date(2022, 7, 12), 35: datetime.date(2022, 7, 6), 36: datetime.date(2022, 8, 1), 37: datetime.date(2022, 9, 11), 38: datetime.date(2022, 10, 7), 39: datetime.date(2017, 3, 23), 40: datetime.date(2018, 5, 3), 41: datetime.date(2018, 12, 16), 42: datetime.date(2018, 12, 20), 43: datetime.date(2018, 12, 17), 44: datetime.date(2018, 12, 26), 45: datetime.date(2020, 3, 9), 46: datetime.date(2020, 3, 15), 47: datetime.date(2020, 3, 27), 48: datetime.date(2020, 3, 25), 49: datetime.date(2020, 12, 19), 50: datetime.date(2021, 1, 1), 51: datetime.date(2020, 12, 31), 52: datetime.date(2021, 1, 9), 53: datetime.date(2021, 1, 16), 54: datetime.date(2021, 2, 1), 55: datetime.date(2021, 2, 3), 56: datetime.date(2021, 1, 31), 57: datetime.date(2021, 2, 6), 58: datetime.date(2021, 2, 16), 59: datetime.date(2021, 2, 23), 60: datetime.date(2021, 3, 5), 61: datetime.date(2021, 3, 15), 62: datetime.date(2021, 3, 16), 63: datetime.date(2021, 3, 26), 64: datetime.date(2020, 3, 12), 65: datetime.date(2020, 3, 12), 66: datetime.date(2020, 10, 24), 67: datetime.date(2020, 10, 29), 68: datetime.date(2022, 10, 8), 69: datetime.date(2022, 10, 18), 70: datetime.date(2022, 10, 16), 71: datetime.date(2022, 10, 5), 72: datetime.date(2020, 3, 10), 73: datetime.date(2020, 3, 22), 74: datetime.date(2020, 3, 29), 75: datetime.date(2020, 6, 6), 76: datetime.date(2020, 6, 6), 77: datetime.date(2020, 6, 10), 78: datetime.date(2020, 6, 28), 79: datetime.date(2020, 6, 29), 80: datetime.date(2022, 7, 5), 81: datetime.date(2022, 7, 12), 82: datetime.date(2022, 8, 9), 83: datetime.date(2022, 9, 12), 84: datetime.date(2022, 10, 5)}, 'price': {0: 42.6989, 1: 42.54, 2: 42.5387, 3: 45.8386, 4: 46.01, 5: 42.08, 6: 42.89, 7: 42.8891, 8: 42.13, 9: 42.15, 10: 42.3098, 11: 43.22, 12: 81.85, 13: 82.23, 14: 90.97, 15: 90.29, 16: 117.36, 17: 117.12, 18: 115.21, 19: 117.45, 20: 150.04, 21: 150.71, 22: 152.08, 23: 152.63, 24: 164.77, 25: 164.81, 26: 192.2771, 27: 48.455, 28: 108.4359, 29: 129.4898, 30: 166.1999, 31: 34.7099, 32: 34.64, 33: 34.71, 34: 104.685, 35: 104.688, 36: 105.26, 37: 103.9199, 38: 38.8588, 39: 44.24, 40: 48.0, 41: 48.375, 42: 47.69, 43: 46.1, 44: 44.19, 45: 46.29, 46: 43.59, 47: 40.3575, 48: 42.77, 49: 62.8, 50: 63.84, 51: 63.78, 52: 63.32, 53: 66.4, 54: 65.08, 55: 63.61, 56: 64.7693, 57: 66.549, 58: 67.57, 59: 67.92, 60: 69.08, 61: 72.18, 62: 72.09, 63: 72.15, 64: 24.5798, 65: 23.3898, 66: 30.4397, 67: 30.2, 68: 28.495, 69: 28.29, 70: 28.8894, 71: 18.0699, 72: 33.73, 73: 32.46, 74: 33.22, 75: 40.1584, 76: 39.1, 77: 38.72, 78: 39.58, 79: 40.2, 80: 41.5597, 81: 41.5561, 82: 41.14, 83: 40.3295, 84: 36.8099}, 'market_value': {0: 13667.201599999999, 1: 314.7053, 2: 359.6632, 3: 10924.7697, 4: 2967.2213999999994, 5: 44.9579, 6: 44.9579, 7: 449.579, 8: 314.7053, 9: 269.7474, 10: 4001.2531000000004, 11: 44.9579, 12: 10896.71, 13: 149.27, 14: 149.27, 15: 11493.79, 16: 149.27, 17: 447.81000000000006, 18: 298.54, 19: 149.27, 20: 1044.89, 21: 1194.16, 22: 746.35, 23: 1044.89, 24: 298.54, 25: 895.6200000000001, 26: 145373.***********, 27: 74972.838, 28: 21803.75, 29: 20233.88, 30: 428748.94, 31: 41390.55, 32: 40.185, 33: 40.185, 34: 28067.13, 35: 616.86, 36: 616.8599999999999, 37: 1233.72, 38: 59653.4757, 39: 75.05, 40: 75.05, 41: 150.1, 42: 75.05, 43: 450.2999, 44: 1350.8999, 45: 1726.1499041666668, 46: 300.2, 47: 300.2, 48: 525.35, 49: 1576.05, 50: 225.1499, 51: 75.05, 52: 900.5999, 53: 600.4, 54: 900.5999, 55: 75.05, 56: 750.5, 57: 600.4, 58: 825.55, 59: 225.1499, 60: 150.1, 61: 75.05, 62: 825.5499083333334, 63: 975.6498916666667, 64: 2079.68, 65: 24707.84, 66: 2886.72, 67: 372.48, 68: 9591.36, 69: 155.2, 70: 212158.4, 71: 39863.2, 72: 375.4099090909091, 73: 37.541, 74: 337.86891, 75: 825.9019999999999, 76: 112.62290000000002, 77: 375.4099, 78: 37.541, 79: 375.4099, 80: 863.443, 81: 1614.2628805555553, 82: 1126.23, 83: 3303.6079, 84: 2552.787905555556}, 'quantity': {0: 304, 1: 7, 2: 8, 3: 243, 4: 66, 5: 1, 6: 1, 7: 10, 8: 7, 9: 6, 10: 89, 11: 1, 12: 73, 13: 1, 14: 1, 15: 77, 16: 1, 17: 3, 18: 2, 19: 1, 20: 7, 21: 8, 22: 5, 23: 7, 24: 2, 25: 6, 26: 713, 27: 1570, 28: 125, 29: 116, 30: 2458, 31: 1030, 32: 1, 33: 1, 34: 273, 35: 6, 36: 6, 37: 12, 38: 1443, 39: 1, 40: 1, 41: 2, 42: 1, 43: 6, 44: 18, 45: 23, 46: 4, 47: 4, 48: 7, 49: 21, 50: 3, 51: 1, 52: 12, 53: 8, 54: 12, 55: 1, 56: 10, 57: 8, 58: 11, 59: 3, 60: 2, 61: 1, 62: 11, 63: 13, 64: 67, 65: 796, 66: 93, 67: 12, 68: 309, 69: 5, 70: 6835, 71: 2080, 72: 10, 73: 1, 74: 9, 75: 22, 76: 3, 77: 10, 78: 1, 79: 10, 80: 23, 81: 43, 82: 30, 83: 88, 84: 68}, 'gain': {0: 686.7359999999986, 1: 16.92530000000002, 2: 19.35360000000003, 3: -214.01009999999908, 4: -69.43859999999992, 5: 2.877900000000004, 6: 2.0679000000000016, 7: 20.68800000000001, 8: 19.795299999999997, 9: 16.84740000000005, 10: 235.68089999999984, 11: 1.7379000000000033, 12: 4921.66, 13: 67.04, 14: 58.30000000000001, 15: 4541.459999999999, 16: 31.91000000000001, 17: 96.44999999999999, 18: 68.12000000000003, 19: 31.820000000000007, 20: -5.389999999999873, 21: -11.519999999999982, 22: -14.050000000000011, 23: -23.52000000000001, 24: -31.0, 25: -93.24000000000001, 26: 8279.99758096829, 27: -1101.5119999999974, 28: 8249.262500000003, 29: 5213.063200000001, 30: 20229.585799999983, 31: 5639.353, 32: 5.545000000000002, 33: 5.475000000000001, 34: -511.875, 35: -11.268, 36: -14.700000000000077, 37: -13.31880000000001, 38: 3580.2272999999996, 39: 30.809999999999995, 40: 27.049999999999997, 41: 53.349999999999994, 42: 27.36, 43: 173.69989999999996, 44: 555.4798999999999, 45: 661.4799041666668, 46: 125.83999999999999, 47: 138.76999999999998, 48: 225.95999999999995, 49: 257.25, 50: 33.62989999999999, 51: 11.269999999999996, 52: 140.75990000000002, 53: 69.2, 54: 119.63990000000001, 55: 11.439999999999998, 56: 102.80700000000002, 57: 68.0079999999999, 58: 82.27999999999997, 59: 21.38990000000001, 60: 11.939999999999998, 61: 2.8699999999999903, 62: 32.55990833333334, 63: 37.699891666666645, 64: 432.8334, 65: 6089.559199999999, 66: 55.82790000000019, 67: 10.080000000000041, 68: 786.4049999999999, 69: 13.75, 70: 14699.35100000002, 71: 2277.8079999999986, 72: 38.***************, 73: 5.***************, 74: 38.88891, 75: -57.**************, 76: -4.**************, 77: -11.***************, 78: -2.****************, 79: -26.***************, 80: -92.**************, 81: -172.**************, 82: -107.**************, 83: -245.**************, 84: 49.**************}}\"))\n", "        \n", "        return self._df_lots.copy()\n", "    \n", "    @property\n", "    def cash(self):\n", "        '''The amount of cash in this portfolio'''\n", "        \n", "        if self._account_number is None:\n", "            return 40000\n", "        \n", "        return self._cash\n", "    \n", "    @property\n", "    def recent_trades(self):\n", "        '''Returns a dictionary containing securities bought and sold in the last 30 days\n", "        \n", "        Note: a copy is returned to avoid inadvertent modification\n", "        \n", "        The dictionary will contain two entires:\n", "          - 'sold'   : a list of tickers for securities that have been sold in the last 30\n", "                       days\n", "          - 'bought' : same with bought\n", "        '''\n", "        \n", "        if self._account_number is None:\n", "            return {'sold'   : ['VNQ', 'MUB', 'ORAN', 'IJH', 'IVV', 'VIG', 'VEA', 'IJR', 'NOK'],\n", "                    'bought' : ['VO', 'VWO', 'SCHF', 'VV', 'ORAN', 'SCHH', 'SCHA', 'NOK', 'VTEB']}\n", "        \n", "        return {'sold'   : list(self._recent_trades['sold']),\n", "                'bought' : list(self._recent_trades['bought'])}"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:780: SyntaxWarning: invalid escape sequence '\\$'\n", "<>:781: SyntaxWarning: invalid escape sequence '\\$'\n", "<>:780: SyntaxWarning: invalid escape sequence '\\$'\n", "<>:781: SyntaxWarning: invalid escape sequence '\\$'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14592\\**********.py:780: SyntaxWarning: invalid escape sequence '\\$'\n", "  f'  - Total harvested losses: **\\\\\\${df_sell_lots.gain.sum():,.2f}** '\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14592\\**********.py:781: SyntaxWarning: invalid escape sequence '\\$'\n", "  f'(including **\\\\\\${df_sell_lots.query(\"gain>0\").gain.sum():,.2f}** of '\n"]}], "source": ["class Rebalancer:\n", "    '''Rebalancing class; handles the re-balancing of a portfolio with tax loss harvesting\n", "    \n", "    This class is the powerhorse of this library; when it is initialized, it immediately\n", "    prints out a summary of the portfolio and of any rebalancing required. It then exposes\n", "    a rebalance() method which carires out the required trades.\n", "    '''\n", "    def __init__(self, account, conn = None, target_portfolio = None, MAX_LOSS_TO_FORGO = 0, MAX_GAIN_TO_SELL = 0, forced_buys = {}):\n", "        '''Initialize the rebalancer and tax loss harvester\n", "        \n", "        This function will initialize the rebalancer; Rebalancer.rebalance() can then\n", "        be called to carry out the trades\n", "        \n", "        Arguments\n", "        ---------\n", "          - account           : an Account object\n", "          - conn              : and EtradeConnection object\n", "          - target_portfolio  : a TargetPortfolio object\n", "          - MAX_LOSS_TO_FORGO : parameter in the rebalancing; see docs for details\n", "          - MAX_GAIN_TO_SELL  : parameter in the rebalancing; see docs for details\n", "          - forced_buys       : this dictionary allows the user to by-pass the algorithm in deciding\n", "                                which security of each asset class to BUY. If provided, this should be\n", "                                a dictionary in which each key is the name of an asset class, and each\n", "                                value is EITHER None (to signify this asset class should not be bought)\n", "                                OR a string with an uppercase ticker for the security that should be\n", "                                bought.\n", "        '''\n", "        \n", "        print('Setting up rebalancer')\n", "        \n", "        # If we haven't provided a target portfolio, create a default one\n", "        if target_portfolio is None:\n", "            target_portfolio = TargetPortfolio()\n", "            \n", "            target_portfolio.add_assetclass(41, '1. US Large Cap',              ['IVV', 'SCHX', 'VV',   'VOO', 'IWB'      ],\n", "                                                                                [1,     2,      3,      4,     None       ])\n", "            target_portfolio.add_assetclass(11, '2. US Mid Cap',                ['IJH', 'VO',   'SCHM', 'IWR'             ],\n", "                                                                                [1,     2,      3,      None              ])\n", "            target_portfolio.add_assetclass(5,  '3. US Small Cap',              ['IJR', 'SCHA', 'VB',   'VXF', 'IWM'      ],\n", "                                                                                [1,     2,      3,      None, None        ])\n", "            target_portfolio.add_assetclass(23, '4. Int. Developed Mkts',       ['VEA', 'IEFA', 'SCHF', 'VEU'             ],\n", "                                                                                [1,     2,      3,      4                 ])\n", "            target_portfolio.add_assetclass(5,  '5. Int. Emerging Mkts',        ['VWO', 'IEMG', 'SCHE'                    ],\n", "                                                                                [1,     2,      3                         ])\n", "            target_portfolio.add_assetclass(5,  '6. Real Estate',               ['VNQ', 'SCHH', 'USRT', 'RWR'             ],\n", "                                                                                [1,     2,      3,      None              ])\n", "            target_portfolio.add_assetclass(3,  '7. Fixed Income Short Trm',    ['SUB', 'SHM'                             ],\n", "                                                                                [1,     2                                 ])\n", "            target_portfolio.add_assetclass(7,  '8. Fixed Income Mid+Long Trm', ['MUB', 'VTEB', 'TFI',  'ITM'             ],\n", "                                                                                [1,     2,      3,      None              ])\n", "            target_portfolio.validate()\n", "        \n", "        # Store variables\n", "        # ---------------\n", "        self._conn = conn\n", "        self._account = account\n", "        self._target_portfolio = target_portfolio\n", "        self._MAX_LOSS_TO_FORGO = MAX_LOSS_TO_FORGO\n", "        self._MAX_GAIN_TO_SELL = MAX_GAIN_TO_SELL\n", "        \n", "        # Note that this rebalancer has not yet been optimized\n", "        self._optimized = False\n", "        \n", "        # Process the forced buys\n", "        # -----------------------\n", "        for a in forced_buys:\n", "            # Make sure this forced buy is an asset class that exists\n", "            if a not in self._target_portfolio.target_assetclasses:\n", "                raise RebalancerError('The force buy specified does not exist in the target portfolio.')\n", "        \n", "            if forced_buys[a] is None:\n", "                forced_buys[a] = {'security' : None, 'badness' : None}\n", "            else:\n", "                # Find the security and make sure it belonds to that asset class\n", "                security = self._target_portfolio.find_security(forced_buys[a])\n", "                \n", "                if security['assetclass'] != a:\n", "                    raise RebalancerError(f'The forced buy specified for asset class {a} was not part '\n", "                                                'of that asset class in the target portfolio, but was '\n", "                                               f'in fact part of asset class {security[\"assetclass\"]}. '\n", "                                                'Please fix.')\n", "                \n", "                forced_buys[a] = {'security' : forced_buys[a], 'badness' : security['badness']}\n", "        \n", "        # Store the forced buys\n", "        self._forced_buys = forced_buys\n", "        \n", "        # Go through steps\n", "        # ----------------\n", "        print('Identifying securities to buy in each asset class')\n", "        self._identify_buys()\n", "        \n", "        print('Identifying lots to sell in each asset class')\n", "        self._identify_sells()\n", "        \n", "        print('Determining how much of each asset class to buy')\n", "        self._identify_buy_amounts()\n", "        \n", "        # Print the results\n", "        self._print_status()\n", "        \n", "    def _identify_buys(self):\n", "        '''For each target asset class, identify the security to buy\n", "        \n", "        See the documentation for a description of the rules used to do this\n", "        \n", "        It creates a self._buys dictionary, in which each entry corresponds to one\n", "        asset class, and is itself a dictionary with the following items\n", "          - security      : the security that will be purchased in this asset class\n", "          - badness       : the badness of this security in this asset class\n", "          - current_price : the current price of the security chosen\n", "        '''\n", "        \n", "        buys = {}\n", "        \n", "        # Go through every target asset class and find the security to buy\n", "        for a_name, a in self._target_portfolio.target_assetclasses.items():\n", "            if a_name in self._forced_buys:\n", "                # If we have a specified forced buy, pick it\n", "                buys[a_name] = self._forced_buys[a_name]\n", "            else:\n", "                # No such luck, we weren't provided a forced buy; find one\n", "                \n", "                # Start with a badness score of 1\n", "                badness_score = 1\n", "            \n", "                while len(a.securities(badness_score)) > 0:\n", "                    # Find all securities at this badness level that haven't recently been\n", "                    # sold, and find their losses (if they are not in df_positions, they\n", "                    # aren't in the portfolio and have therefore experienced losses of 0)\n", "                    buy_options = [(s, 0 if s in self._account.recent_trades['bought'] else self._account.df_positions.lots_loss.get(s, 0))\n", "                                       for s in a.securities(badness_score)\n", "                                          if (s not in self._account.recent_trades['sold'])]\n", "                    \n", "                    # Sort by the loss in decreasing order (least loss to most loss); the\n", "                    # \"sorted\" function is stable, so any ties will remain in the original,\n", "                    # user-specified order.\n", "                    buy_options = sorted(buy_options, key = lambda x : -x[1])\n", "                    \n", "                    # If the abs(loss) in the security with the smallest loss is <=\n", "                    # MAX_LOSS_TO_FORGO, we're good. <PERSON><PERSON> move to the next badness level\n", "                    if (len(buy_options) > 0) and (np.abs(buy_options[0][1]) <= self._MAX_LOSS_TO_FORGO):\n", "                        buys[a_name] = {'security' : buy_options[0][0], 'badness' : badness_score}\n", "                        break\n", "                    else:\n", "                        badness_score += 1\n", "        \n", "        # Check whether we've identified a buy for every asset class\n", "        if set(buys.keys()) != set(self._target_portfolio.target_assetclasses):\n", "            raise RebalancerError('The rebalancer was not able to identify a security to buy '\n", "                                    'for every asset class')\n", "        \n", "        # For every asset, find the current price\n", "        for _, a in buys.items():\n", "            if a['security'] is not None:\n", "                if self._conn is None:\n", "                    a['current_price'] = {'IEMG': 44.9579, 'VIG': 149.27, 'VO': 203.8899998330551, 'VTEB': 47.7534,\n", "                                            'VV': 174.43, 'VEA': 40.185, 'SUB': 102.81, 'SCHA': 41.3399,\n", "                                            'SCHD': 75.04999513134058, 'SCHF': 31.04, 'SCHH': 19.165,\n", "                                            'VWO': 37.540997500635285, 'IVV': 383.39, 'SCHX': 45.075, 'VOO': 350.855,\n", "                                            'IWB': 208.79, 'IJH': 236.5, 'SCHM': 65.64500000000001, 'IWR': 67.1,\n", "                                            'IJR': 98.92, 'VB': 185.5, 'VXF': 135.18, 'IWM': 179.4, 'IEFA': 59.575,\n", "                                            'VEU': 46.725, 'SCHE': 10.785, 'VNQ': 81.91499999999999, 'USRT': 49.065,\n", "                                            'RWR': 87.025, 'SHM': 45.54, 'MUB': 102.015, 'TFI': 43.86, 'ITM': 43.76}[a['security']]\n", "                else:\n", "                    a['current_price'] = self._conn.get_current_price(a['security'])\n", "                \n", "        self._buys = buys\n", "            \n", "    def _identify_sells(self):\n", "        '''Identify all tax lots to sell, and preview the transactions with eTrade\n", "        \n", "        This function will identify all tax lots that should be sold.\n", "        \n", "        It creates a self._sells dictionary, in which each entry corresponds to one\n", "        security we will sell, and is itself a dictionary with the following items\n", "          - lots               : a list of lots to sell for this security. Each entry will\n", "                                 be a dictionary with two entries; position_lot_id (the lot\n", "                                 ID) and quantity (the quantity of securities in that lot to\n", "                                 sell)\n", "          - preview_result     : the result of the eTrade trade preview that can be\n", "                                 later used to place the order for this security's\n", "                                 transactions\n", "          - estimated_proceeds : the estimated proceeds from the sale of this security\n", "                                 returned by the eTrade API preview; this is extracted\n", "                                 from the preview result for convenience\n", "          - sell_loss          : True if all losing lots of this security are being sold\n", "          - sell_gain          : True if all winning lots of this security are being sold\n", "        '''\n", "        \n", "        # Create a list of all lots we cannot sell, either because they were\n", "        # bought in the last 30 days, or because we're about to buy them\n", "        do_not_sell = ( self._account.recent_trades['bought'] + [b['security'] for b in self._buys.values()] )\n", "        \n", "        # Create a dictionary of sells; we will eventually remove any securities\n", "        # that will not be sold\n", "        sells = {s : {'lots' : [], 'sell_loss' : False, 'sell_gain' : False}\n", "                                          for s in self._account.df_positions.index}\n", "        \n", "        # Identify losing lots we should sell\n", "        # -----------------------------------\n", "        for row_n, row in self._account.df_lots.iterrows():\n", "            # If we can sell this security, and the lot experienced a loss, sell it!\n", "            if (row.symbol not in do_not_sell) and (row.gain < 0):\n", "                sells[row.symbol]['lots'].append({'position_lot_id' : row.position_lot_id,\n", "                                                  'quantity'        : row.quantity})\n", "                sells[row.symbol]['sell_loss'] = True\n", "        \n", "        # Identify winning lots we should sell\n", "        # ------------------------------------\n", "        \n", "        # This one's a little more complicated; we're going to go through\n", "        # every SECURITY, not lot, and check whether this security meets\n", "        # three tests:\n", "        #   - The security is not in the do_not_sell list\n", "        #   - We have identified a security to buy in this asset class\n", "        #   - The badness of the security is > the badness of the security\n", "        #     we're buying in that asset class OR the security is not in our\n", "        #     target portfolio\n", "        # If we pass the test, then we look at the lots of that security in\n", "        # ascending order of gain. Sell all the lots with gains until the\n", "        # cumulative gain (not offset by losses) reaches MAX_GAIN_TO_SELL\n", "        \n", "        for row_n, row in self._account.df_positions.iterrows():\n", "            symbol     = row.name\n", "            total_gain = row.lots_gain\n", "            assetclass = self._target_portfolio.find_security(symbol)['assetclass']\n", "            badness    = self._target_portfolio.find_security(symbol)['badness'] \n", "            \n", "            if (      (row.name not in do_not_sell)\n", "                  and (    (assetclass == OTHER_NAME)\n", "                        or (self._buys[assetclass]['security'] is not None) )\n", "                  and (    (assetclass == OTHER_NAME)\n", "                        or (badness is None)\n", "                        or (badness > self._buys[assetclass]['badness']))):\n", "                # We've identified a security for which we want to sell gaining lots\n", "                # if the gain is small enough. Identify those lots\n", "                gaining_lots = (self._account\n", "                                    .df_lots\n", "                                    .query(f'(symbol == \"{symbol}\") and (gain > 0)')\n", "                                    .sort_values('gain', ascending=True))\n", "                \n", "                # Add the lots until the gains are >= MAX_GAIN_TO_SELL\n", "                lots_to_sell = 0\n", "                while gaining_lots.iloc[0:(lots_to_sell+1), :].gain.sum() < self._MAX_GAIN_TO_SELL:\n", "                    lots_to_sell += 1\n", "                    \n", "                    if lots_to_sell == len(gaining_lots):\n", "                        break\n", "                \n", "                sells[symbol]['lots'].extend(gaining_lots.iloc[0:lots_to_sell, :]\n", "                                                         .apply(lambda r : {'position_lot_id' : r.position_lot_id,\n", "                                                                            'quantity'        : r.quantity},\n", "                                                                axis = 1)\n", "                                                         .tolist())\n", "                \n", "                if lots_to_sell > 0:\n", "                    sells[symbol]['sell_gain'] = True\n", "        \n", "        # Filter down to securities for which\n", "        # we'll sell at least one lot\n", "        # -----------------------------------\n", "        sells = {s : sells[s] for s in sells if sells[s]['sell_loss'] or sells[s]['sell_gain']}\n", "\n", "        # Preview the orders\n", "        # ------------------\n", "        for s in tqdm(sells):\n", "            if self._conn is None:\n", "                sells[s]['estimated_proceeds'] = self._account.df_lots[lambda x : x.position_lot_id.isin([l['position_lot_id'] for l in sells[s]['lots']])].market_value.sum()\n", "            else:\n", "                sells[s]['preview_result'] = (\n", "                        self._conn\n", "                            .execute_order(account_id_key = self._account._account_id_key,\n", "                                           symbol         = s,\n", "                                           price_type     = 'MARKET',\n", "                                           order_action   = 'SELL',\n", "                                           quantity       = sum([l['quantity'] for l in sells[s]['lots']]),\n", "                                           lots           = sells[s]['lots'],\n", "                                           preview_only   = True) )\n", "                \n", "                sells[s]['estimated_proceeds'] = -sells[s]['preview_result']['estimated_cost']\n", "        \n", "        self._sells = sells\n", "        \n", "    def _identify_buy_amounts(self):\n", "        '''This function will calculate the amount of each  asset class we should buy\n", "        \n", "        The amounts will be calculated to get the final allocation as close as possible to the target\n", "        asset class percentages. This will be done using a constrained quadratic program; see the\n", "        documentation for an explanation of the algorithm used\n", "        \n", "        This functions adds a 'target_buy' entry to each dictionary in self._buys, describing the\n", "        amount of that asset class that will be bought. It will then self self._optimized to True.\n", "        '''\n", "        \n", "        # Find the total proceeds from sales, including free cash\n", "        cash_from_sales = self._get_assetclass_table().expected_sales.sum()\n", "        \n", "        # Get the rows of the allocations table that will be in the target\n", "        # portfolio\n", "        df_assetclasses = self._get_assetclass_table().pipe(lambda x : x[x.target_perc.notnull()])\n", "        \n", "        # Identify the securities that we are optimizing over\n", "        I = df_assetclasses.buy_security.notnull().to_numpy()\n", "        N = sum(I)\n", "        \n", "        # Calculate the total size of the portfolio we're building with these\n", "        # securities\n", "        T = sum(df_assetclasses.expected_balance.to_numpy()[I]) + cash_from_sales\n", "        \n", "        # Calculate the target market values; if I does not include every\n", "        # asset class, the way we should do this is a little bit ambiguous.\n", "        # The most obvious way to do this is simply to scale the percentages\n", "        # of the securities in I to sum to 100%\n", "        chi = df_assetclasses.target_perc.to_numpy()\n", "        chi = chi/sum(chi[I])\n", "        chi = chi*T\n", "        \n", "        # Find the expected market values\n", "        ell = df_assetclasses.expected_balance.to_numpy()\n", "        \n", "        # Start with an x equal to ell\n", "        x = ell.copy()\n", "        \n", "        # Solve\n", "        # -----\n", "        \n", "        # Begin by assuming all the mus are 0; set gamma accordingly\n", "        mu = np.zeros(len(chi))\n", "        gamma = (2/N) * (sum(chi[I]) - T)\n", "        \n", "        # Now calculate the x's\n", "        x[I] = chi[I] - 0.5*gamma\n", "        \n", "        # Iterate until we meet primal feasibility; to ensure we don't run\n", "        # forever, at a 1000 iteration limit\n", "        i = 0\n", "        while (sum(x[I] < ell[I]) > 0) and (i < 1000):\n", "            # Find the indices that violate feasibility or are on the edge of\n", "            # the feasible region\n", "            J = (x[I] <= ell[I])\n", "            \n", "            Imj = I.copy()\n", "            Imj[J] = False\n", "            \n", "            # Find the new gamma\n", "            gamma = (2 / (N - sum(J))) * ( sum(chi[Imj]) + sum(ell[J]) - T )\n", "            \n", "            # Find the new mu_j\n", "            mu[J] = 2*(ell[J] - chi[J]) + gamma\n", "            \n", "            # Calculate the x's\n", "            x[I] = chi[I] + 0.5*(mu[I] - gamma)\n", "            \n", "            i += 1\n", "        \n", "        # Return\n", "        # ------\n", "        \n", "        for a, x in zip(df_assetclasses.index, x):\n", "            self._buys[a]['target_buy'] = x - df_assetclasses.expected_balance[a]\n", "            \n", "        self._optimized = True\n", "        \n", "    def _get_assetclass_table(self):\n", "        '''This function returns a Pandas DataFrame summarizing the asset classes in our target portfolio\n", "        \n", "        The table will contain the following columns\n", "          INDEX: the asset class in question\n", "            - market_value           : the market value of all securities held in this asset class\n", "            - target_perc            : the percentage of this asset class in the target portfolio\n", "            - current_perc_exc_other : the current percentage of the portfolio comprising this security.\n", "                                       Note this will ONLY include securities in asset classes that we\n", "                                       want to include in the target portfolio; all other securities (and\n", "                                       cash) will not be included in these percentage calculations.\n", "            - expected_sales         : the expected proceeds from sales of securities in this asset class\n", "            - expected_balance       : the expected balance of securities in this asset class after sales\n", "            - buy_security           : the security we will be buying in this asset class\n", "        \n", "        In addition, if self._optimized is True (in other words, if we have already calculated the quantity\n", "        of each asset we will be buying), the following additional columns will be output\n", "            - target_buy     : the amount of that asset class we would like to buy\n", "            - buy_quantity   : the number of securities to buy in that asset class to achieve the target_buy\n", "                               (this will be rounded down to the closest integer)\n", "            - expected_buy   : the amount of that asset class that *will* be bought after rounding down\n", "            - final_balance  : the final balance of this asset class after purchases\n", "            - final_perc     : the final percentage of the portfolio that will comprise this asset class\n", "        \n", "        '''\n", "        \n", "        # Step 1; get the positions table and target asset classes\n", "        # --------------------------------------------------------\n", "        df_positions        = self._account.df_positions\n", "        target_assetclasses = self._target_portfolio.target_assetclasses\n", "        \n", "        # Step 2; classify each security into asset classes\n", "        # -------------------------------------------------\n", "        df_positions['assetclass'] = df_positions.apply(lambda x : self._target_portfolio\n", "                                                                       .find_security(x.name)\n", "                                                                       ['assetclass'],\n", "                                                        axis = 1)\n", "\n", "        # Step 3; add expected sales\n", "        # --------------------------\n", "        if len(self._sells) > 0:\n", "            df_positions['expected_sales'] = ( pd.DataFrame(self._sells)\n", "                                                 .transpose()\n", "                                                 ['estimated_proceeds'] )\n", "            df_positions['expected_sales'] = df_positions['expected_sales'].fillna(0)\n", "        else:\n", "            df_positions['expected_sales'] = 0\n", "            \n", "        # Step 4; group by asset class\n", "        # ----------------------------\n", "        df = df_positions.groupby('assetclass')[['market_value', 'expected_sales']].sum()\n", "        \n", "        # Re-order rows so that the \"other\" allocation is at the end\n", "        df = df.loc[ [i for i in df.index if i != OTHER_NAME] + [OTHER_NAME], :]\n", "        \n", "        # Step 5; Calculate %s\n", "        # --------------------\n", "        df['target_perc'] = pd.DataFrame({i : [j.target] for i, j in\n", "                                            target_assetclasses.items()}).transpose()[0]\n", "        \n", "        rows_exc_other = df.index != OTHER_NAME\n", "        df['current_perc_exc_other'] = None\n", "        df.loc[rows_exc_other, 'current_perc_exc_other'] = (\n", "                            df.loc[rows_exc_other, 'market_value'] * 100\n", "                                     / df[rows_exc_other].market_value.sum() )\n", "        \n", "        # Step 6; add the cash row\n", "        # ------------------------\n", "        free_cash = self._account.cash\n", "        df.loc[CASH_NAME, :] = {'market_value'   : free_cash,\n", "                                'expected_sales' : free_cash}\n", "        \n", "        # Step 7; add an \"expected balance\" column\n", "        # ----------------------------------------\n", "        df['expected_balance'] = df.market_value - df.expected_sales.fillna(0)\n", "        \n", "        # Step 8; add the \"buy security\" column\n", "        # -------------------------------------\n", "        df['buy_security'] = pd.DataFrame(self._buys).transpose()['security']\n", "        \n", "        # Step 9; re-order columns\n", "        # ------------------------\n", "        df = df[['market_value', 'target_perc', 'current_perc_exc_other',\n", "                            'expected_sales', 'expected_balance', 'buy_security']]\n", "        \n", "        # Step 10; add target purchases if they exist\n", "        # -------------------------------------------\n", "        if self._optimized:\n", "            # Add the target_buy to the DataFrame\n", "            df['target_buy'] = pd.DataFrame(self._buys).transpose()['target_buy']\n", "        \n", "            # Add the current price to the DataFrame\n", "            df['current_price'] = pd.DataFrame(self._buys).transpose()['current_price']\n", "        \n", "            # Calculate the buy quantity and expected buy\n", "            df['buy_quantity'] = (df.target_buy / df.current_price).apply(lambda x : int(x) if pd.notnull(x) else np.nan)\n", "            df['expected_buy'] = df.buy_quantity * df.current_price\n", "        \n", "            # Calculate the final balance\n", "            df['final_balance'] = df['expected_balance'] + df['expected_buy'].fillna(0)\n", "            \n", "            # Calculate the final percentage\n", "            rel_rows = df.target_perc.notnull()\n", "            df['final_perc'] = None\n", "            df.loc[rel_rows, 'final_perc'] = df[rel_rows].final_balance*100/df[rel_rows].final_balance.sum()\n", "            \n", "            # Figure out the amount of cash left\n", "            df.loc[CASH_NAME, 'final_balance'] = df.expected_sales.sum() - df.expected_buy.sum()\n", "            \n", "            # Re-order columns\n", "            df = df[['market_value', 'target_perc', 'current_perc_exc_other',\n", "                         'expected_sales', 'expected_balance', 'buy_security', 'target_buy',\n", "                                'buy_quantity', 'expected_buy', 'final_balance', 'final_perc']]\n", "            \n", "        return df\n", "        \n", "    def _print_status(self):\n", "        '''This function prints a summary of any rebalancing that will be carried out'''\n", "        \n", "        display(Markdown('# Preliminary warnings'))\n", "        \n", "        # Determine whether the markets are open; print a warning\n", "        if market_open():\n", "            display(Markdown('  - It seems markets are currently open. <span style=\"color:red\">'\n", "                             '    PLEASE DOUBLE CHECK THAT THIS IS INDEED TRUE, THE ALGORITHM WE '\n", "                             '    USE IS NOT PERFECT.</span> If you run the re-balancing algorithm'\n", "                             '    while the market is closed, the orders will be queued, and by '\n", "                             '    the time they execute, prices might have changed.'))\n", "        else:\n", "            display(Markdown(\"  - <span style='color:red'>It looks like markets are currently \"\n", "                             \"    closed. The rebalancer will therefore not let you place any \"\n", "                             \"    orders. If it did, they would be queued, and would only \"\n", "                             \"    execute once the market re-opens, at which point prices might \"\n", "                             \"    have changed.</span> The algorithm we use to determine whether \"\n", "                             \"    markets are open isn't foolproof; to override it, modify the \"\n", "                             \"    `market_open` function in the code.\"))\n", "        \n", "        # Warn about re-invested dividends\n", "        display(Markdown(\"  - <span style='color:red'>Make sure your portfolio is not set to \"\n", "                         \"    automatically re-invest dividends.</span> If it is, you might \"\n", "                         \"    inadvertently create a wash sale.\"))\n", "                             \n", "        display(Markdown('# Portfolio rebalancing report'))\n", "        \n", "        # ==============================\n", "        # =   Summary by asset class   =\n", "        # ==============================\n", "        display(Markdown('### 1. Summary by asset classes'))\n", "        \n", "        display(Markdown('Each row in the table below corresponds to a specific asset class we need '\n", "                         'in our final portfolio, and an extra row for all assets that are not in one '\n", "                         'of these \"target\" asset classes. For each asset, it provides the following '\n", "                         'details:\\n\\n'\n", "                         '  - **Market value**: the current market value of assets in this class.\\n\\n'\n", "                         '  - **Target %**: the target percentage of the final portfolio that '\n", "                         '    should comprise this asset class.\\n\\n'\n", "                         '  - **Current %**: the current percentage of the portfolio that comprises '\n", "                         '    this asset class. Note that this does *not* include assets in the '\n", "                         '    \"Other\" category, but *does* include cash.\\n\\n'\n", "                         '  - **Expected sales**: the $ amount of securities in this asset class that '\n", "                         '    we intend to sell to carry out tax loss harvesting. Further details are '\n", "                         '    provided in the forthcoming sections.\\n\\n'\n", "                         '  - **Expected balance**: the expected market value of securities in this '\n", "                         '    asset class *after* any expected sales described in the previous column.\\n\\n'\n", "                         '  - **Buy security**: the security we will be buying to replenish this asset '\n", "                         '    class.\\n\\n'\n", "                         '  - **Buy target**: the $ amount of the security in the previous column we '\n", "                         '    would ideally want to buy. This will be determined using a constrained '\n", "                         '    quadratic program; see the documentation.\\n\\n'\n", "                         '  - **Buy quantity**: the number of shares of the security in question we will '\n", "                         '    buy. This is obtained by dividing the previous column by the share price, '\n", "                         '    and rounding down.\\n\\n'\n", "                         '  - **Expected buy**: the expected $ amount of the security we will buy, '\n", "                         '    calculated by multiplying the quantity in the previous column by the price '\n", "                         '    of the security. This should be close to **Buy target**, but not exactly '\n", "                         '    equal to it because the quantity will have been rounded down.\\n\\n'\n", "                         '  - **Final balance**: the final market value of this asset class after sales '\n", "                         '    and purchases.\\n\\n'\n", "                         '  - **Final %**: the final percentage of the portfolio that will comprise this '\n", "                         '    asset class, after sales and purchases.'))\n", "        \n", "        # Get the table        \n", "        df_assetclasses = self._get_assetclass_table()\n", "                \n", "        # Format and display it\n", "        dollar_formatter = lambda x : '' if x == 0 else f'${x:,.2f}'\n", "        df_assetclasses.index.name = None\n", "        display( df_assetclasses.style\n", "                                .format(na_rep    = '',\n", "                                        # Format the numbers\n", "                                        formatter = {'market_value'           : dollar_formatter,\n", "                                                     'target_perc'            : lambda x : f'{int(x)}%',\n", "                                                     'current_perc_exc_other' : lambda x : f'{round(x,2)}%',\n", "                                                     'expected_sales'         : dollar_formatter,\n", "                                                     'expected_balance'       : dollar_formatter,\n", "                                                     'target_buy'             : dollar_formatter,\n", "                                                     'buy_quantity'           : lambda x : '' if x == 0 else int(x),\n", "                                                     'expected_buy'           : dollar_formatter,\n", "                                                     'final_balance'          : dollar_formatter,\n", "                                                     'final_perc'             : lambda x : f'{round(x,2)}%'})\n", "                                # Display the \"other\" and \"cash\" row in italics\n", "                                .apply(lambda x : ['font-style: italic;'\n", "                                                         if pd.isnull(x.target_perc)\n", "                                                                 else None]*len(x), axis=1)\n", "                                # Change the column names to something more legible\n", "                                .format_index(lambda x : {'market_value'           : 'Market value',\n", "                                                          'target_perc'            : 'Target %',\n", "                                                          'current_perc_exc_other' : 'Current %',\n", "                                                          'expected_sales'         : 'Expected sales',\n", "                                                          'expected_balance'       : 'Expected balance',\n", "                                                          'buy_security'           : 'Buy security',\n", "                                                          'target_buy'             : 'Buy target',\n", "                                                          'buy_quantity'           : 'Buy quantity',\n", "                                                          'expected_buy'           : 'Expected buy',\n", "                                                          'final_balance'          : 'Final balance',\n", "                                                          'final_perc'             : 'Final %'}.get(x,x), \n", "                                              axis = 1)\n", "                                # Color the sales column in red\n", "                                .apply(lambda x : ['background:pink']*len(x), subset = 'expected_sales')\n", "                                .apply_index(lambda x : np.where(x=='expected_sales','background:pink', None),\n", "                                             axis = 1)\n", "                                # Color the buy column in green\n", "                                .apply(lambda x : ['background:lightgreen']*len(x), subset = ['target_buy', 'expected_buy'])\n", "                                .apply_index(lambda x : np.where(x.isin(['target_buy', 'expected_buy']),\n", "                                                                 'background:lightgreen',\n", "                                                                 None),\n", "                                             axis = 1)\n", "                                # Add vertical lines\n", "                                .apply(lambda x : ['border-left: 2px solid black']*len(x), subset = ['expected_sales', 'buy_security', 'final_balance'])\n", "                                .apply_index(lambda x : np.where(x.isin(['expected_sales', 'buy_security', 'final_balance']),\n", "                                                                 'border-left: 2px solid black',\n", "                                                                 None),\n", "                                             axis = 1)\n", "        )\n", "        \n", "        display(Markdown('The graph below summarizes the current and final deviations from the '\n", "                         'target portfolio'))\n", "        \n", "        ( df_assetclasses[df_assetclasses.target_perc.notnull()]\n", "                         .assign(current_perc_over = lambda x : x.current_perc_exc_other - x.target_perc,\n", "                                 final_perc_over   = lambda x : x.final_perc - x.target_perc)\n", "                         [['current_perc_over', 'final_perc_over']]\n", "                         .rename(columns = {'current_perc_over' : 'Current % over target',\n", "                                           'final_perc_over'   : 'Final % over target'})\n", "                         .plot(kind='bar', xlabel='', ylabel='% (+ve = over target)') )\n", "        sns.despine()\n", "        plt.show()\n", "        \n", "        # ====================================\n", "        # =   Security-by-security summary   =\n", "        # ====================================\n", "        display(Markdown('### 2. Security-by-security summary'))\n", "        \n", "        display(Markdown('Each row below corresponds to one security, separated by target asset class. '\n", "                         'Every security in the current portfolio will be listed in this table, whether '\n", "                         ' it appears in the target portfolio or not.\\n\\n'\n", "                         '  - Column legend\\n\\n'\n", "                         '       * **Badness**: the user-specified badness of that security within its asset class.\\n\\n'\n", "                         '       * **30 days**: whether this security was bought (`B`) or sold (`S`) in the last 30 days.\\n\\n'\n", "                         '       * **Quantity**, **Market value**: the quantity of that security currently owned, and the current '\n", "                         '         market value of those securities; any empty entries represent securities that are not owned.\\n\\n'\n", "                         '       * **Lots loss**, **Lots gain**: the sum of all losses/gains for all losing/gaining tax lots for'\n", "                         '         this security.\\n\\n'\n", "                         '  - The security highlighted in <span style=\"background:lightgreen\">green</span> in each section denotes '\n", "                         '    the security that will be **bought** during rebalancing.\\n\\n'\n", "                         '  - Any cells highlighted in <span style=\"background:pink\">pink</span> denote assets for which at least some '\n", "                         '    tax lots that will be **sold** during rebalancing. More details on these tax lots are provided in the '\n", "                         '    next section.'))\n", "        \n", "        # Step 2a; construct the table\n", "        # ----------------------------\n", "        \n", "        # First, construct a base DataFrame in which each row is a security (either\n", "        # securities in the portfolio, or securities in the target allocations)\n", "        #   - The name of the asset class the security is in\n", "        #   - The symbol for the security\n", "        #   - The badness score for the security in that asset class\n", "        \n", "        # Begin with all the securities in target allocations\n", "        df = [{'assetclass':a_name, 'symbol':symbol, 'badness':badness}\n", "                    for a_name, a in self._target_portfolio.target_assetclasses.items()\n", "                        for symbol, badness in zip(a.securities(), a.badness_scores)]\n", "        \n", "        # Add any securities in the portfolio that haven't been added yet\n", "        df.extend([{'assetclass':'Not in target portfolio', 'symbol':symbol, 'badness':None}\n", "                                            for symbol in set(self._account.df_positions.index)\n", "                                                                - set([i['symbol'] for i in df])])\n", "        \n", "        # Add cash\n", "        df.append({'assetclass':'Not in target portfolio', 'symbol':'Cash', 'badness':None})\n", "        \n", "        # Convert to a DataFrame\n", "        df = pd.DataFrame(df)\n", "        \n", "        # Merge in data about how much of the security we hold\n", "        df = pd.merge(df,\n", "                      self._account.df_positions,\n", "                      on       = 'symbol',\n", "                      how      = 'outer',\n", "                      validate = 'one_to_one')\n", "        \n", "        # Add cash data\n", "        df.loc[df.symbol == 'Cash', 'market_value'] = self._account.cash\n", "        \n", "        # Add data about whether this security was traded in the last 30 days\n", "        df['30_days'] = ''\n", "        for s in self._account.recent_trades['bought']:\n", "            df.loc[df.symbol == s, '30_days'] += 'B'\n", "        for s in self._account.recent_trades['sold']:\n", "            df.loc[df.symbol == s, '30_days'] += 'S'\n", "        \n", "        # Step 2b; prepare to format the table\n", "        # ------------------------------------\n", "        \n", "        # One of the ways to format a DataFrame in Pandas is using lists with as\n", "        # many entries as rows in which each element contains formatting\n", "        # instructions for the relevant row. Begin by constructing these lists\n", "        \n", "        # First, create a lists that will create a bottom border on the last line\n", "        # of every asset class\n", "        style = 'border-bottom: 3px solid black'\n", "        last_line_border = [style\n", "                              if df.assetclass[i] != df.assetclass[i+1] else None\n", "                                 for i in range(len(df)-1)] + [style]\n", "                    \n", "        # Because of the way multi-indexes work, the bottom border needs to be\n", "        # on the FIRST line of every asset class for the index; create that list\n", "        first_line_border = [style] + [style if df.assetclass[i]\n", "                                                   != df.assetclass[i-1] else None\n", "                                                           for i in range(1, len(df))]        \n", "        \n", "        # We now need to determine how to format the lot gains and lot losses.\n", "        # Losses will be in red and gains will be in green. Any lots that will\n", "        # be sold will have a light blue background\n", "        loss_format = []\n", "        for row_n, row in df.iterrows():\n", "            this_format = []\n", "            \n", "            if self._sells.get(row.symbol, {}).get('sell_loss'):\n", "                this_format.append('background:pink')\n", "            \n", "            loss_format.append(';'.join(this_format) or None)\n", "        \n", "        gain_format = []\n", "        for row_n, row in df.iterrows():\n", "            this_format = []\n", "            \n", "            if self._sells.get(row.symbol, {}).get('sell_gain'):\n", "                this_format.append('background:pink')\n", "            \n", "            gain_format.append(';'.join(this_format) or None)        \n", "        \n", "        # Finally, identify any rows containing securities we will be BOUGHT;\n", "        # these should be highlighted in yellow\n", "        buy_format = np.where(df.symbol.isin([i['security'] for i in self._buys.values()]),\n", "                                                                 'background:lightgreen', None)\n", "        \n", "        # Step 2c; set the index and select columns\n", "        # -----------------------------------------\n", "        df = df.sort_values(['assetclass', 'badness'])\n", "        df = df.set_index(['assetclass', 'symbol'])\n", "        df = df[['badness', '30_days', 'quantity',\n", "                    'market_value', 'lots_loss', 'lots_gain']]\n", "        \n", "        # Step 2d; style the table\n", "        # ------------------------\n", "        df = ( df.style\n", "                 # Format the cells to get the numbers to look nice, and to\n", "                 # show NA values as blanks\n", "                 .format(na_rep    = '',\n", "                         formatter = {'badness'      : lambda x : int(x),\n", "                                      'quantity'     : lambda x : int(x),\n", "                                      'market_value' : lambda x : f'${x:,.2f}',\n", "                                      'lots_loss'    : lambda x : '' if x == 0 else f'${x:,.2f}',\n", "                                      'lots_gain'    : lambda x : '' if x == 0 else f'${x:,.2f}'})\n", "                 # Change the column names to something more legible\n", "                 .format_index(lambda x : {'badness'      : 'Badness',\n", "                                           '30_days'      : '30 days',\n", "                                           'quantity'     : 'Quantity',\n", "                                           'market_value' : 'Market value',\n", "                                           'lots_loss'    : 'Lots loss',\n", "                                           'lots_gain'    : 'Lots gain'}.get(x,x), axis=1)\n", "                 # Rotate the \"badness\" and \"30_days\" column names 90 degrees\n", "                 .apply_index(lambda x : np.where(x.isin(['badness', '30_days']),\n", "                                                    'transform: rotate(180deg); '\n", "                                                    'writing-mode:vertical-rl', None), axis=1)\n", "                 # Format the lots_loss and lots_gain columns\n", "                 .apply(lambda x : loss_format, subset='lots_loss')\n", "                 .apply(lambda x : gain_format, subset='lots_gain')\n", "                 # Format the buy rows\n", "                 .apply_index(lambda x : buy_format if x.name == 1 else [None]*len(x))\n", "                 .apply(lambda x : buy_format, axis=0)\n", "                 # Add the lines separating the allocations\n", "                 .apply(lambda x : last_line_border)\n", "                 .apply_index(lambda x : first_line_border if x.name == 0 else last_line_border) )\n", "        \n", "        # Display the results\n", "        display(df)\n", "        \n", "        # ====================\n", "        # =   Lots to sell   =\n", "        # ====================\n", "        \n", "        # Find all the lots that will be sold\n", "        sell_lots = [l['position_lot_id'] for s in self._sells for l in self._sells[s]['lots']]\n", "        df_sell_lots = ( self._account\n", "                             .df_lots\n", "                             [lambda x : x.position_lot_id.isin(sell_lots)]\n", "                             [['symbol', 'position_lot_id', 'acquired_date', 'quantity', 'market_value', 'gain']]\n", "                             .reset_index(drop=True) )\n", "                \n", "        display(Markdown('### 3. Specific lots to sell'))\n", "        \n", "        display(Markdown( 'The following summarizes the positions we will sell:\\n\\n'\n", "                         f'  - Total lots: **{len(df_sell_lots)}**.\\n\\n'\n", "                         f'  - Total harvested losses: **\\\\\\${df_sell_lots.gain.sum():,.2f}** '\n", "                         f'(including **\\\\\\${df_sell_lots.query(\"gain>0\").gain.sum():,.2f}** of '\n", "                          'realized gains).\\n\\n'))\n", "        \n", "        if len(sell_lots) > 0:\n", "            # First, create a lists that will create a bottom border on the last line\n", "            # of every allocation\n", "            style = 'border-bottom: 3px solid black'\n", "            last_line_border = [style\n", "                                  if df_sell_lots.symbol[i] != df_sell_lots.symbol[i+1] else None\n", "                                     for i in range(len(df_sell_lots)-1)] + [style]\n", "                  \n", "            # Because of the way multi-indexes work, the bottom border needs to be\n", "            # on the FIRST line of every allocation for the index; create that list\n", "            first_line_border = [style] + [style if df_sell_lots.symbol[i]\n", "                                                       != df_sell_lots.symbol[i-1] else None\n", "                                                               for i in range(1, len(df_sell_lots))]        \n", "            \n", "            display(df_sell_lots.rename(columns={'symbol'          : 'Symbol',\n", "                                                 'position_lot_id' : 'Lot ID'})\n", "                                .set_index(['Symbol', 'Lot ID'])\n", "                                .style.format(formatter = {'market_value' : lambda x : f'${x:,.2f}',\n", "                                                           'gain'         : lambda x : f'${x:,.2f}'})\n", "                                      .format_index(lambda x : {'acquired_date'   : 'Acquired date',\n", "                                                                'quantity'        : 'Quantity',\n", "                                                                'market_value'    : 'Market value',\n", "                                                                'gain'            : 'Gain',\n", "                                                                'allocation'      : 'Allocation'}.get(x,x), axis=1)\n", "                                      .apply(lambda x : last_line_border)\n", "                                      .apply_index(lambda x : first_line_border if x.name == 0 else last_line_border) )\n", "            \n", "        # ================\n", "        # =  Next steps  =\n", "        # ================\n", "        \n", "        display(Markdown('# Next steps'))\n", "        \n", "        display(Markdown('When you are ready to re-balance, run the `rebalance` method of '\n", "                         'this object. It will first sell the lots above, and then immediately '\n", "                         'buy the assets described above.'))\n", "        \n", "        display(Markdown(\"<span style='color:red'>You must run this function as quickly as \"\n", "                         \"possible after first creating the rebalancing object, to ensure \"\n", "                         \"prices do not shift between the time the rebalancing amounts are \"\n", "                         \"calculated, and the time the trades are executed. If you wait too \"\n", "                         \"long, the previewed transactions will expire and eTrade will not \"\n", "                         \"let you carry out any transaction.</span>\"))\n", "    \n", "    def rebalance(self):\n", "        '''This function will rebalance the portfolio by placing buy/sell trades\n", "        \n", "        Note that the function will require affirmative confirmation from the developer to place\n", "        trades; they will need to type \"yes\". This is to ensure no trades are inadvertently run\n", "        if the notebook is run automatically.\n", "        '''\n", "        \n", "        # Get the cell width\n", "        cell_width = 127\n", "        \n", "        # Ensure the markets are open\n", "        if not market_open():\n", "            raise RebalancerError('The markets are currently closed; please try again when the markets '\n", "                                  'are open.')\n", "        \n", "        # Get confirmation\n", "        if input('This function will place trades; please type \"yes\", all lowercase, to proceed  ') != 'yes':\n", "            print('No trades have been placed.')\n", "            return\n", "        \n", "        print()\n", "        print()\n", "        print('###############')\n", "        print('#   SELLING   #')\n", "        print('###############')\n", "        print()\n", "        print('Security'.ljust(10) + 'Outcome'.ljust(15) + 'eTrade messages')\n", "        print('-'*cell_width)\n", "        \n", "        # Sell the lots we need to sell\n", "        # -----------------------------\n", "        error = False\n", "        sells = self._sells\n", "        for s in sells:\n", "            print(s.ljust(10), end='')\n", "            \n", "            try:\n", "                sells[s]['trade_result'] = ( self._conn\n", "                                                 .execute_order(account_id_key = self._account._account_id_key,\n", "                                                                symbol         = s,\n", "                                                                price_type     = 'MARKET',\n", "                                                                order_action   = 'SELL',\n", "                                                                quantity       = sum([l['quantity'] for l in sells[s]['lots']]),\n", "                                                                lots           = sells[s]['lots'],\n", "                                                                preview_only   = False,\n", "                                                                preview_result = sells[s]['preview_result']['preview_result']) )\n", "            except Exception as e:\n", "                sells[s]['trade_result'] = {'messages': str(e), 'outcome':'ERROR', 'error':e}\n", "            \n", "            print( sells[s]['trade_result']['outcome'].ljust(15), end='' )\n", "            \n", "            message_width = cell_width - 25\n", "            messages = sells[s]['trade_result']['messages']\n", "            print(('\\n' + ' '*25).join([messages[(i*message_width):((i+1)*message_width)] for i in range(int(len(messages)/message_width)+1)]))\n", "            print()\n", "            \n", "            if sells[s]['trade_result']['outcome'] != 'PLACED':\n", "                error = True\n", "        \n", "        if error:\n", "            raise RebalancerError('Some of the sell lots could not be sold; please check what '\n", "                                  'happened. Just in case, I will *not* proceed with purchases, '\n", "                                  'so you may be left with a large amount of cash in your account. '\n", "                                  'Please triple check.')\n", "        \n", "        # Buy the lots we need to buy\n", "        # ---------------------------\n", "        \n", "        print('##############')\n", "        print('#   BUYING   #')\n", "        print('##############')\n", "        print()\n", "        \n", "        first_col_len = max(11, max(self._get_assetclass_table()\n", "                                        [lambda x : x.buy_quantity.notnull()]\n", "                                        .apply(lambda x : f'{x.name} + ({int(x.buy_quantity)} units of {x.buy_security})',\n", "                                               axis = 1)\n", "                                        .str\n", "                                        .len()) + 3)\n", "        \n", "        print('Asset class'.ljust(first_col_len) + 'Outcome'.ljust(15) + 'eTrade messages')\n", "        print('-'*cell_width)\n", "        \n", "        error = False\n", "        for row_n, row in self._get_assetclass_table()[lambda x : x.buy_security.notnull()].iterrows():\n", "            this_buy = self._buys[row.name]\n", "            \n", "            if pd.notnull(row.buy_quantity) and (row.buy_quantity > 0):\n", "                print(f'{row.name} ({int(row.buy_quantity)} units of {row.buy_security})'.ljust(first_col_len), end='')\n", "                \n", "                try:\n", "                    this_buy['trade_result'] = ( self._conn\n", "                                                     .execute_order(account_id_key = self._account._account_id_key,\n", "                                                                    symbol         = row.buy_security,\n", "                                                                    price_type     = 'MARKET',\n", "                                                                    order_action   = 'BUY',\n", "                                                                    quantity       = int(row.buy_quantity),\n", "                                                                    preview_only   = False) )\n", "                except Exception as e:\n", "                    this_buy['trade_result'] = {'messages': str(e), 'outcome':'ERROR', 'error':e}\n", "                \n", "                print( this_buy['trade_result']['outcome'].ljust(15), end='' )\n", "                \n", "                message_width = cell_width - first_col_len - 15\n", "                messages = this_buy['trade_result']['messages']\n", "                print(('\\n' + ' '*(first_col_len + 15)).join([messages[(i*message_width):((i+1)*message_width)] for i in range(int(len(messages)/message_width)+1)]))\n", "                print()\n", "                \n", "                if this_buy['trade_result']['outcome'] != 'PLACED':\n", "                    error = True\n", "                \n", "                # Pause to make sure the trade has time to execute\n", "                time.sleep(3)\n", "            \n", "        if error:\n", "            raise RebalancerError('Some of the buys could not be carried out. Please '\n", "                                  'triple check the state of the portfolio.')"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using the sample portfolio\n", "Setting up rebalancer\n", "Identifying securities to buy in each asset class\n", "Identifying lots to sell in each asset class\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████| 3/3 [00:00<00:00, 272.53it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Determining how much of each asset class to buy\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14592\\**********.py:409: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df_positions['expected_sales'] = df_positions['expected_sales'].fillna(0)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14592\\**********.py:409: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df_positions['expected_sales'] = df_positions['expected_sales'].fillna(0)\n"]}, {"data": {"text/markdown": ["# Preliminary warnings"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["  - <span style='color:red'>It looks like markets are currently     closed. The rebalancer will therefore not let you place any     orders. If it did, they would be queued, and would only     execute once the market re-opens, at which point prices might     have changed.</span> The algorithm we use to determine whether     markets are open isn't foolproof; to override it, modify the     `market_open` function in the code."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["  - <span style='color:red'>Make sure your portfolio is not set to     automatically re-invest dividends.</span> If it is, you might     inadvertently create a wash sale."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["# Portfolio rebalancing report"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["### 1. Summary by asset classes"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["Each row in the table below corresponds to a specific asset class we need in our final portfolio, and an extra row for all assets that are not in one of these \"target\" asset classes. For each asset, it provides the following details:\n", "\n", "  - **Market value**: the current market value of assets in this class.\n", "\n", "  - **Target %**: the target percentage of the final portfolio that     should comprise this asset class.\n", "\n", "  - **Current %**: the current percentage of the portfolio that comprises     this asset class. Note that this does *not* include assets in the     \"Other\" category, but *does* include cash.\n", "\n", "  - **Expected sales**: the $ amount of securities in this asset class that     we intend to sell to carry out tax loss harvesting. Further details are     provided in the forthcoming sections.\n", "\n", "  - **Expected balance**: the expected market value of securities in this     asset class *after* any expected sales described in the previous column.\n", "\n", "  - **Buy security**: the security we will be buying to replenish this asset     class.\n", "\n", "  - **Buy target**: the $ amount of the security in the previous column we     would ideally want to buy. This will be determined using a constrained     quadratic program; see the documentation.\n", "\n", "  - **Buy quantity**: the number of shares of the security in question we will     buy. This is obtained by dividing the previous column by the share price,     and rounding down.\n", "\n", "  - **Expected buy**: the expected $ amount of the security we will buy,     calculated by multiplying the quantity in the previous column by the price     of the security. This should be close to **Buy target**, but not exactly     equal to it because the quantity will have been rounded down.\n", "\n", "  - **Final balance**: the final market value of this asset class after sales     and purchases.\n", "\n", "  - **Final %**: the final percentage of the portfolio that will comprise this     asset class, after sales and purchases."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14592\\**********.py:409: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df_positions['expected_sales'] = df_positions['expected_sales'].fillna(0)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14592\\**********.py:464: FutureWarning: Downcasting object dtype arrays on .fillna, .ffill, .bfill is deprecated and will change in a future version. Call result.infer_objects(copy=False) instead. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  df['final_balance'] = df['expected_balance'] + df['expected_buy'].fillna(0)\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_1dc69_row0_col3, #T_1dc69_row1_col3, #T_1dc69_row2_col3, #T_1dc69_row3_col3, #T_1dc69_row4_col3, #T_1dc69_row5_col3, #T_1dc69_row6_col3, #T_1dc69_row7_col3 {\n", "  background: pink;\n", "  border-left: 2px solid black;\n", "}\n", "#T_1dc69_row0_col5, #T_1dc69_row0_col9, #T_1dc69_row1_col5, #T_1dc69_row1_col9, #T_1dc69_row2_col5, #T_1dc69_row2_col9, #T_1dc69_row3_col5, #T_1dc69_row3_col9, #T_1dc69_row4_col5, #T_1dc69_row4_col9, #T_1dc69_row5_col5, #T_1dc69_row5_col9, #T_1dc69_row6_col5, #T_1dc69_row6_col9, #T_1dc69_row7_col5, #T_1dc69_row7_col9 {\n", "  border-left: 2px solid black;\n", "}\n", "#T_1dc69_row0_col6, #T_1dc69_row0_col8, #T_1dc69_row1_col6, #T_1dc69_row1_col8, #T_1dc69_row2_col6, #T_1dc69_row2_col8, #T_1dc69_row3_col6, #T_1dc69_row3_col8, #T_1dc69_row4_col6, #T_1dc69_row4_col8, #T_1dc69_row5_col6, #T_1dc69_row5_col8, #T_1dc69_row6_col6, #T_1dc69_row6_col8, #T_1dc69_row7_col6, #T_1dc69_row7_col8 {\n", "  background: lightgreen;\n", "}\n", "#T_1dc69_row8_col0, #T_1dc69_row8_col1, #T_1dc69_row8_col2, #T_1dc69_row8_col4, #T_1dc69_row8_col7, #T_1dc69_row8_col10, #T_1dc69_row9_col0, #T_1dc69_row9_col1, #T_1dc69_row9_col2, #T_1dc69_row9_col4, #T_1dc69_row9_col7, #T_1dc69_row9_col10 {\n", "  font-style: italic;\n", "}\n", "#T_1dc69_row8_col3, #T_1dc69_row9_col3 {\n", "  font-style: italic;\n", "  background: pink;\n", "  border-left: 2px solid black;\n", "}\n", "#T_1dc69_row8_col5, #T_1dc69_row8_col9, #T_1dc69_row9_col5, #T_1dc69_row9_col9 {\n", "  font-style: italic;\n", "  border-left: 2px solid black;\n", "}\n", "#T_1dc69_row8_col6, #T_1dc69_row8_col8, #T_1dc69_row9_col6, #T_1dc69_row9_col8 {\n", "  font-style: italic;\n", "  background: lightgreen;\n", "}\n", "#T_1dc69_level0_col3 {\n", "  background: pink;\n", "  border-left: 2px solid black;\n", "}\n", "#T_1dc69_level0_col5, #T_1dc69_level0_col9 {\n", "  border-left: 2px solid black;\n", "}\n", "#T_1dc69_level0_col6, #T_1dc69_level0_col8 {\n", "  background: lightgreen;\n", "}\n", "</style>\n", "<table id=\"T_1dc69\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_1dc69_level0_col0\" class=\"col_heading level0 col0\" >Market value</th>\n", "      <th id=\"T_1dc69_level0_col1\" class=\"col_heading level0 col1\" >Target %</th>\n", "      <th id=\"T_1dc69_level0_col2\" class=\"col_heading level0 col2\" >Current %</th>\n", "      <th id=\"T_1dc69_level0_col3\" class=\"col_heading level0 col3\" >Expected sales</th>\n", "      <th id=\"T_1dc69_level0_col4\" class=\"col_heading level0 col4\" >Expected balance</th>\n", "      <th id=\"T_1dc69_level0_col5\" class=\"col_heading level0 col5\" >Buy security</th>\n", "      <th id=\"T_1dc69_level0_col6\" class=\"col_heading level0 col6\" >Buy target</th>\n", "      <th id=\"T_1dc69_level0_col7\" class=\"col_heading level0 col7\" >Buy quantity</th>\n", "      <th id=\"T_1dc69_level0_col8\" class=\"col_heading level0 col8\" >Expected buy</th>\n", "      <th id=\"T_1dc69_level0_col9\" class=\"col_heading level0 col9\" >Final balance</th>\n", "      <th id=\"T_1dc69_level0_col10\" class=\"col_heading level0 col10\" >Final %</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row0\" class=\"row_heading level0 row0\" >1. US Large Cap</th>\n", "      <td id=\"T_1dc69_row0_col0\" class=\"data row0 col0\" >$470,786.57</td>\n", "      <td id=\"T_1dc69_row0_col1\" class=\"data row0 col1\" >41%</td>\n", "      <td id=\"T_1dc69_row0_col2\" class=\"data row0 col2\" >40.59%</td>\n", "      <td id=\"T_1dc69_row0_col3\" class=\"data row0 col3\" ></td>\n", "      <td id=\"T_1dc69_row0_col4\" class=\"data row0 col4\" >$470,786.57</td>\n", "      <td id=\"T_1dc69_row0_col5\" class=\"data row0 col5\" >SCHX</td>\n", "      <td id=\"T_1dc69_row0_col6\" class=\"data row0 col6\" >$17,647.94</td>\n", "      <td id=\"T_1dc69_row0_col7\" class=\"data row0 col7\" >391</td>\n", "      <td id=\"T_1dc69_row0_col8\" class=\"data row0 col8\" >$17,624.33</td>\n", "      <td id=\"T_1dc69_row0_col9\" class=\"data row0 col9\" >$488,410.90</td>\n", "      <td id=\"T_1dc69_row0_col10\" class=\"data row0 col10\" >40.53%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row1\" class=\"row_heading level0 row1\" >2. US Mid Cap</th>\n", "      <td id=\"T_1dc69_row1_col0\" class=\"data row1 col0\" >$145,373.57</td>\n", "      <td id=\"T_1dc69_row1_col1\" class=\"data row1 col1\" >11%</td>\n", "      <td id=\"T_1dc69_row1_col2\" class=\"data row1 col2\" >12.53%</td>\n", "      <td id=\"T_1dc69_row1_col3\" class=\"data row1 col3\" ></td>\n", "      <td id=\"T_1dc69_row1_col4\" class=\"data row1 col4\" >$145,373.57</td>\n", "      <td id=\"T_1dc69_row1_col5\" class=\"data row1 col5\" >VO</td>\n", "      <td id=\"T_1dc69_row1_col6\" class=\"data row1 col6\" ></td>\n", "      <td id=\"T_1dc69_row1_col7\" class=\"data row1 col7\" ></td>\n", "      <td id=\"T_1dc69_row1_col8\" class=\"data row1 col8\" ></td>\n", "      <td id=\"T_1dc69_row1_col9\" class=\"data row1 col9\" >$145,373.57</td>\n", "      <td id=\"T_1dc69_row1_col10\" class=\"data row1 col10\" >12.06%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row2\" class=\"row_heading level0 row2\" >3. US Small Cap</th>\n", "      <td id=\"T_1dc69_row2_col0\" class=\"data row2 col0\" >$59,653.48</td>\n", "      <td id=\"T_1dc69_row2_col1\" class=\"data row2 col1\" >5%</td>\n", "      <td id=\"T_1dc69_row2_col2\" class=\"data row2 col2\" >5.14%</td>\n", "      <td id=\"T_1dc69_row2_col3\" class=\"data row2 col3\" ></td>\n", "      <td id=\"T_1dc69_row2_col4\" class=\"data row2 col4\" >$59,653.48</td>\n", "      <td id=\"T_1dc69_row2_col5\" class=\"data row2 col5\" >SCHA</td>\n", "      <td id=\"T_1dc69_row2_col6\" class=\"data row2 col6\" ></td>\n", "      <td id=\"T_1dc69_row2_col7\" class=\"data row2 col7\" ></td>\n", "      <td id=\"T_1dc69_row2_col8\" class=\"data row2 col8\" ></td>\n", "      <td id=\"T_1dc69_row2_col9\" class=\"data row2 col9\" >$59,653.48</td>\n", "      <td id=\"T_1dc69_row2_col10\" class=\"data row2 col10\" >4.95%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row3\" class=\"row_heading level0 row3\" >4. Int. Developed Mkts</th>\n", "      <td id=\"T_1dc69_row3_col0\" class=\"data row3 col0\" >$293,422.60</td>\n", "      <td id=\"T_1dc69_row3_col1\" class=\"data row3 col1\" >23%</td>\n", "      <td id=\"T_1dc69_row3_col2\" class=\"data row3 col2\" >25.3%</td>\n", "      <td id=\"T_1dc69_row3_col3\" class=\"data row3 col3\" ></td>\n", "      <td id=\"T_1dc69_row3_col4\" class=\"data row3 col4\" >$293,422.60</td>\n", "      <td id=\"T_1dc69_row3_col5\" class=\"data row3 col5\" >IEFA</td>\n", "      <td id=\"T_1dc69_row3_col6\" class=\"data row3 col6\" ></td>\n", "      <td id=\"T_1dc69_row3_col7\" class=\"data row3 col7\" ></td>\n", "      <td id=\"T_1dc69_row3_col8\" class=\"data row3 col8\" ></td>\n", "      <td id=\"T_1dc69_row3_col9\" class=\"data row3 col9\" >$293,422.60</td>\n", "      <td id=\"T_1dc69_row3_col10\" class=\"data row3 col10\" >24.35%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row4\" class=\"row_heading level0 row4\" >5. Int. Emerging Mkts</th>\n", "      <td id=\"T_1dc69_row4_col0\" class=\"data row4 col0\" >$45,341.76</td>\n", "      <td id=\"T_1dc69_row4_col1\" class=\"data row4 col1\" >5%</td>\n", "      <td id=\"T_1dc69_row4_col2\" class=\"data row4 col2\" >3.91%</td>\n", "      <td id=\"T_1dc69_row4_col3\" class=\"data row4 col3\" >$13,891.99</td>\n", "      <td id=\"T_1dc69_row4_col4\" class=\"data row4 col4\" >$31,449.77</td>\n", "      <td id=\"T_1dc69_row4_col5\" class=\"data row4 col5\" >VWO</td>\n", "      <td id=\"T_1dc69_row4_col6\" class=\"data row4 col6\" >$23,122.45</td>\n", "      <td id=\"T_1dc69_row4_col7\" class=\"data row4 col7\" >615</td>\n", "      <td id=\"T_1dc69_row4_col8\" class=\"data row4 col8\" >$23,087.71</td>\n", "      <td id=\"T_1dc69_row4_col9\" class=\"data row4 col9\" >$54,537.48</td>\n", "      <td id=\"T_1dc69_row4_col10\" class=\"data row4 col10\" >4.53%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row5\" class=\"row_heading level0 row5\" >6. Real Estate</th>\n", "      <td id=\"T_1dc69_row5_col0\" class=\"data row5 col0\" >$39,863.20</td>\n", "      <td id=\"T_1dc69_row5_col1\" class=\"data row5 col1\" >5%</td>\n", "      <td id=\"T_1dc69_row5_col2\" class=\"data row5 col2\" >3.44%</td>\n", "      <td id=\"T_1dc69_row5_col3\" class=\"data row5 col3\" ></td>\n", "      <td id=\"T_1dc69_row5_col4\" class=\"data row5 col4\" >$39,863.20</td>\n", "      <td id=\"T_1dc69_row5_col5\" class=\"data row5 col5\" >SCHH</td>\n", "      <td id=\"T_1dc69_row5_col6\" class=\"data row5 col6\" >$14,709.02</td>\n", "      <td id=\"T_1dc69_row5_col7\" class=\"data row5 col7\" >767</td>\n", "      <td id=\"T_1dc69_row5_col8\" class=\"data row5 col8\" >$14,699.55</td>\n", "      <td id=\"T_1dc69_row5_col9\" class=\"data row5 col9\" >$54,562.75</td>\n", "      <td id=\"T_1dc69_row5_col10\" class=\"data row5 col10\" >4.53%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row6\" class=\"row_heading level0 row6\" >7. Fixed Income Short Trm</th>\n", "      <td id=\"T_1dc69_row6_col0\" class=\"data row6 col0\" >$30,534.57</td>\n", "      <td id=\"T_1dc69_row6_col1\" class=\"data row6 col1\" >3%</td>\n", "      <td id=\"T_1dc69_row6_col2\" class=\"data row6 col2\" >2.63%</td>\n", "      <td id=\"T_1dc69_row6_col3\" class=\"data row6 col3\" >$30,534.57</td>\n", "      <td id=\"T_1dc69_row6_col4\" class=\"data row6 col4\" >$-0.00</td>\n", "      <td id=\"T_1dc69_row6_col5\" class=\"data row6 col5\" >SHM</td>\n", "      <td id=\"T_1dc69_row6_col6\" class=\"data row6 col6\" >$30,468.76</td>\n", "      <td id=\"T_1dc69_row6_col7\" class=\"data row6 col7\" >669</td>\n", "      <td id=\"T_1dc69_row6_col8\" class=\"data row6 col8\" >$30,466.26</td>\n", "      <td id=\"T_1dc69_row6_col9\" class=\"data row6 col9\" >$30,466.26</td>\n", "      <td id=\"T_1dc69_row6_col10\" class=\"data row6 col10\" >2.53%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row7\" class=\"row_heading level0 row7\" >8. Fixed Income Mid+Long Trm</th>\n", "      <td id=\"T_1dc69_row7_col0\" class=\"data row7 col0\" >$74,972.84</td>\n", "      <td id=\"T_1dc69_row7_col1\" class=\"data row7 col1\" >7%</td>\n", "      <td id=\"T_1dc69_row7_col2\" class=\"data row7 col2\" >6.46%</td>\n", "      <td id=\"T_1dc69_row7_col3\" class=\"data row7 col3\" ></td>\n", "      <td id=\"T_1dc69_row7_col4\" class=\"data row7 col4\" >$74,972.84</td>\n", "      <td id=\"T_1dc69_row7_col5\" class=\"data row7 col5\" >VTEB</td>\n", "      <td id=\"T_1dc69_row7_col6\" class=\"data row7 col6\" >$3,702.84</td>\n", "      <td id=\"T_1dc69_row7_col7\" class=\"data row7 col7\" >77</td>\n", "      <td id=\"T_1dc69_row7_col8\" class=\"data row7 col8\" >$3,677.01</td>\n", "      <td id=\"T_1dc69_row7_col9\" class=\"data row7 col9\" >$78,649.85</td>\n", "      <td id=\"T_1dc69_row7_col10\" class=\"data row7 col10\" >6.53%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row8\" class=\"row_heading level0 row8\" >Other</th>\n", "      <td id=\"T_1dc69_row8_col0\" class=\"data row8 col0\" >$42,767.58</td>\n", "      <td id=\"T_1dc69_row8_col1\" class=\"data row8 col1\" ></td>\n", "      <td id=\"T_1dc69_row8_col2\" class=\"data row8 col2\" ></td>\n", "      <td id=\"T_1dc69_row8_col3\" class=\"data row8 col3\" >$5,224.45</td>\n", "      <td id=\"T_1dc69_row8_col4\" class=\"data row8 col4\" >$37,543.13</td>\n", "      <td id=\"T_1dc69_row8_col5\" class=\"data row8 col5\" ></td>\n", "      <td id=\"T_1dc69_row8_col6\" class=\"data row8 col6\" ></td>\n", "      <td id=\"T_1dc69_row8_col7\" class=\"data row8 col7\" ></td>\n", "      <td id=\"T_1dc69_row8_col8\" class=\"data row8 col8\" ></td>\n", "      <td id=\"T_1dc69_row8_col9\" class=\"data row8 col9\" >$37,543.13</td>\n", "      <td id=\"T_1dc69_row8_col10\" class=\"data row8 col10\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1dc69_level0_row9\" class=\"row_heading level0 row9\" >Cash</th>\n", "      <td id=\"T_1dc69_row9_col0\" class=\"data row9 col0\" >$40,000.00</td>\n", "      <td id=\"T_1dc69_row9_col1\" class=\"data row9 col1\" ></td>\n", "      <td id=\"T_1dc69_row9_col2\" class=\"data row9 col2\" ></td>\n", "      <td id=\"T_1dc69_row9_col3\" class=\"data row9 col3\" >$40,000.00</td>\n", "      <td id=\"T_1dc69_row9_col4\" class=\"data row9 col4\" ></td>\n", "      <td id=\"T_1dc69_row9_col5\" class=\"data row9 col5\" ></td>\n", "      <td id=\"T_1dc69_row9_col6\" class=\"data row9 col6\" ></td>\n", "      <td id=\"T_1dc69_row9_col7\" class=\"data row9 col7\" ></td>\n", "      <td id=\"T_1dc69_row9_col8\" class=\"data row9 col8\" ></td>\n", "      <td id=\"T_1dc69_row9_col9\" class=\"data row9 col9\" >$96.15</td>\n", "      <td id=\"T_1dc69_row9_col10\" class=\"data row9 col10\" ></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x22782ef69f0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["The graph below summarizes the current and final deviations from the target portfolio"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["### 2. Security-by-security summary"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["Each row below corresponds to one security, separated by target asset class. Every security in the current portfolio will be listed in this table, whether  it appears in the target portfolio or not.\n", "\n", "  - Column legend\n", "\n", "       * **Badness**: the user-specified badness of that security within its asset class.\n", "\n", "       * **30 days**: whether this security was bought (`B`) or sold (`S`) in the last 30 days.\n", "\n", "       * **Quantity**, **Market value**: the quantity of that security currently owned, and the current          market value of those securities; any empty entries represent securities that are not owned.\n", "\n", "       * **Lots loss**, **Lots gain**: the sum of all losses/gains for all losing/gaining tax lots for         this security.\n", "\n", "  - The security highlighted in <span style=\"background:lightgreen\">green</span> in each section denotes     the security that will be **bought** during rebalancing.\n", "\n", "  - Any cells highlighted in <span style=\"background:pink\">pink</span> denote assets for which at least some     tax lots that will be **sold** during rebalancing. More details on these tax lots are provided in the     next section."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_ee607_row0_col0, #T_ee607_row0_col1, #T_ee607_row0_col2, #T_ee607_row0_col3, #T_ee607_row0_col4, #T_ee607_row0_col5, #T_ee607_row2_col0, #T_ee607_row2_col1, #T_ee607_row2_col2, #T_ee607_row2_col3, #T_ee607_row2_col5, #T_ee607_row3_col0, #T_ee607_row3_col1, #T_ee607_row3_col2, #T_ee607_row3_col3, #T_ee607_row3_col4, #T_ee607_row3_col5, #T_ee607_row4_col0, #T_ee607_row4_col1, #T_ee607_row4_col2, #T_ee607_row4_col3, #T_ee607_row4_col4, #T_ee607_row4_col5, #T_ee607_row5_col0, #T_ee607_row5_col1, #T_ee607_row5_col2, #T_ee607_row5_col3, #T_ee607_row5_col4, #T_ee607_row5_col5, #T_ee607_row7_col0, #T_ee607_row7_col1, #T_ee607_row7_col2, #T_ee607_row7_col3, #T_ee607_row7_col4, #T_ee607_row7_col5, #T_ee607_row8_col0, #T_ee607_row8_col1, #T_ee607_row8_col2, #T_ee607_row8_col3, #T_ee607_row8_col4, #T_ee607_row8_col5, #T_ee607_row9_col0, #T_ee607_row9_col1, #T_ee607_row9_col2, #T_ee607_row9_col3, #T_ee607_row9_col4, #T_ee607_row9_col5, #T_ee607_row10_col0, #T_ee607_row10_col1, #T_ee607_row10_col2, #T_ee607_row10_col3, #T_ee607_row10_col4, #T_ee607_row10_col5, #T_ee607_row11_col0, #T_ee607_row11_col1, #T_ee607_row11_col2, #T_ee607_row11_col3, #T_ee607_row11_col4, #T_ee607_row11_col5, #T_ee607_row13_col0, #T_ee607_row13_col1, #T_ee607_row13_col2, #T_ee607_row13_col3, #T_ee607_row13_col4, #T_ee607_row13_col5, #T_ee607_row14_col0, #T_ee607_row14_col1, #T_ee607_row14_col2, #T_ee607_row14_col3, #T_ee607_row14_col4, #T_ee607_row14_col5, #T_ee607_row15_col0, #T_ee607_row15_col1, #T_ee607_row15_col2, #T_ee607_row15_col3, #T_ee607_row15_col4, #T_ee607_row15_col5, #T_ee607_row17_col0, #T_ee607_row17_col1, #T_ee607_row17_col2, #T_ee607_row17_col3, #T_ee607_row17_col4, #T_ee607_row17_col5, #T_ee607_row20_col0, #T_ee607_row20_col1, #T_ee607_row20_col2, #T_ee607_row20_col3, #T_ee607_row20_col5, #T_ee607_row21_col0, #T_ee607_row21_col1, #T_ee607_row21_col2, #T_ee607_row21_col3, #T_ee607_row21_col4, #T_ee607_row21_col5, #T_ee607_row22_col0, #T_ee607_row22_col1, #T_ee607_row22_col2, #T_ee607_row22_col3, #T_ee607_row22_col4, #T_ee607_row22_col5, #T_ee607_row23_col0, #T_ee607_row23_col1, #T_ee607_row23_col2, #T_ee607_row23_col3, #T_ee607_row23_col4, #T_ee607_row23_col5, #T_ee607_row25_col0, #T_ee607_row25_col1, #T_ee607_row25_col2, #T_ee607_row25_col3, #T_ee607_row25_col4, #T_ee607_row25_col5, #T_ee607_row26_col0, #T_ee607_row26_col1, #T_ee607_row26_col2, #T_ee607_row26_col3, #T_ee607_row26_col5, #T_ee607_row27_col0, #T_ee607_row27_col1, #T_ee607_row27_col2, #T_ee607_row27_col3, #T_ee607_row27_col4, #T_ee607_row27_col5, #T_ee607_row29_col0, #T_ee607_row29_col1, #T_ee607_row29_col2, #T_ee607_row29_col3, #T_ee607_row29_col4, #T_ee607_row29_col5, #T_ee607_row31_col0, #T_ee607_row31_col1, #T_ee607_row31_col2, #T_ee607_row31_col3, #T_ee607_row31_col4, #T_ee607_row31_col5, #T_ee607_row33_col0, #T_ee607_row33_col1, #T_ee607_row33_col2, #T_ee607_row33_col3, #T_ee607_row33_col4, #T_ee607_row33_col5 {\n", "  border-bottom: 3px solid black;\n", "}\n", "#T_ee607_row1_col0, #T_ee607_row1_col1, #T_ee607_row1_col2, #T_ee607_row1_col3, #T_ee607_row1_col4, #T_ee607_row1_col5, #T_ee607_row12_col0, #T_ee607_row12_col1, #T_ee607_row12_col2, #T_ee607_row12_col3, #T_ee607_row12_col4, #T_ee607_row12_col5, #T_ee607_row16_col0, #T_ee607_row16_col1, #T_ee607_row16_col2, #T_ee607_row16_col3, #T_ee607_row16_col4, #T_ee607_row16_col5, #T_ee607_row18_col0, #T_ee607_row18_col1, #T_ee607_row18_col2, #T_ee607_row18_col3, #T_ee607_row18_col4, #T_ee607_row18_col5, #T_ee607_row28_col0, #T_ee607_row28_col1, #T_ee607_row28_col2, #T_ee607_row28_col3, #T_ee607_row28_col4, #T_ee607_row28_col5, #T_ee607_row30_col0, #T_ee607_row30_col1, #T_ee607_row30_col2, #T_ee607_row30_col3, #T_ee607_row30_col4, #T_ee607_row30_col5, #T_ee607_row32_col0, #T_ee607_row32_col1, #T_ee607_row32_col2, #T_ee607_row32_col3, #T_ee607_row32_col4, #T_ee607_row32_col5 {\n", "  background: lightgreen;\n", "  border-bottom: 3px solid black;\n", "}\n", "#T_ee607_row2_col4, #T_ee607_row20_col4, #T_ee607_row26_col4 {\n", "  background: pink;\n", "  border-bottom: 3px solid black;\n", "}\n", "#T_ee607_row19_col0, #T_ee607_row19_col1, #T_ee607_row19_col2, #T_ee607_row19_col3, #T_ee607_row19_col4, #T_ee607_row19_col5 {\n", "  background: lightgreen;\n", "}\n", "#T_ee607_level0_row0, #T_ee607_level1_row0, #T_ee607_level1_row2, #T_ee607_level1_row3, #T_ee607_level1_row4, #T_ee607_level0_row5, #T_ee607_level1_row5, #T_ee607_level1_row7, #T_ee607_level1_row8, #T_ee607_level0_row9, #T_ee607_level1_row9, #T_ee607_level1_row10, #T_ee607_level1_row11, #T_ee607_level1_row13, #T_ee607_level0_row14, #T_ee607_level1_row14, #T_ee607_level1_row15, #T_ee607_level1_row17, #T_ee607_level0_row18, #T_ee607_level1_row20, #T_ee607_level0_row21, #T_ee607_level1_row21, #T_ee607_level1_row22, #T_ee607_level1_row23, #T_ee607_level1_row25, #T_ee607_level1_row26, #T_ee607_level0_row27, #T_ee607_level1_row27, #T_ee607_level1_row29, #T_ee607_level0_row31, #T_ee607_level1_row31, #T_ee607_level1_row33 {\n", "  border-bottom: 3px solid black;\n", "}\n", "#T_ee607_level1_row1, #T_ee607_level1_row12, #T_ee607_level1_row16, #T_ee607_level1_row18, #T_ee607_level1_row28, #T_ee607_level1_row30, #T_ee607_level1_row32 {\n", "  background: lightgreen;\n", "  border-bottom: 3px solid black;\n", "}\n", "#T_ee607_level1_row19 {\n", "  background: lightgreen;\n", "}\n", "#T_ee607_level0_col0, #T_ee607_level0_col1 {\n", "  transform: rotate(180deg);\n", "  writing-mode: vertical-rl;\n", "}\n", "</style>\n", "<table id=\"T_ee607\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_ee607_level0_col0\" class=\"col_heading level0 col0\" >Badness</th>\n", "      <th id=\"T_ee607_level0_col1\" class=\"col_heading level0 col1\" >30 days</th>\n", "      <th id=\"T_ee607_level0_col2\" class=\"col_heading level0 col2\" >Quantity</th>\n", "      <th id=\"T_ee607_level0_col3\" class=\"col_heading level0 col3\" >Market value</th>\n", "      <th id=\"T_ee607_level0_col4\" class=\"col_heading level0 col4\" >Lots loss</th>\n", "      <th id=\"T_ee607_level0_col5\" class=\"col_heading level0 col5\" >Lots gain</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >assetclass</th>\n", "      <th class=\"index_name level1\" >symbol</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row0\" class=\"row_heading level0 row0\" rowspan=\"5\">1. US Large Cap</th>\n", "      <th id=\"T_ee607_level1_row0\" class=\"row_heading level1 row0\" >IVV</th>\n", "      <td id=\"T_ee607_row0_col0\" class=\"data row0 col0\" >1</td>\n", "      <td id=\"T_ee607_row0_col1\" class=\"data row0 col1\" >S</td>\n", "      <td id=\"T_ee607_row0_col2\" class=\"data row0 col2\" ></td>\n", "      <td id=\"T_ee607_row0_col3\" class=\"data row0 col3\" ></td>\n", "      <td id=\"T_ee607_row0_col4\" class=\"data row0 col4\" ></td>\n", "      <td id=\"T_ee607_row0_col5\" class=\"data row0 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row1\" class=\"row_heading level1 row1\" >SCHX</th>\n", "      <td id=\"T_ee607_row1_col0\" class=\"data row1 col0\" >2</td>\n", "      <td id=\"T_ee607_row1_col1\" class=\"data row1 col1\" ></td>\n", "      <td id=\"T_ee607_row1_col2\" class=\"data row1 col2\" ></td>\n", "      <td id=\"T_ee607_row1_col3\" class=\"data row1 col3\" ></td>\n", "      <td id=\"T_ee607_row1_col4\" class=\"data row1 col4\" ></td>\n", "      <td id=\"T_ee607_row1_col5\" class=\"data row1 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row2\" class=\"row_heading level1 row2\" >VV</th>\n", "      <td id=\"T_ee607_row2_col0\" class=\"data row2 col0\" >3</td>\n", "      <td id=\"T_ee607_row2_col1\" class=\"data row2 col1\" >B</td>\n", "      <td id=\"T_ee607_row2_col2\" class=\"data row2 col2\" >2699</td>\n", "      <td id=\"T_ee607_row2_col3\" class=\"data row2 col3\" >$470,786.57</td>\n", "      <td id=\"T_ee607_row2_col4\" class=\"data row2 col4\" ></td>\n", "      <td id=\"T_ee607_row2_col5\" class=\"data row2 col5\" >$33,691.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row3\" class=\"row_heading level1 row3\" >VOO</th>\n", "      <td id=\"T_ee607_row3_col0\" class=\"data row3 col0\" >4</td>\n", "      <td id=\"T_ee607_row3_col1\" class=\"data row3 col1\" ></td>\n", "      <td id=\"T_ee607_row3_col2\" class=\"data row3 col2\" ></td>\n", "      <td id=\"T_ee607_row3_col3\" class=\"data row3 col3\" ></td>\n", "      <td id=\"T_ee607_row3_col4\" class=\"data row3 col4\" ></td>\n", "      <td id=\"T_ee607_row3_col5\" class=\"data row3 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row4\" class=\"row_heading level1 row4\" >IWB</th>\n", "      <td id=\"T_ee607_row4_col0\" class=\"data row4 col0\" ></td>\n", "      <td id=\"T_ee607_row4_col1\" class=\"data row4 col1\" ></td>\n", "      <td id=\"T_ee607_row4_col2\" class=\"data row4 col2\" ></td>\n", "      <td id=\"T_ee607_row4_col3\" class=\"data row4 col3\" ></td>\n", "      <td id=\"T_ee607_row4_col4\" class=\"data row4 col4\" ></td>\n", "      <td id=\"T_ee607_row4_col5\" class=\"data row4 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row5\" class=\"row_heading level0 row5\" rowspan=\"4\">2. US Mid Cap</th>\n", "      <th id=\"T_ee607_level1_row5\" class=\"row_heading level1 row5\" >IJH</th>\n", "      <td id=\"T_ee607_row5_col0\" class=\"data row5 col0\" >1</td>\n", "      <td id=\"T_ee607_row5_col1\" class=\"data row5 col1\" >S</td>\n", "      <td id=\"T_ee607_row5_col2\" class=\"data row5 col2\" ></td>\n", "      <td id=\"T_ee607_row5_col3\" class=\"data row5 col3\" ></td>\n", "      <td id=\"T_ee607_row5_col4\" class=\"data row5 col4\" ></td>\n", "      <td id=\"T_ee607_row5_col5\" class=\"data row5 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row6\" class=\"row_heading level1 row6\" >VO</th>\n", "      <td id=\"T_ee607_row6_col0\" class=\"data row6 col0\" >2</td>\n", "      <td id=\"T_ee607_row6_col1\" class=\"data row6 col1\" >B</td>\n", "      <td id=\"T_ee607_row6_col2\" class=\"data row6 col2\" >713</td>\n", "      <td id=\"T_ee607_row6_col3\" class=\"data row6 col3\" >$145,373.57</td>\n", "      <td id=\"T_ee607_row6_col4\" class=\"data row6 col4\" ></td>\n", "      <td id=\"T_ee607_row6_col5\" class=\"data row6 col5\" >$8,280.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row7\" class=\"row_heading level1 row7\" >SCHM</th>\n", "      <td id=\"T_ee607_row7_col0\" class=\"data row7 col0\" >3</td>\n", "      <td id=\"T_ee607_row7_col1\" class=\"data row7 col1\" ></td>\n", "      <td id=\"T_ee607_row7_col2\" class=\"data row7 col2\" ></td>\n", "      <td id=\"T_ee607_row7_col3\" class=\"data row7 col3\" ></td>\n", "      <td id=\"T_ee607_row7_col4\" class=\"data row7 col4\" ></td>\n", "      <td id=\"T_ee607_row7_col5\" class=\"data row7 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row8\" class=\"row_heading level1 row8\" >IWR</th>\n", "      <td id=\"T_ee607_row8_col0\" class=\"data row8 col0\" ></td>\n", "      <td id=\"T_ee607_row8_col1\" class=\"data row8 col1\" ></td>\n", "      <td id=\"T_ee607_row8_col2\" class=\"data row8 col2\" ></td>\n", "      <td id=\"T_ee607_row8_col3\" class=\"data row8 col3\" ></td>\n", "      <td id=\"T_ee607_row8_col4\" class=\"data row8 col4\" ></td>\n", "      <td id=\"T_ee607_row8_col5\" class=\"data row8 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row9\" class=\"row_heading level0 row9\" rowspan=\"5\">3. US Small Cap</th>\n", "      <th id=\"T_ee607_level1_row9\" class=\"row_heading level1 row9\" >IJR</th>\n", "      <td id=\"T_ee607_row9_col0\" class=\"data row9 col0\" >1</td>\n", "      <td id=\"T_ee607_row9_col1\" class=\"data row9 col1\" >S</td>\n", "      <td id=\"T_ee607_row9_col2\" class=\"data row9 col2\" ></td>\n", "      <td id=\"T_ee607_row9_col3\" class=\"data row9 col3\" ></td>\n", "      <td id=\"T_ee607_row9_col4\" class=\"data row9 col4\" ></td>\n", "      <td id=\"T_ee607_row9_col5\" class=\"data row9 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row10\" class=\"row_heading level1 row10\" >SCHA</th>\n", "      <td id=\"T_ee607_row10_col0\" class=\"data row10 col0\" >2</td>\n", "      <td id=\"T_ee607_row10_col1\" class=\"data row10 col1\" >B</td>\n", "      <td id=\"T_ee607_row10_col2\" class=\"data row10 col2\" >1443</td>\n", "      <td id=\"T_ee607_row10_col3\" class=\"data row10 col3\" >$59,653.48</td>\n", "      <td id=\"T_ee607_row10_col4\" class=\"data row10 col4\" ></td>\n", "      <td id=\"T_ee607_row10_col5\" class=\"data row10 col5\" >$3,580.23</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row11\" class=\"row_heading level1 row11\" >VB</th>\n", "      <td id=\"T_ee607_row11_col0\" class=\"data row11 col0\" >3</td>\n", "      <td id=\"T_ee607_row11_col1\" class=\"data row11 col1\" ></td>\n", "      <td id=\"T_ee607_row11_col2\" class=\"data row11 col2\" ></td>\n", "      <td id=\"T_ee607_row11_col3\" class=\"data row11 col3\" ></td>\n", "      <td id=\"T_ee607_row11_col4\" class=\"data row11 col4\" ></td>\n", "      <td id=\"T_ee607_row11_col5\" class=\"data row11 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row12\" class=\"row_heading level1 row12\" >IWM</th>\n", "      <td id=\"T_ee607_row12_col0\" class=\"data row12 col0\" ></td>\n", "      <td id=\"T_ee607_row12_col1\" class=\"data row12 col1\" ></td>\n", "      <td id=\"T_ee607_row12_col2\" class=\"data row12 col2\" ></td>\n", "      <td id=\"T_ee607_row12_col3\" class=\"data row12 col3\" ></td>\n", "      <td id=\"T_ee607_row12_col4\" class=\"data row12 col4\" ></td>\n", "      <td id=\"T_ee607_row12_col5\" class=\"data row12 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row13\" class=\"row_heading level1 row13\" >VXF</th>\n", "      <td id=\"T_ee607_row13_col0\" class=\"data row13 col0\" ></td>\n", "      <td id=\"T_ee607_row13_col1\" class=\"data row13 col1\" ></td>\n", "      <td id=\"T_ee607_row13_col2\" class=\"data row13 col2\" ></td>\n", "      <td id=\"T_ee607_row13_col3\" class=\"data row13 col3\" ></td>\n", "      <td id=\"T_ee607_row13_col4\" class=\"data row13 col4\" ></td>\n", "      <td id=\"T_ee607_row13_col5\" class=\"data row13 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row14\" class=\"row_heading level0 row14\" rowspan=\"4\">4. Int. Developed Mkts</th>\n", "      <th id=\"T_ee607_level1_row14\" class=\"row_heading level1 row14\" >VEA</th>\n", "      <td id=\"T_ee607_row14_col0\" class=\"data row14 col0\" >1</td>\n", "      <td id=\"T_ee607_row14_col1\" class=\"data row14 col1\" >S</td>\n", "      <td id=\"T_ee607_row14_col2\" class=\"data row14 col2\" >1032</td>\n", "      <td id=\"T_ee607_row14_col3\" class=\"data row14 col3\" >$41,470.92</td>\n", "      <td id=\"T_ee607_row14_col4\" class=\"data row14 col4\" ></td>\n", "      <td id=\"T_ee607_row14_col5\" class=\"data row14 col5\" >$5,650.37</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row15\" class=\"row_heading level1 row15\" >IEFA</th>\n", "      <td id=\"T_ee607_row15_col0\" class=\"data row15 col0\" >2</td>\n", "      <td id=\"T_ee607_row15_col1\" class=\"data row15 col1\" ></td>\n", "      <td id=\"T_ee607_row15_col2\" class=\"data row15 col2\" ></td>\n", "      <td id=\"T_ee607_row15_col3\" class=\"data row15 col3\" ></td>\n", "      <td id=\"T_ee607_row15_col4\" class=\"data row15 col4\" ></td>\n", "      <td id=\"T_ee607_row15_col5\" class=\"data row15 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row16\" class=\"row_heading level1 row16\" >SCHF</th>\n", "      <td id=\"T_ee607_row16_col0\" class=\"data row16 col0\" >3</td>\n", "      <td id=\"T_ee607_row16_col1\" class=\"data row16 col1\" >B</td>\n", "      <td id=\"T_ee607_row16_col2\" class=\"data row16 col2\" >8117</td>\n", "      <td id=\"T_ee607_row16_col3\" class=\"data row16 col3\" >$251,951.68</td>\n", "      <td id=\"T_ee607_row16_col4\" class=\"data row16 col4\" ></td>\n", "      <td id=\"T_ee607_row16_col5\" class=\"data row16 col5\" >$22,087.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row17\" class=\"row_heading level1 row17\" >VEU</th>\n", "      <td id=\"T_ee607_row17_col0\" class=\"data row17 col0\" >4</td>\n", "      <td id=\"T_ee607_row17_col1\" class=\"data row17 col1\" ></td>\n", "      <td id=\"T_ee607_row17_col2\" class=\"data row17 col2\" ></td>\n", "      <td id=\"T_ee607_row17_col3\" class=\"data row17 col3\" ></td>\n", "      <td id=\"T_ee607_row17_col4\" class=\"data row17 col4\" ></td>\n", "      <td id=\"T_ee607_row17_col5\" class=\"data row17 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row18\" class=\"row_heading level0 row18\" rowspan=\"3\">5. Int. Emerging Mkts</th>\n", "      <th id=\"T_ee607_level1_row18\" class=\"row_heading level1 row18\" >VWO</th>\n", "      <td id=\"T_ee607_row18_col0\" class=\"data row18 col0\" >1</td>\n", "      <td id=\"T_ee607_row18_col1\" class=\"data row18 col1\" >B</td>\n", "      <td id=\"T_ee607_row18_col2\" class=\"data row18 col2\" >318</td>\n", "      <td id=\"T_ee607_row18_col3\" class=\"data row18 col3\" >$11,938.04</td>\n", "      <td id=\"T_ee607_row18_col4\" class=\"data row18 col4\" >$-721.12</td>\n", "      <td id=\"T_ee607_row18_col5\" class=\"data row18 col5\" >$131.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row19\" class=\"row_heading level1 row19\" >IEMG</th>\n", "      <td id=\"T_ee607_row19_col0\" class=\"data row19 col0\" >2</td>\n", "      <td id=\"T_ee607_row19_col1\" class=\"data row19 col1\" ></td>\n", "      <td id=\"T_ee607_row19_col2\" class=\"data row19 col2\" >743</td>\n", "      <td id=\"T_ee607_row19_col3\" class=\"data row19 col3\" >$33,403.72</td>\n", "      <td id=\"T_ee607_row19_col4\" class=\"data row19 col4\" >$-283.45</td>\n", "      <td id=\"T_ee607_row19_col5\" class=\"data row19 col5\" >$1,022.71</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row20\" class=\"row_heading level1 row20\" >SCHE</th>\n", "      <td id=\"T_ee607_row20_col0\" class=\"data row20 col0\" >3</td>\n", "      <td id=\"T_ee607_row20_col1\" class=\"data row20 col1\" ></td>\n", "      <td id=\"T_ee607_row20_col2\" class=\"data row20 col2\" ></td>\n", "      <td id=\"T_ee607_row20_col3\" class=\"data row20 col3\" ></td>\n", "      <td id=\"T_ee607_row20_col4\" class=\"data row20 col4\" ></td>\n", "      <td id=\"T_ee607_row20_col5\" class=\"data row20 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row21\" class=\"row_heading level0 row21\" rowspan=\"4\">6. Real Estate</th>\n", "      <th id=\"T_ee607_level1_row21\" class=\"row_heading level1 row21\" >VNQ</th>\n", "      <td id=\"T_ee607_row21_col0\" class=\"data row21 col0\" >1</td>\n", "      <td id=\"T_ee607_row21_col1\" class=\"data row21 col1\" >S</td>\n", "      <td id=\"T_ee607_row21_col2\" class=\"data row21 col2\" ></td>\n", "      <td id=\"T_ee607_row21_col3\" class=\"data row21 col3\" ></td>\n", "      <td id=\"T_ee607_row21_col4\" class=\"data row21 col4\" ></td>\n", "      <td id=\"T_ee607_row21_col5\" class=\"data row21 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row22\" class=\"row_heading level1 row22\" >SCHH</th>\n", "      <td id=\"T_ee607_row22_col0\" class=\"data row22 col0\" >2</td>\n", "      <td id=\"T_ee607_row22_col1\" class=\"data row22 col1\" >B</td>\n", "      <td id=\"T_ee607_row22_col2\" class=\"data row22 col2\" >2080</td>\n", "      <td id=\"T_ee607_row22_col3\" class=\"data row22 col3\" >$39,863.20</td>\n", "      <td id=\"T_ee607_row22_col4\" class=\"data row22 col4\" ></td>\n", "      <td id=\"T_ee607_row22_col5\" class=\"data row22 col5\" >$2,277.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row23\" class=\"row_heading level1 row23\" >USRT</th>\n", "      <td id=\"T_ee607_row23_col0\" class=\"data row23 col0\" >3</td>\n", "      <td id=\"T_ee607_row23_col1\" class=\"data row23 col1\" ></td>\n", "      <td id=\"T_ee607_row23_col2\" class=\"data row23 col2\" ></td>\n", "      <td id=\"T_ee607_row23_col3\" class=\"data row23 col3\" ></td>\n", "      <td id=\"T_ee607_row23_col4\" class=\"data row23 col4\" ></td>\n", "      <td id=\"T_ee607_row23_col5\" class=\"data row23 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row24\" class=\"row_heading level1 row24\" >RWR</th>\n", "      <td id=\"T_ee607_row24_col0\" class=\"data row24 col0\" ></td>\n", "      <td id=\"T_ee607_row24_col1\" class=\"data row24 col1\" ></td>\n", "      <td id=\"T_ee607_row24_col2\" class=\"data row24 col2\" ></td>\n", "      <td id=\"T_ee607_row24_col3\" class=\"data row24 col3\" ></td>\n", "      <td id=\"T_ee607_row24_col4\" class=\"data row24 col4\" ></td>\n", "      <td id=\"T_ee607_row24_col5\" class=\"data row24 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row25\" class=\"row_heading level0 row25\" rowspan=\"2\">7. Fixed Income Short Trm</th>\n", "      <th id=\"T_ee607_level1_row25\" class=\"row_heading level1 row25\" >SUB</th>\n", "      <td id=\"T_ee607_row25_col0\" class=\"data row25 col0\" >1</td>\n", "      <td id=\"T_ee607_row25_col1\" class=\"data row25 col1\" ></td>\n", "      <td id=\"T_ee607_row25_col2\" class=\"data row25 col2\" >297</td>\n", "      <td id=\"T_ee607_row25_col3\" class=\"data row25 col3\" >$30,534.57</td>\n", "      <td id=\"T_ee607_row25_col4\" class=\"data row25 col4\" >$-551.16</td>\n", "      <td id=\"T_ee607_row25_col5\" class=\"data row25 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row26\" class=\"row_heading level1 row26\" >SHM</th>\n", "      <td id=\"T_ee607_row26_col0\" class=\"data row26 col0\" >2</td>\n", "      <td id=\"T_ee607_row26_col1\" class=\"data row26 col1\" ></td>\n", "      <td id=\"T_ee607_row26_col2\" class=\"data row26 col2\" ></td>\n", "      <td id=\"T_ee607_row26_col3\" class=\"data row26 col3\" ></td>\n", "      <td id=\"T_ee607_row26_col4\" class=\"data row26 col4\" ></td>\n", "      <td id=\"T_ee607_row26_col5\" class=\"data row26 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row27\" class=\"row_heading level0 row27\" rowspan=\"4\">8. Fixed Income Mid+Long Trm</th>\n", "      <th id=\"T_ee607_level1_row27\" class=\"row_heading level1 row27\" >MUB</th>\n", "      <td id=\"T_ee607_row27_col0\" class=\"data row27 col0\" >1</td>\n", "      <td id=\"T_ee607_row27_col1\" class=\"data row27 col1\" >S</td>\n", "      <td id=\"T_ee607_row27_col2\" class=\"data row27 col2\" ></td>\n", "      <td id=\"T_ee607_row27_col3\" class=\"data row27 col3\" ></td>\n", "      <td id=\"T_ee607_row27_col4\" class=\"data row27 col4\" ></td>\n", "      <td id=\"T_ee607_row27_col5\" class=\"data row27 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row28\" class=\"row_heading level1 row28\" >VTEB</th>\n", "      <td id=\"T_ee607_row28_col0\" class=\"data row28 col0\" >2</td>\n", "      <td id=\"T_ee607_row28_col1\" class=\"data row28 col1\" >B</td>\n", "      <td id=\"T_ee607_row28_col2\" class=\"data row28 col2\" >1570</td>\n", "      <td id=\"T_ee607_row28_col3\" class=\"data row28 col3\" >$74,972.84</td>\n", "      <td id=\"T_ee607_row28_col4\" class=\"data row28 col4\" >$-1,101.51</td>\n", "      <td id=\"T_ee607_row28_col5\" class=\"data row28 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row29\" class=\"row_heading level1 row29\" >TFI</th>\n", "      <td id=\"T_ee607_row29_col0\" class=\"data row29 col0\" >3</td>\n", "      <td id=\"T_ee607_row29_col1\" class=\"data row29 col1\" ></td>\n", "      <td id=\"T_ee607_row29_col2\" class=\"data row29 col2\" ></td>\n", "      <td id=\"T_ee607_row29_col3\" class=\"data row29 col3\" ></td>\n", "      <td id=\"T_ee607_row29_col4\" class=\"data row29 col4\" ></td>\n", "      <td id=\"T_ee607_row29_col5\" class=\"data row29 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row30\" class=\"row_heading level1 row30\" >ITM</th>\n", "      <td id=\"T_ee607_row30_col0\" class=\"data row30 col0\" ></td>\n", "      <td id=\"T_ee607_row30_col1\" class=\"data row30 col1\" ></td>\n", "      <td id=\"T_ee607_row30_col2\" class=\"data row30 col2\" ></td>\n", "      <td id=\"T_ee607_row30_col3\" class=\"data row30 col3\" ></td>\n", "      <td id=\"T_ee607_row30_col4\" class=\"data row30 col4\" ></td>\n", "      <td id=\"T_ee607_row30_col5\" class=\"data row30 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level0_row31\" class=\"row_heading level0 row31\" rowspan=\"3\">Not in target portfolio</th>\n", "      <th id=\"T_ee607_level1_row31\" class=\"row_heading level1 row31\" >Cash</th>\n", "      <td id=\"T_ee607_row31_col0\" class=\"data row31 col0\" ></td>\n", "      <td id=\"T_ee607_row31_col1\" class=\"data row31 col1\" ></td>\n", "      <td id=\"T_ee607_row31_col2\" class=\"data row31 col2\" ></td>\n", "      <td id=\"T_ee607_row31_col3\" class=\"data row31 col3\" >$40,000.00</td>\n", "      <td id=\"T_ee607_row31_col4\" class=\"data row31 col4\" ></td>\n", "      <td id=\"T_ee607_row31_col5\" class=\"data row31 col5\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row32\" class=\"row_heading level1 row32\" >SCHD</th>\n", "      <td id=\"T_ee607_row32_col0\" class=\"data row32 col0\" ></td>\n", "      <td id=\"T_ee607_row32_col1\" class=\"data row32 col1\" ></td>\n", "      <td id=\"T_ee607_row32_col2\" class=\"data row32 col2\" >184</td>\n", "      <td id=\"T_ee607_row32_col3\" class=\"data row32 col3\" >$13,809.20</td>\n", "      <td id=\"T_ee607_row32_col4\" class=\"data row32 col4\" ></td>\n", "      <td id=\"T_ee607_row32_col5\" class=\"data row32 col5\" >$3,022.54</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_ee607_level1_row33\" class=\"row_heading level1 row33\" >VIG</th>\n", "      <td id=\"T_ee607_row33_col0\" class=\"data row33 col0\" ></td>\n", "      <td id=\"T_ee607_row33_col1\" class=\"data row33 col1\" >S</td>\n", "      <td id=\"T_ee607_row33_col2\" class=\"data row33 col2\" >194</td>\n", "      <td id=\"T_ee607_row33_col3\" class=\"data row33 col3\" >$28,958.38</td>\n", "      <td id=\"T_ee607_row33_col4\" class=\"data row33 col4\" >$-178.72</td>\n", "      <td id=\"T_ee607_row33_col5\" class=\"data row33 col5\" >$9,816.76</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x22786104fe0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["### 3. Specific lots to sell"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["The following summarizes the positions we will sell:\n", "\n", "  - Total lots: **12**.\n", "\n", "  - Total harvested losses: **\\\\$-1,013.33** (including **\\\\$0.00** of realized gains).\n", "\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_69d5b_row1_col0, #T_69d5b_row1_col1, #T_69d5b_row1_col2, #T_69d5b_row1_col3, #T_69d5b_row7_col0, #T_69d5b_row7_col1, #T_69d5b_row7_col2, #T_69d5b_row7_col3, #T_69d5b_row11_col0, #T_69d5b_row11_col1, #T_69d5b_row11_col2, #T_69d5b_row11_col3 {\n", "  border-bottom: 3px solid black;\n", "}\n", "#T_69d5b_level0_row0, #T_69d5b_level1_row1, #T_69d5b_level0_row2, #T_69d5b_level1_row7, #T_69d5b_level0_row8, #T_69d5b_level1_row11 {\n", "  border-bottom: 3px solid black;\n", "}\n", "</style>\n", "<table id=\"T_69d5b\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_69d5b_level0_col0\" class=\"col_heading level0 col0\" >Acquired date</th>\n", "      <th id=\"T_69d5b_level0_col1\" class=\"col_heading level0 col1\" >Quantity</th>\n", "      <th id=\"T_69d5b_level0_col2\" class=\"col_heading level0 col2\" >Market value</th>\n", "      <th id=\"T_69d5b_level0_col3\" class=\"col_heading level0 col3\" >Gain</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Symbol</th>\n", "      <th class=\"index_name level1\" >Lot ID</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_69d5b_level0_row0\" class=\"row_heading level0 row0\" rowspan=\"2\">IEMG</th>\n", "      <th id=\"T_69d5b_level1_row0\" class=\"row_heading level1 row0\" >764195865158</th>\n", "      <td id=\"T_69d5b_row0_col0\" class=\"data row0 col0\" >2017-03-13</td>\n", "      <td id=\"T_69d5b_row0_col1\" class=\"data row0 col1\" >243</td>\n", "      <td id=\"T_69d5b_row0_col2\" class=\"data row0 col2\" >$10,924.77</td>\n", "      <td id=\"T_69d5b_row0_col3\" class=\"data row0 col3\" >$-214.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row1\" class=\"row_heading level1 row1\" >264242557407</th>\n", "      <td id=\"T_69d5b_row1_col0\" class=\"data row1 col0\" >2018-12-26</td>\n", "      <td id=\"T_69d5b_row1_col1\" class=\"data row1 col1\" >66</td>\n", "      <td id=\"T_69d5b_row1_col2\" class=\"data row1 col2\" >$2,967.22</td>\n", "      <td id=\"T_69d5b_row1_col3\" class=\"data row1 col3\" >$-69.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level0_row2\" class=\"row_heading level0 row2\" rowspan=\"6\">VIG</th>\n", "      <th id=\"T_69d5b_level1_row2\" class=\"row_heading level1 row2\" >487776486996</th>\n", "      <td id=\"T_69d5b_row2_col0\" class=\"data row2 col0\" >2021-04-02</td>\n", "      <td id=\"T_69d5b_row2_col1\" class=\"data row2 col1\" >7</td>\n", "      <td id=\"T_69d5b_row2_col2\" class=\"data row2 col2\" >$1,044.89</td>\n", "      <td id=\"T_69d5b_row2_col3\" class=\"data row2 col3\" >$-5.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row3\" class=\"row_heading level1 row3\" >544316587884</th>\n", "      <td id=\"T_69d5b_row3_col0\" class=\"data row3 col0\" >2021-04-08</td>\n", "      <td id=\"T_69d5b_row3_col1\" class=\"data row3 col1\" >8</td>\n", "      <td id=\"T_69d5b_row3_col2\" class=\"data row3 col2\" >$1,194.16</td>\n", "      <td id=\"T_69d5b_row3_col3\" class=\"data row3 col3\" >$-11.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row4\" class=\"row_heading level1 row4\" >483247261265</th>\n", "      <td id=\"T_69d5b_row4_col0\" class=\"data row4 col0\" >2021-04-15</td>\n", "      <td id=\"T_69d5b_row4_col1\" class=\"data row4 col1\" >5</td>\n", "      <td id=\"T_69d5b_row4_col2\" class=\"data row4 col2\" >$746.35</td>\n", "      <td id=\"T_69d5b_row4_col3\" class=\"data row4 col3\" >$-14.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row5\" class=\"row_heading level1 row5\" >381035100674</th>\n", "      <td id=\"T_69d5b_row5_col0\" class=\"data row5 col0\" >2021-05-01</td>\n", "      <td id=\"T_69d5b_row5_col1\" class=\"data row5 col1\" >7</td>\n", "      <td id=\"T_69d5b_row5_col2\" class=\"data row5 col2\" >$1,044.89</td>\n", "      <td id=\"T_69d5b_row5_col3\" class=\"data row5 col3\" >$-23.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row6\" class=\"row_heading level1 row6\" >483716176266</th>\n", "      <td id=\"T_69d5b_row6_col0\" class=\"data row6 col0\" >2021-10-29</td>\n", "      <td id=\"T_69d5b_row6_col1\" class=\"data row6 col1\" >2</td>\n", "      <td id=\"T_69d5b_row6_col2\" class=\"data row6 col2\" >$298.54</td>\n", "      <td id=\"T_69d5b_row6_col3\" class=\"data row6 col3\" >$-31.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row7\" class=\"row_heading level1 row7\" >904050246804</th>\n", "      <td id=\"T_69d5b_row7_col0\" class=\"data row7 col0\" >2021-11-01</td>\n", "      <td id=\"T_69d5b_row7_col1\" class=\"data row7 col1\" >6</td>\n", "      <td id=\"T_69d5b_row7_col2\" class=\"data row7 col2\" >$895.62</td>\n", "      <td id=\"T_69d5b_row7_col3\" class=\"data row7 col3\" >$-93.24</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level0_row8\" class=\"row_heading level0 row8\" rowspan=\"4\">SUB</th>\n", "      <th id=\"T_69d5b_level1_row8\" class=\"row_heading level1 row8\" >534730837835</th>\n", "      <td id=\"T_69d5b_row8_col0\" class=\"data row8 col0\" >2022-07-12</td>\n", "      <td id=\"T_69d5b_row8_col1\" class=\"data row8 col1\" >273</td>\n", "      <td id=\"T_69d5b_row8_col2\" class=\"data row8 col2\" >$28,067.13</td>\n", "      <td id=\"T_69d5b_row8_col3\" class=\"data row8 col3\" >$-511.88</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row9\" class=\"row_heading level1 row9\" >987003807048</th>\n", "      <td id=\"T_69d5b_row9_col0\" class=\"data row9 col0\" >2022-07-06</td>\n", "      <td id=\"T_69d5b_row9_col1\" class=\"data row9 col1\" >6</td>\n", "      <td id=\"T_69d5b_row9_col2\" class=\"data row9 col2\" >$616.86</td>\n", "      <td id=\"T_69d5b_row9_col3\" class=\"data row9 col3\" >$-11.27</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row10\" class=\"row_heading level1 row10\" >567536607333</th>\n", "      <td id=\"T_69d5b_row10_col0\" class=\"data row10 col0\" >2022-08-01</td>\n", "      <td id=\"T_69d5b_row10_col1\" class=\"data row10 col1\" >6</td>\n", "      <td id=\"T_69d5b_row10_col2\" class=\"data row10 col2\" >$616.86</td>\n", "      <td id=\"T_69d5b_row10_col3\" class=\"data row10 col3\" >$-14.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_69d5b_level1_row11\" class=\"row_heading level1 row11\" >651605073186</th>\n", "      <td id=\"T_69d5b_row11_col0\" class=\"data row11 col0\" >2022-09-11</td>\n", "      <td id=\"T_69d5b_row11_col1\" class=\"data row11 col1\" >12</td>\n", "      <td id=\"T_69d5b_row11_col2\" class=\"data row11 col2\" >$1,233.72</td>\n", "      <td id=\"T_69d5b_row11_col3\" class=\"data row11 col3\" >$-13.32</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x22785f9eb10>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["# Next steps"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["When you are ready to re-balance, run the `rebalance` method of this object. It will first sell the lots above, and then immediately buy the assets described above."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["<span style='color:red'>You must run this function as quickly as possible after first creating the rebalancing object, to ensure prices do not shift between the time the rebalancing amounts are calculated, and the time the trades are executed. If you wait too long, the previewed transactions will expire and eTrade will not let you carry out any transaction.</span>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["acc = Account()\n", "port = Rebalancer(acc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}