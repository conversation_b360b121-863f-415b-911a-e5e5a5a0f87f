#!/usr/bin/env python
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import backtrader as bt
from backtrader.analyzers import TaxEfficiency
import pandas as pd

# Create simple test data
test_data = pd.DataFrame({
    'open': [100, 101, 102, 103, 104] * 10,
    'high': [101, 102, 103, 104, 105] * 10,
    'low': [99, 100, 101, 102, 103] * 10,
    'close': [100, 101, 102, 103, 104] * 10,
    'volume': [10000] * 50
}, index=pd.date_range('2023-01-01', periods=50, freq='D'))

class SimpleStrategy(bt.Strategy):
    def __init__(self):
        self.day_count = 0
    
    def next(self):
        self.day_count += 1
        if self.day_count == 10:
            self.buy(size=100)
        elif self.day_count == 30:
            self.sell(size=50)

# Setup cerebro
cerebro = bt.Cerebro()
data = bt.feeds.PandasData(dataname=test_data)
cerebro.adddata(data)
cerebro.addstrategy(SimpleStrategy)

# Add TaxEfficiency analyzer
cerebro.addanalyzer(TaxEfficiency,
                   capital_gains_tax_rate=0.20,
                   include_transaction_costs=True,
                   european_compliance=True,
                   _name='taxeff')

cerebro.broker.setcash(100000.0)

# Run and examine results
results = cerebro.run()
strategy = results[0]
analyzer = strategy.analyzers.taxeff
analysis = analyzer.get_analysis()

print("=== Full Analysis Structure ===")
def print_dict_structure(d, prefix="", max_depth=3, current_depth=0):
    if current_depth >= max_depth:
        print(f"{prefix}... (max depth reached)")
        return
    
    if isinstance(d, dict):
        for key, value in d.items():
            if isinstance(value, dict):
                print(f"{prefix}{key}:")
                print_dict_structure(value, prefix + "  ", max_depth, current_depth + 1)
            else:
                print(f"{prefix}{key}: {value}")
    else:
        print(f"{prefix}{d}")

print_dict_structure(analysis)

print("\n=== Testing Different Access Methods ===")

# Try different ways to access the data
print(f"Keys in analysis: {list(analysis.keys())}")

for key in analysis.keys():
    try:
        value = analysis[key]
        print(f"analysis['{key}'] = {value}")
        if isinstance(value, dict):
            for subkey in value.keys():
                print(f"  analysis['{key}']['{subkey}'] = {value[subkey]}")
    except Exception as e:
        print(f"Error accessing analysis['{key}']: {e}")

print("\n=== Checking convenience methods ===")
try:
    efficiency_score = analyzer.get_efficiency_score()
    print(f"get_efficiency_score(): {efficiency_score}")
except Exception as e:
    print(f"Error with get_efficiency_score(): {e}")

try:
    tax_drag = analyzer.get_tax_drag()
    print(f"get_tax_drag(): {tax_drag}")
except Exception as e:
    print(f"Error with get_tax_drag(): {e}")

print("\n=== Print Analysis Method ===")
try:
    analyzer.print_analysis()
except Exception as e:
    print(f"Error with print_analysis(): {e}") 