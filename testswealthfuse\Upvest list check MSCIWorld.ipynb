{"cells": [{"cell_type": "code", "execution_count": 6, "id": "6d413435", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matched Tickers: ['<PERSON><PERSON>', '<PERSON><PERSON>', 'U<PERSON>', 'SW<PERSON>', '<PERSON><PERSON><PERSON>', 'NVD<PERSON>', 'J<PERSON>', '<PERSON><PERSON>', 'NOW', 'L<PERSON>', 'CHRW', 'OMC', 'EQT', 'AME', '<PERSON>', 'MC', 'R<PERSON>', 'NDAQ', 'G<PERSON><PERSON>', 'BEN', 'LAND', 'CLX', 'FBK', 'NWG', 'CARR', 'ZBRA', 'TW', 'JPM', 'ENI', 'ORK', 'STT', 'APD', 'ZAL', 'STAN', 'LNT', 'LLY', 'SK', 'TLS', 'SMIN', 'III', 'CNA', 'WISE', 'NVR', 'ERF', 'ENPH', 'DIA', 'ML', 'FNF', 'WEC', 'VICI', 'RSG', 'DHI', 'EDP', 'LULU', 'ABX', 'HIK', 'APP', 'AM', 'BB<PERSON>', 'IP', 'MRO', '<PERSON>Q<PERSON>', 'CF', 'ORSTED', 'CMS', 'G1A', 'AFG', 'SNAP', 'TER', 'TAP', 'WAB', 'AC', 'UBER', 'HLT', 'TJX', 'ON', 'BRO', 'VRSK', 'TWLO', 'TTE', 'BAYN', 'NOK', 'ACA', 'META', 'NOC', 'BILL', 'AON', 'ED', 'HRL', 'WELL', 'TMO', 'DDOG', 'TFC', 'ASX', 'U', 'SCHW', 'RCO', 'L', 'KKR', 'HEN3', 'DEMANT', 'TDY', 'KNEBV', 'MDB', 'EDV', 'MT', 'TROW', 'FDJ', 'K', 'GEN', 'CMCSA', 'VOW3', 'NTAP', 'GPC', 'IHG', 'EXPE', 'TRU', 'BXP', 'TFX', 'TRI', 'IFF', 'PHNX', 'CTVA', 'CTLT', 'MDT', 'SU', 'WDC', 'BA', 'JMT', 'PPG', 'CS', 'CZR', 'ADSK', 'MAR', 'BG', 'G', 'GDDY', 'GD', 'BSL', 'AD', 'NKE', 'LMT', 'GILD', 'SMCI', 'WST', 'UDR', 'AXP', 'IQV', 'SSNC', 'ROK', 'GJF', 'EDEN', 'EA', 'RMS', 'PEG', 'UPM', 'BKNG', 'DD', 'ALNY', 'APA', 'ROP', 'EQIX', 'Z', 'PAYC', 'VIV', 'MCK', 'RTO', 'ATD', 'AJG', 'LGEN', 'PNDORA', 'CMI', 'BSY', 'EOG', 'PSX', 'CCK', 'MDLZ', 'ITX', 'IBM', 'GLW', 'DTG', 'METSO', 'VWS', 'WLN', 'PHM', 'ORI', 'SO', 'FME', 'COST', 'TTD', 'LOTB', 'GLE', 'REG', 'GS', 'SBUX', 'ES', 'WMT', 'RS', 'RACE', 'SYK', 'KDP', 'URW', 'G24', 'ADI', 'PPL', 'NDSN', 'CEG', 'LW', 'DKNG', 'FMC', 'BVI', 'STLD', 'CNR', 'WFC', 'CDW', 'COP', 'BNR', 'PWR', 'ABT', 'SHEL', 'LYB', 'CSL', 'MTCH', 'NXT', 'INTU', 'ELISA', 'SNPS', 'DVN', 'FANG', 'AZO', 'FOX', 'RWE', 'CA', 'LNG', 'WRK', 'MRNA', 'DOC', 'LLOY', 'HON', 'ELV', 'HSY', 'GFC', 'FICO', 'MCHP', 'JKHY', 'P911', 'CVS', 'NEE', 'WRB', 'FCNCA', 'ABN', 'TRV', 'AMH', 'J', 'PUB', 'AHT', 'MBG', 'MOH', 'HST', 'SOBI', 'FUTU', 'MRVL', 'AIR', 'AMP', 'DUK', 'ILMN', 'HAS', 'PKG', 'NBIX', 'AAL', 'TIT', 'ASML', 'VOD', 'PLTR', 'KO', '1COV', 'HII', 'HNR1', 'AVY', 'OR', 'FDS', 'MKL', 'RPRX', 'CTSH', 'SYF', 'PCTY', 'GET', 'RED', 'ELS', 'UCB', 'VER', 'DBX', 'ALLE', 'ANSS', 'EFX', 'LUV', 'KNX', 'LEA', 'LII', 'SALM', 'URI', 'COR', 'DOW', 'LBRDK', 'IAG', 'DAL', 'AVB', 'CMG', 'GMAB', 'SQ', 'BATS', 'ACS', 'NOKIA', 'KBX', 'BWA', 'VRT', 'EVRG', 'TEVA', 'IEX', 'KMX', 'MTB', 'EIX', 'UMI', 'FE', 'NSC', 'BHP', 'BURL', 'IPG', 'PARA', 'DE', 'VRSN', 'ANA', 'EXR', 'STMPA', 'SPGI', 'TGT', 'WMB', 'BIRG', 'BT/A', 'AZN', 'PUM', 'JDEP', 'O', 'ETN', 'INDT', 'BR', 'RGEN', 'JCI', 'AKAM', 'BALL', 'AXON', 'CINF', 'KER', 'NLY', 'T', 'D', 'AMAT', 'OXY', 'BARC', 'F', 'TMUS', 'YAR', 'WTW', 'MNG', 'ADYEN', 'SAN', 'CNH', 'HBAN', 'ENGI', 'CAP', 'ETSY', 'GM', 'FIS', 'NWSA', 'TEP', 'SOL', 'MOWI', 'WSO', 'CPRT', 'CHD', 'OCDO', 'HD', 'HWM', 'DRI', 'RI', 'REGN', 'GPN', 'SYY', 'HOLX', 'ABDN', 'CSCO', 'PNR', 'LSCC', 'DELL', 'AIZ', 'BK', 'LHX', 'EW', 'LIN', 'CRM', 'DHER', 'EVK', 'CE', 'ZBH', 'REP', 'HUBS', 'STE', 'CNP', 'FGR', 'PATH', 'WY', 'DASH', 'OVV', 'RF', 'BNZL', 'AAPL', 'MONC', 'CPG', 'WPC', 'VTRS', 'RIO', 'CME', 'CRH', 'HES', 'LSEG', 'OC', 'MEL', 'GSK', 'EDPR', 'HIG', 'DGX', 'DB1', 'SNA', 'GEHC', 'WKL', 'WM', 'SBAC', 'H', 'ABBV', 'TOST', 'BDX', 'STJ', 'ADE', 'ELI', 'ZTS', 'NHY', 'WBA', 'AVGO', 'ORA', 'DGE', 'XYL', 'JBHT', 'ENX', 'PSN', 'RHI', 'FI', 'ABI', 'UNP', 'IR', 'OKE', 'BIM', 'AEE', 'LYV', 'MET', 'AFX', 'INTC', 'CLF', 'BAMI', 'AMS', 'SE', 'FITB', 'NFLX', 'ALV', 'AMZN', 'INCY', 'BDEV', 'SY1', 'DTE', 'DINO', 'GWW', 'NXPI', 'PRU', 'CAT', 'PG', 'UPS', 'ROST', 'ATO', 'DOV', 'DFS', 'ANTO', 'MAS', 'AOS', 'BBY', 'MRK', 'HLN', 'LOW', 'TYL', 'BSX', 'EPAM', 'FDX', 'CSGP', 'MOS', 'EQH', 'BNP', 'KLAC', 'COIN', 'ASM', 'CHTR', 'VMC', 'QCOM', 'VNA', 'CHK', 'MGM', 'IPN', 'CFR', 'AENA', 'CAG', 'BKR', 'DBK', 'EBO', 'PAYX', 'TXT', 'FWONK', 'PCAR', 'BAC', 'CCL', 'PCG', 'CLNX', 'MPWR', 'KR', 'DHL', 'TEF', 'BKG', 'PH', 'BMRN', 'TRGP', 'ULTA', 'CDNS', 'SBRY', 'TRMB', 'ETR', 'DNB', 'APH', 'HOOD', 'AMGN', 'IBE', 'WDAY', 'CRL', 'FTV', 'FTNT', 'DAR', 'AMUN', 'ZM', 'MA', 'MUV2', 'IT', 'EXPD', 'KPN', 'ORCL', 'KHC', 'AKRBP', 'TXN', 'GALP', 'CPT', 'PAH3', 'BAS', 'TSCO', 'EMR', 'OTIS', 'SHW', 'ISRG', 'FFIV', 'HCA', 'MKTX', 'HPE', 'ENR', 'SAP', 'RHM', 'ELE', 'JAZZ', 'SNOW', 'COV', 'NI', 'VRTX', 'EMN', 'CBK', 'MTD', 'GL', 'CAH', 'ACM', 'MO', 'DIS', 'MSI', 'CSX', 'FBIN', 'OMV', 'DLTR', 'KMB', 'OKTA', 'LDOS', 'AKE', 'WTB', 'MB', 'IRM', 'KVUE', 'GIL', 'CPB', 'ACI', 'CI', 'ICE', 'ALFA', 'KOG', 'HEIO', 'OCI', 'ANET', 'RAA', 'MTX', 'CBOE', 'EQNR', 'FSLR', 'LEG', 'HO', 'AIG', 'DHR', 'UMG', 'KEYS', 'AMD', 'FCX', 'DECK', 'USB', 'RPM', 'BIO', 'NN', 'AMT', 'KGF', 'TTC', 'VST', 'CRDA', 'DKS', 'ULVR', 'ADM', 'PRY', 'RAND', 'BMW', 'PEP', 'KIM', 'MCD', 'ODFL', 'ADBE', 'SJM', 'ZS', 'TSN', 'FPH', 'IFX', 'ROL', 'PRX', 'GOOGL', 'EL', 'BEI', 'CON', 'LRCX', 'CFG', 'SW', 'PANW', 'ADP', 'WOW', 'INVH', 'DG', 'JNJ', 'ENEL', 'MU', 'ABF', 'WSM', 'BIIB', 'TTWO', 'APO', 'BESI', 'AES', 'GIS', 'AZPN', 'SGO', 'HLMA', 'TT', 'LI', 'EBS', 'TECH', 'LEN', 'CL', 'PNC', 'AEP', 'NEXI', 'NTRS', 'ABNB', 'VOW', 'GOOG', 'DPZ', 'EVO', 'CRWD', 'BLK', 'BX', 'SIE', 'FOXA', 'SPG', 'SDR', 'WTRG', 'EN', 'IDXX', 'SAF', 'KEY', 'AFL', 'RJF', 'ALB', 'MAA', 'BAX', 'DIE', 'ESS', 'RTX', 'X', 'MMC', 'WAT', 'AVTR', 'ECL', 'MMM', 'ENG', 'PGR', 'PYPL', 'AI', 'KMI', 'MPC', 'NET', 'ORLY', 'POOL', 'PSON', 'CTRA', 'PST', 'WYNN', 'NESTE', 'BMW3', 'MANH', 'CFLT', 'HAL', 'ALLY', 'DOCU', 'HSIC', 'COF', 'PODD', 'BN', 'ITW', 'DSV', 'VLO', 'HEI', 'MSFT', 'PTC', 'ARE', 'BOL', 'UNH', 'BAH', 'V', 'PLD', 'YUM', 'TPL', 'PFE', 'SVT', 'SHL', 'BMY', 'FAST', 'MNST', 'NEM', 'TRN', 'HUM', 'GRF', 'DIM', 'INW', 'CBRE', 'NUE', 'PSA', 'MLM', 'DAY', 'SRT3', 'COL', 'CNC', nan, 'LPLA', 'WBD', 'IMCD', 'CG', 'BLDR', 'TSLA', 'GGG', 'VIE', 'SRG', 'LHA', 'MSCI', 'VEEV', 'ARGX', 'SRE', 'TLX', 'TEL', 'QRVO', 'ASRNL']\n", "Missing Tickers: [1, 2, 3, 4, 6146, 6, 'SPX', 11, 12, '<PERSON><PERSON><PERSON><PERSON>', 'ESSITY B', 16, 19, 27, 'C07', 'I<PERSON>', 6178, 'UCG', 'PFG', 'C09', 4151, 'GPT', 'BB<PERSON>', 6201, 'SCA B', 'SPK', 'VCX', 66, 'MPL', 'CCO', 'CYBR', 'QBE', 8267, 'SGP', 'ALD', 83, 4183, 'INVE B', 'SAMPO', 4188, 'SLF', 101, 4204, 'NPI', 8306, 'EMA', 8308, 8309, 8316, 'CM', 'NZD', 6273, 8331, 'J36', 'CSU', 'HSBA', 'RBLX', 6301, 6305, 'CHF', 'TLC', 'LR', 'EXO', 'CHKP', 'BMO', 6326, 'VGM4', 'SLB', 'ACN', 'BA.', '<PERSON><PERSON>', 4307, '<PERSON><PERSON>', 8411, 2267, 2269, 6367, 'INGA', 4324, 'EUR', 'D05', 6383, 'FM', 'GLEN', 'BAER', 'QAN', 'NG.', 'VOLV B', 8473, 'GMG', 'TEAM', 'LUND B', 288, 'SGSN', 'RXL', 'HELN', 6448, 'NSIS B', 'ZURN', 'CVX', 'DSCT', 6465, 'ALGN', 'WES', 'TIH', 'MKC', 'CAR.UN', 6479, 'STZ', 2388, 'EPI A', 'TD', 'FRE', 'WIX', 4452, 6501, 'ESLT', 6503, 6504, 6506, 'RO', 2413, 'ALL', 'SKF B', 'STX', 'NESN', 'ARX', 'WSP', 388, 8591, 'ORG', 8593, 'SHB A', 4502, 4503, 8601, 4507, 8604, 'GEV', 'GFL', 'S32', 'INDU A', 4519, 'TWODF', 'NST', 4523, 'FER', 'HKD', 4528, 'EMSN', 8630, 'REI.UN', 'SEIC', 6586, 'CP', 4543, 'ADEN', 'TECK.B', 'HOLN', 6594, 'LOGN', 'BANB', 2502, 2503, 'HOLM B', 'LKQ', 'POLI', 4568, 'BKW', 'BC8', 4578, 'LBTYK', 'WFG', 'MAERSK A', 'LIFCO B', 500, 6645, 'QSR', 8697, 'TFII', 'BEIJ B', 'QBR.B', 'WCN', 'RHC', 4612, 'HEN', 'DXCM', 'WLK', 'SCHP', 'HUBB', 'MARGIN_JPY', 'ENB', 8725, 'CAR', 'DSY', 2587, 'NTR', 'U96', 'SECU B', 'BP.', 'ERIC B', 6701, 8750, 6702, 'MRU', 'KBC', 4661, 'ENTG', 8766, 'SOON', 'SGRO', 6723, 6724, 4684, 4689, 'HEXA B', 'OTEX', 'VZ', 8795, 'FTS', 'COO', 8801, 8802, 'EXAS', 4704, 6752, 'AZRG', 6753, 6758, 'WTC', 6762, 4716, 4732, 8830, 'PKI', 'BEPC', 'MGR', 'SDZ', 'GRMN', 2702, 'TELIA', 4755, 'TRP', 6806, 669, 'NDA FI', 4768, 6823, 'UBSG', 'LISP', 'CCI', 6841, 6845, 'BALN', 'DLR', 'PINS', 'GIB.A', 6857, 'SIKA', 6861, 'FMG', 6869, 'AWK', 'CNQ', 2801, 2802, 6902, 8951, 8952, 8953, 'TOU', 'JPY', 'ILS', 6920, 8972, 'ICL', 'S68', 'BXB', 'MCO', 'ARES', 'HEIA', 4901, 9001, 'DSFIR', 6954, 9005, 9007, 4911, 9009, 'AUD', 6963, 6965, 'MG', 823, 'HM B', 9020, 9021, 9022, 6971, 'AER', 'GWO', 'REL', 'QIA', 6981, 'FNV', 6988, 'RNO', 9041, 9042, 2897, 'STLAM', 'RR.', 2914, 7011, 9064, 'BRKB', 'ALC', 'RMD', 'ORNBV', 'UTHR', 'REA', 'ISP', 9101, 'BRBY', 9104, 9107, 'ATCO A', 5019, 'EOAN', 5020, 'TPM4', 'CABK', 'RIVN', 'VLTO', 9143, 9147, 3003, 'TDG', 'EG', 'FFH', 'HL.', 'REH', 3038, 'IOT', 'LATO B', 9201, 9202, 5108, 'SPSN', 'MS', 'IMO', 3064, 'SCMN', '-', 7181, 1038, 7182, 3088, 'AIA', 'SEK', 7186, 3092, 'NRG', 7201, 7202, 7203, 'COH', 'ITRK', 'STERV', 'CCEP', 'GLBE', 'VOLCAR B', 'UHRN', 'MFC', 5201, 1113, 7259, 7261, 'PGHN', 'AUTO', 7267, 7269, 7270, 7272, 7276, 'VACN', 'AZJ', 'JD.', 'MIN', 'MZTF', 'STMN', 'CCL.B', 'ANZ', 7309, 'TRYG', 'A', 'CPU', 'EFN', 'COLO B', 3231, 'FLTR', 'ONEX', 'EXPN', 'NGPN', 'SGD', 'SIRI', 'SREN', 'TEN', 'CB', 'SEB A', 'CAD', 3281, 'NIBE B', 3283, 5332, 'VTR', 'ROG', 9432, 9433, 9434, 9435, 3291, 'ERIE', 'MTN', 'ALODS', 'TWE', 'ICSUAGD', 'LH', 'GBLB', 'BAM', 'CCH', 1299, 5401, 'Z M4', 1308, 'FORTUM', 9502, 9503, 9501, 'IMB', 'BN4', 5411, 3382, 9531, 9532, 'SOLV', 'CLN', 'JNPR', 'M44U', 3402, 'GIVN', 'ALO', 'LUN', 3407, 'AGS', 'SCG', 'VOE', 'IVN', 'CVE', 'CARL B', 'EXC', 'INF', 3436, 7532, 'POW', 7550, 9602, 'MAERSK B', 'BCE', 3462, '9CI', 'DOL', 'HPQ', 'RBA', 9613, 'XRO', 'CPR', 'APTV', 'EPI B', 'C', 'NOVO B', 'RVTY', 'SCHN', 'WDS', 'FERG', 'GETI B', 'SKA B', 'DT', 'BNS', 'GRAB', 'STN', 9684, 'CTAS', 'PAAS', 9697, 'MCY', 'G13', 9719, 'BARN', 'TEL2 B', 'O39', 9735, 'WPM', 'RCL', 'HUSQ B', 'C6L', 'JHX', 'DXS', 7701, 'SOF', 'PHIA', 'ABBN', 'FSV', 9766, 'WRT1V', 3626, 'AV.', 'SSE', 'KRX', 7733, 7735, 'TEMN', 'SWED A', 7741, 7747, 'TCL', 1605, 7751, 'ACGL', 'SIGN', 7752, 3659, 'CBA', 'RCI.B', 5713, 'AMCR', 'CPAY', 'MEG', 'EBAY', 'BZFUT', 'BFB', 'SAGA B', 9843, 'A17U', 'CELH', 'AEM', 'PM', 'F34', 'SK3', 7832, 'GEBN', 5802, 'MNDI', 'H78', 'ENT', 'SN.', 'CTC.A', 'KNIN', '2299955D', 'VOLV A', 'INDU C', 5831, 'SHOP', 'CAE', 'NOVN', 'LUMI', 7911, 7912, 'A5G', 9962, 'LSXMK', 'STO', 9983, 9984, 'RKT', 'ESM4', 1801, 1802, 1803, 'SWKS', 'MELI', 'DANSKE', 7951, 'WN', 1812, 'WDP', 'IFC', 'RY', 'ROCK B', 'C38U', 1821, 'NAB', 'AGN', 7974, 'KYGA', 'NICE', 'UU.', 8001, 8002, 'ADS', 8015, 'DCC', 'MQG', 'ATCO B', 1878, 'GBP', 'WPP', 'UHALB', 'USD', 8031, 8035, 'N2IU', 'ALA', 'UHR', 'EMP.A', 8053, 'BCVN', 8058, 'SVW', 'Z74', 1925, 1928, 'LISN', 'MNDY', 'CU', 'LDO', 'DVA', 'DSG', 8113, 'AVOL', 1972, 4021, 'SLHN', 'SAAB B', 'SYENS', 'DOO', 'BALD B', 'WBC', 'LONN', 'UOB', 1997, 6098, 'S63', 'ASSA B', 'PLS', 4062, 4063, 'SAND', 'SUN', 4091, 'AKZA', 'DKK']\n"]}], "source": ["import pandas as pd\n", "\n", "# Function to read MSCI World tickers from a excel file\n", "def get_msci_world_tickers(file_path):\n", "    msci_world_df = pd.read_excel(file_path)\n", "    msci_world_tickers = msci_world_df['Ticker'].tolist()\n", "    return msci_world_tickers\n", "\n", "# Function to check if MSCI World tickers are in our DataFrame\n", "def check_msci_world_in_df(df, msci_world_tickers):\n", "    df_tickers = df['Ticker'].tolist()\n", "    \n", "    matches_ticker = set(df_tickers).intersection(set(msci_world_tickers))\n", "    missing_tickers = set(msci_world_tickers) - matches_ticker\n", "    \n", "    return list(matches_ticker), list(missing_tickers)\n", "\n", "# Load your DataFrame from the Excel file\n", "file_path = 'upvest_instrument_universe_with_tickers.xlsx'\n", "df = pd.read_excel(file_path)\n", "\n", "# Get the MSCI World tickers from the CSV file\n", "msci_world_file_path = 'msci_world_tickers.xlsx'  # Replace with your actual file path\n", "msci_world_tickers = get_msci_world_tickers(msci_world_file_path)\n", "\n", "# Check for matches\n", "matches_ticker, missing_tickers = check_msci_world_in_df(df, msci_world_tickers)\n", "\n", "print(\"Matched Tickers:\", matches_ticker)\n", "print(\"Missing Tickers:\", missing_tickers)\n", "\n", "# Create a DataFrame to store the results\n", "results = pd.DataFrame({\n", "    'Matched Tickers': pd.Series(matches_ticker),\n", "    'Missing Tickers': pd.Series(missing_tickers)\n", "})\n", "\n", "# Optionally, save the results to a file\n", "results.to_excel('msci_world_check_results.xlsx', index=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "683e8602", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d328636a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}