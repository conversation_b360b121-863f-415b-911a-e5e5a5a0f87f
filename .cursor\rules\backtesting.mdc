---
description: 
globs: 
alwaysApply: true
---
### Implementation Quality:
- **Zero breaking changes** - existing code unaffected
- **Additive enhancement pattern** - new features don't disrupt old
- **Comprehensive test coverage** - 48+ tests covering edge cases
- **Real-world scenario testing** - day trading, averaging, scaling
- **Error-resistant design** - proper validation and exception handling
- **Backtesting datetime accuracy** - uses strategy datetime, not system time

### 🕐 CRITICAL: DATETIME USAGE GUIDELINES

**⚠️ ALWAYS CHALLENGE DATETIME USAGE IN BACKTESTING**

**Problem**: Using system datetime (`datetime.datetime.now()`) breaks backtesting accuracy

**Examples of Wrong Usage:**
```python
# ❌ WRONG - Uses current system time (e.g., 2025-01-06)
current_time = datetime.datetime.now()
analysis_date = datetime.datetime.now()
days_held = (datetime.datetime.now() - lot_date).days
```

**Correct Backtesting Usage:**
```python
# ✅ CORRECT - Uses backtest datetime (e.g., 2006-12-29 from data)
if hasattr(self.strategy, 'datetime'):
    current_dt = self.strategy.datetime.datetime()
elif len(self.strategy.datas) > 0:
    current_dt = self.strategy.datas[0].datetime.datetime()
else:
    current_dt = None  # Handle gracefully
```

**Development Rules:**
- ❌ **Never use** `datetime.datetime.now()` in backtesting components
- ❌ **Never use** `time.time()` or system time functions  
- ❌ **Never assume** "current time" means "system time"
- ✅ **Always use** strategy datetime or data datetime
- ✅ **Challenge every datetime usage** during code review
- ✅ **Test with historical data** to verify datetime accuracy
- ✅ **Document datetime source** in function/method comments

**When System Time is Acceptable:**
- ✅ Live trading mode only
- ✅ Logging and debugging (with clear timestamps)
- ✅ Performance measurement (execution timing)
- ✅ File naming and versioning
- ✅ Cache expiration and cleanup operations

**Impact of Wrong Datetime Usage:**
- **Incorrect calculations**: Days held, holding periods, time-based metrics
- **Broken backtests**: Results not historically accurate or reproducible
- **Tax reporting errors**: Wrong acquisition/sale date calculations
- **Performance metrics**: Inaccurate time-based analytics

**Key Principle**: Backtest results must be reproducible and historically accurate

---

## 🇪🇺 CRITICAL: EUROPEAN MARKETS COMPLIANCE NOTE

**⚠️ IMPORTANT: NO WASH SALES IN EUROPE**

**Wash sale rules are a US-only tax concept and do NOT apply to European markets.**

- **European Union**: No wash sale rules exist
- **United Kingdom**: No wash sale equivalent  
- **Germany**: No wash sale restrictions
- **France**: No wash sale provisions
- **Other European markets**: Generally no wash sale concepts

**This means:**
- ❌ **Do NOT implement** wash sale detection for European strategies
- ❌ **Do NOT add** wash sale loss disallowance calculations  
- ❌ **Do NOT include** wash sale related fields or logic
- ✅ **European strategies** can sell and immediately repurchase without tax penalties
- ✅ **Tax loss harvesting** works differently in European markets
- ✅ **Focus on** capital gains reporting, not wash sale avoidance
**European Tax Lot Reporting - No Holding Period Distinctions**

**Key European Market Characteristics:**
- **No short-term vs long-term capital gains** - All gains treated equally
- **No holding period requirements** - No 1-year distinction for tax rates
- **No wash sale rules** - Can sell and immediately repurchase
- **Focus on capital gains reporting** - Simple gain/loss calculations

**Tax Lot Analyzer Design for Europe:**
- **No holding period tracking** - All lots treated equally
- **Simple FIFO reporting** - Standard European accounting practice
- **Capital gains focus** - Gain/loss calculations without time distinctions
- **Transaction cost basis** - Actual purchase prices for accurate reporting

**European vs US Tax Lot Differences:**
| Feature | European Markets | US Markets |
|---------|------------------|------------|
| Holding Periods | ❌ No distinction | ✅ Short-term vs long-term |
| Wash Sales | ❌ Not applicable | ✅ 30-day rule applies |
| Tax Rates | ✅ Single rate per country | ✅ Different rates by holding period |
| Lot Selection | ✅ FIFO standard | ✅ FIFO, LIFO, specific identification |
| Reporting | ✅ Capital gains focus | ✅ Wash sale + holding period focus |

**Implementation Notes:**
- Tax lot tracking focuses on **cost basis accuracy**
- **No time-based calculations** for tax optimization
- **Simple gain/loss reporting** for European tax authorities

**Key Design Principle**: All European market implementations should be free of any US wash sale logic or considerations.

