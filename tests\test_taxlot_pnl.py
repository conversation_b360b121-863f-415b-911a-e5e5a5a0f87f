#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
###############################################################################
#
# Test suite for tax lot aware PnL calculations
#
###############################################################################
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import sys
import os
import unittest
from datetime import datetime

# Add the parent directory to the path so we can import backtrader
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import backtrader as bt
from backtrader.position import Position
from backtrader.broker import TaxAwareBrokerMixin
from backtrader.brokers.bbroker import BackBroker


class TestTaxLotPnL(unittest.TestCase):
    """Test tax lot aware PnL calculations"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.dt1 = datetime(2023, 1, 1)
        self.dt2 = datetime(2023, 1, 2)
        self.dt3 = datetime(2023, 1, 3)
        
    def test_basic_taxlot_pnl_calculation(self):
        """Test basic tax lot PnL calculation vs average price"""
        position = Position()
        
        # Buy 100 shares at $10
        position.update(100, 10.0, self.dt1)
        
        # Buy 100 more shares at $20
        position.update(100, 20.0, self.dt2)
        
        # Position should now be 200 shares at $15 average
        self.assertEqual(position.size, 200)
        self.assertEqual(position.price, 15.0)
        
        # Sell 100 shares at $25
        # Average price PnL: 100 * (25 - 15) = $1000
        # Tax lot PnL (FIFO): 100 * (25 - 10) = $1500 (sells first lot at $10)
        
        average_pnl = 100 * (25.0 - position.price)
        taxlot_pnl = position.get_taxlot_pnl(100, 25.0)
        
        self.assertEqual(average_pnl, 1000.0)
        self.assertEqual(taxlot_pnl, 1500.0)
        
        print(f"Average price PnL: ${average_pnl}")
        print(f"Tax lot PnL (FIFO): ${taxlot_pnl}")
        print(f"Difference: ${taxlot_pnl - average_pnl}")
        
    def test_partial_lot_sale(self):
        """Test PnL calculation when partially selling a tax lot"""
        position = Position()
        
        # Buy 100 shares at $10
        position.update(100, 10.0, self.dt1)
        
        # Buy 50 shares at $20
        position.update(50, 20.0, self.dt2)
        
        # Sell 120 shares at $15
        # Should sell: 100 @ $10 (full first lot) + 20 @ $20 (partial second lot)
        # PnL = 100 * (15 - 10) + 20 * (15 - 20) = 500 - 100 = $400
        
        taxlot_pnl = position.get_taxlot_pnl(120, 15.0)
        expected_pnl = 100 * (15.0 - 10.0) + 20 * (15.0 - 20.0)
        
        self.assertEqual(taxlot_pnl, expected_pnl)
        self.assertEqual(taxlot_pnl, 400.0)
        
    def test_multiple_lots_different_prices(self):
        """Test PnL with multiple lots at different prices"""
        position = Position()
        
        # Build position with multiple purchases
        position.update(50, 8.0, self.dt1)   # Lot 1: 50 @ $8
        position.update(75, 12.0, self.dt2)  # Lot 2: 75 @ $12
        position.update(25, 16.0, self.dt3)  # Lot 3: 25 @ $16
        
        # Total: 150 shares, average price = (50*8 + 75*12 + 25*16) / 150 = $11.33
        expected_avg = (50*8.0 + 75*12.0 + 25*16.0) / 150
        self.assertAlmostEqual(position.price, expected_avg, places=2)
        
        # Sell 100 shares at $20
        # FIFO: 50 @ $8 + 50 @ $12 = 50*(20-8) + 50*(20-12) = 600 + 400 = $1000
        taxlot_pnl = position.get_taxlot_pnl(100, 20.0)
        expected_pnl = 50 * (20.0 - 8.0) + 50 * (20.0 - 12.0)
        
        self.assertEqual(taxlot_pnl, expected_pnl)
        self.assertEqual(taxlot_pnl, 1000.0)
        
    def test_edge_cases(self):
        """Test edge cases for tax lot PnL calculation"""
        position = Position()
        
        # Test with no position
        pnl = position.get_taxlot_pnl(100, 15.0)
        self.assertEqual(pnl, 0.0)
        
        # Test with zero sale quantity
        position.update(100, 10.0, self.dt1)
        pnl = position.get_taxlot_pnl(0, 15.0)
        self.assertEqual(pnl, 0.0)
        
        # Test selling more than position size
        pnl = position.get_taxlot_pnl(200, 15.0)  # Position only has 100
        expected_pnl = 100 * (15.0 - 10.0)  # Should cap at position size
        self.assertEqual(pnl, expected_pnl)
        
    def test_negative_pnl(self):
        """Test PnL calculation with losses"""
        position = Position()
        
        # Buy at high price, sell at low price
        position.update(100, 20.0, self.dt1)
        position.update(100, 25.0, self.dt2)
        
        # Sell 150 shares at $15 (loss)
        # FIFO: 100 @ $20 + 50 @ $25 = 100*(15-20) + 50*(15-25) = -500 + -500 = -$1000
        taxlot_pnl = position.get_taxlot_pnl(150, 15.0)
        expected_pnl = 100 * (15.0 - 20.0) + 50 * (15.0 - 25.0)
        
        self.assertEqual(taxlot_pnl, expected_pnl)
        self.assertEqual(taxlot_pnl, -1000.0)


class TestTaxAwareBrokerMixin(unittest.TestCase):
    """Test the TaxAwareBrokerMixin functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.dt1 = datetime(2023, 1, 1)
        self.dt2 = datetime(2023, 1, 2)
        
    def test_mixin_taxlot_pnl(self):
        """Test the mixin's get_taxlot_pnl method"""
        class TestBroker(TaxAwareBrokerMixin):
            pass
        
        broker = TestBroker()
        position = Position()
        
        # Build position
        position.update(100, 10.0, self.dt1)
        position.update(100, 20.0, self.dt2)
        
        # Test mixin method
        pnl = broker.get_taxlot_pnl(position, 100, 25.0)
        expected_pnl = 100 * (25.0 - 10.0)  # FIFO: first lot at $10
        
        self.assertEqual(pnl, expected_pnl)
        self.assertEqual(pnl, 1500.0)
        
    def test_mixin_realized_unrealized_pnl(self):
        """Test the mixin's realized/unrealized PnL calculation"""
        class TestBroker(TaxAwareBrokerMixin):
            pass
        
        broker = TestBroker()
        position = Position()
        
        # Build position
        position.update(100, 10.0, self.dt1)
        position.update(100, 20.0, self.dt2)
        
        # Test unrealized PnL at current price of $25
        realized, unrealized = broker.get_realized_unrealized_pnl(position, 25.0)
        
        # Unrealized PnL should be: 100*(25-10) + 100*(25-20) = 1500 + 500 = $2000
        expected_unrealized = 100 * (25.0 - 10.0) + 100 * (25.0 - 20.0)
        
        self.assertEqual(realized, 0.0)  # No realized PnL tracking yet
        self.assertEqual(unrealized, expected_unrealized)
        self.assertEqual(unrealized, 2000.0)
        
    def test_mixin_is_taxlot_enabled(self):
        """Test the mixin's taxlot detection method"""
        class TestBroker(TaxAwareBrokerMixin):
            pass
        
        broker = TestBroker()
        position = Position()
        
        # Position should have tax lot functionality
        self.assertTrue(broker.is_taxlot_enabled(position))
        
        # Create a basic object without tax lot functionality
        class BasicPosition:
            def __init__(self):
                self.size = 0
                self.price = 0.0
        
        basic_pos = BasicPosition()
        self.assertFalse(broker.is_taxlot_enabled(basic_pos))


class TestComparisonScenarios(unittest.TestCase):
    """Test scenarios that highlight the difference between average price and tax lot PnL"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.dt1 = datetime(2023, 1, 1)
        self.dt2 = datetime(2023, 1, 2)
        self.dt3 = datetime(2023, 1, 3)
        
    def test_dollar_cost_averaging_scenario(self):
        """Test a realistic dollar cost averaging scenario"""
        position = Position()
        
        print("\n=== Dollar Cost Averaging Scenario ===")
        
        # Simulate dollar cost averaging into a volatile stock
        purchases = [
            (100, 50.0),  # Buy at peak
            (200, 25.0),  # Buy after 50% drop
            (100, 40.0),  # Buy on recovery
        ]
        
        for i, (qty, price) in enumerate(purchases):
            position.update(qty, price, datetime(2023, 1, i+1))
            print(f"Purchase {i+1}: {qty} shares @ ${price}")
        
        total_shares = sum(qty for qty, _ in purchases)
        weighted_avg = sum(qty * price for qty, price in purchases) / total_shares
        
        print(f"\nTotal position: {total_shares} shares @ ${weighted_avg:.2f} average")
        print(f"Tax lots: {len(position.taxlots)}")
        
        # Sell half position at $45
        sale_qty = total_shares // 2
        sale_price = 45.0
        
        average_pnl = sale_qty * (sale_price - position.price)
        taxlot_pnl = position.get_taxlot_pnl(sale_qty, sale_price)
        
        print(f"\nSelling {sale_qty} shares @ ${sale_price}")
        print(f"Average price PnL: ${average_pnl:.2f}")
        print(f"Tax lot PnL (FIFO): ${taxlot_pnl:.2f}")
        print(f"Difference: ${taxlot_pnl - average_pnl:.2f}")
        
        # Tax lot should be more accurate for tax purposes
        # FIFO: 100 @ $50 + 100 @ $25 = 100*(45-50) + 100*(45-25) = -500 + 2000 = $1500
        expected_taxlot_pnl = 100 * (45.0 - 50.0) + 100 * (45.0 - 25.0)
        self.assertEqual(taxlot_pnl, expected_taxlot_pnl)
        
    def test_tax_optimization_scenario(self):
        """Test scenario useful for tax loss harvesting"""
        position = Position()
        
        print("\n=== Tax Optimization Scenario ===")
        
        # Build position over time
        position.update(100, 100.0, self.dt1)  # Lot 1: Loss position
        position.update(100, 80.0, self.dt2)   # Lot 2: Bigger loss position  
        position.update(100, 120.0, self.dt3)  # Lot 3: Gain position
        
        current_price = 90.0
        
        # Show different PnL for different sale quantities
        for sale_qty in [100, 200, 250]:
            taxlot_pnl = position.get_taxlot_pnl(sale_qty, current_price)
            average_pnl = sale_qty * (current_price - position.price)
            
            print(f"\nSelling {sale_qty} shares @ ${current_price}:")
            print(f"  Tax lot PnL (FIFO): ${taxlot_pnl:.2f}")
            print(f"  Average price PnL: ${average_pnl:.2f}")
            print(f"  Difference: ${taxlot_pnl - average_pnl:.2f}")


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2) 