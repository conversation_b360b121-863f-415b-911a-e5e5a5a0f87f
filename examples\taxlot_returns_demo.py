#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
"""
TaxLotReturns Analyzer Demonstration

This example demonstrates the TaxLotReturns analyzer functionality including:
- Tax lot aware return calculations using FIFO costs instead of average prices
- Comparison with standard returns to show tax efficiency impact
- European market compliance (no US holding period distinctions)
- Comprehensive tax efficiency metrics and analysis
- Integration with other analyzers for complete portfolio analysis

Key Features Demonstrated:
- FIFO lot cost calculations for accurate return analysis
- Tax efficiency impact measurement vs standard return calculations
- European markets compliance with proper FIFO accounting
- Performance analysis with tax lot considerations
- Comprehensive analysis methods and reporting
"""

import datetime
import sys
import os

# Add the parent directory to the path to import backtrader
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import backtrader as bt
from backtrader.analyzers.taxlotreturns import TaxLotReturns


class TaxLotDemoStrategy(bt.Strategy):
    """
    Demonstration strategy for TaxLotReturns analyzer
    
    This strategy creates various trading scenarios to demonstrate
    tax lot tracking and return calculation features:
    - Multiple purchases at different prices (tax lot creation)
    - Partial sales using FIFO accounting 
    - Position scaling and averaging scenarios
    - Tax efficiency through lot-specific analysis
    """
    
    params = (
        ('sma_period', 20),
        ('max_positions', 3),
        ('position_size', 200),
    )
    
    def __init__(self):
        # Technical indicators
        self.sma = bt.indicators.SimpleMovingAverage(self.data.close, period=self.p.sma_period)
        self.rsi = bt.indicators.RSI(self.data.close, period=14)
        
        # Strategy state
        self.position_count = 0
        self.trade_log = []
        
        print(f"Strategy initialized with:")
        print(f"  - SMA period: {self.p.sma_period}")
        print(f"  - Max positions: {self.p.max_positions}")
        print(f"  - Position size: {self.p.position_size}")
    
    def log_trade(self, action, size, price):
        """Log trade details for analysis"""
        current_dt = self.data.datetime.datetime()
        self.trade_log.append({
            'datetime': current_dt,
            'action': action,
            'size': size,
            'price': price,
            'portfolio_value': self.broker.getvalue()
        })
    
    def next(self):
        current_price = self.data.close[0]
        
        # Entry logic: Buy on bullish signals
        if not self.position and self.position_count < self.p.max_positions:
            if (self.data.close[0] > self.sma[0] and 
                self.rsi[0] < 70 and 
                self.data.close[0] > self.data.close[-1]):
                
                self.buy(size=self.p.position_size)
                self.log_trade('BUY', self.p.position_size, current_price)
                self.position_count += 1
                
        # Position management: Scale in on dips
        elif self.position.size > 0 and self.position_count < self.p.max_positions:
            if (self.data.close[0] < self.position.price * 0.95 and  # 5% dip
                self.rsi[0] < 40):  # Oversold
                
                scale_size = self.p.position_size // 2
                self.buy(size=scale_size)
                self.log_trade('SCALE_IN', scale_size, current_price)
                self.position_count += 1
        
        # Exit logic: Partial and full exits
        elif self.position.size > 0:
            # Take partial profits
            if self.data.close[0] > self.position.price * 1.10:  # 10% profit
                exit_size = self.position.size // 3
                self.sell(size=exit_size)
                self.log_trade('PARTIAL_SELL', exit_size, current_price)
                
            # Stop loss
            elif self.data.close[0] < self.position.price * 0.90:  # 10% loss
                self.close()
                self.log_trade('STOP_LOSS', self.position.size, current_price)
                self.position_count = 0
                
            # Full exit on bearish signals
            elif (self.data.close[0] < self.sma[0] and 
                  self.rsi[0] > 75):
                
                self.close()
                self.log_trade('FULL_SELL', self.position.size, current_price)
                self.position_count = 0
    
    def stop(self):
        """Print strategy completion summary"""
        print(f"\nStrategy completed:")
        print(f"  - Total trades executed: {len(self.trade_log)}")
        print(f"  - Final portfolio value: ${self.broker.getvalue():.2f}")
        print(f"  - Total return: {((self.broker.getvalue() / 100000.0) - 1.0) * 100:.2f}%")


def run_taxlot_returns_demo():
    """Run comprehensive TaxLotReturns analyzer demonstration"""
    
    print("=" * 80)
    print("TAXLOT RETURNS ANALYZER - COMPREHENSIVE DEMONSTRATION")
    print("=" * 80)
    
    # Demo 1: Basic TaxLotReturns with comparison
    print("\n=== Demo 1: Tax Lot Returns with Standard Comparison ===")
    
    cerebro1 = bt.Cerebro()
    cerebro1.addstrategy(TaxLotDemoStrategy)
    
    # Add data
    data = bt.feeds.BacktraderCSVData(
        dataname=os.path.join(os.path.dirname(__file__), '..', 'datas', '2006-day-001.txt'),
        fromdate=datetime.datetime(2006, 1, 1),
        todate=datetime.datetime(2006, 12, 31)
    )
    cerebro1.adddata(data)
    cerebro1.broker.setcash(100000.0)
    
    # Add TaxLotReturns analyzer with comparison
    cerebro1.addanalyzer(TaxLotReturns,
                         use_taxlot_returns=True,
                         compare_standard=True,
                         _name='taxlot_returns')
    
    # Add other analyzers for comprehensive analysis
    cerebro1.addanalyzer(bt.analyzers.TimeReturn, _name='standard_returns')
    cerebro1.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro1.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    
    # Run strategy
    print("Running strategy with TaxLotReturns analyzer...")
    results1 = cerebro1.run()
    strategy1 = results1[0]
    
    # Analyze results
    taxlot_analysis = strategy1.analyzers.taxlot_returns.get_analysis()
    standard_analysis = strategy1.analyzers.standard_returns.get_analysis()
    sharpe_analysis = strategy1.analyzers.sharpe.get_analysis()
    
    print(f"\n📊 ANALYSIS RESULTS:")
    print(f"TaxLot Returns Analysis:")
    summary = taxlot_analysis.get('summary', {})
    print(f"  - Analysis type: {summary.get('analyzer_type', 'Unknown')}")
    print(f"  - European compliance: {summary.get('european_compliance', False)}")
    print(f"  - Tax lot returns enabled: {summary.get('use_taxlot_returns', False)}")
    print(f"  - Standard comparison: {summary.get('compare_standard', False)}")
    print(f"  - Total periods analyzed: {summary.get('total_periods', 0)}")
    
    if 'tax_efficiency' in taxlot_analysis:
        tax_eff = taxlot_analysis['tax_efficiency']
        print(f"Tax Efficiency Metrics:")
        print(f"  - Tax lot coverage: {tax_eff.get('taxlot_coverage', 0):.1%}")
        print(f"  - Positions with tax lots: {tax_eff.get('positions_with_taxlots', 0)}")
        print(f"  - Positions without tax lots: {tax_eff.get('positions_without_taxlots', 0)}")
        print(f"  - Average value difference: ${tax_eff.get('avg_value_difference', 0):.2f}")
    
    # Demo 2: Position-only analysis (excluding cash)
    print("\n=== Demo 2: Position-Only Tax Lot Returns ===")
    
    cerebro2 = bt.Cerebro()
    cerebro2.addstrategy(TaxLotDemoStrategy)
    cerebro2.adddata(data)
    cerebro2.broker.setcash(100000.0)
    
    # Add TaxLotReturns analyzer excluding cash
    cerebro2.addanalyzer(TaxLotReturns,
                         use_taxlot_returns=True,
                         include_cash=False,  # Focus only on position performance
                         _name='position_only')
    
    print("Running strategy with position-only tax lot analysis...")
    results2 = cerebro2.run()
    strategy2 = results2[0]
    
    position_analysis = strategy2.analyzers.position_only.get_analysis()
    position_summary = position_analysis.get('summary', {})
    
    print(f"\n📊 POSITION-ONLY ANALYSIS:")
    print(f"  - Include cash: {position_summary.get('include_cash', True)}")
    print(f"  - Fund mode: {position_summary.get('fund_mode', 'Unknown')}")
    print(f"  - Analysis periods: {position_summary.get('total_periods', 0)}")
    
    # Demo 3: Full analysis report
    print("\n=== Demo 3: Comprehensive Analysis Report ===")
    
    print("\nDetailed TaxLotReturns Analysis:")
    strategy1.analyzers.taxlot_returns.print_analysis()
    
    # Demo 4: Integration with other analyzers
    print("\n=== Demo 4: Integration Analysis ===")
    
    print(f"\n📈 PORTFOLIO PERFORMANCE SUMMARY:")
    
    # Standard returns summary
    if standard_analysis:
        print(f"Standard Returns (TimeReturn):")
        returns_count = len(standard_analysis)
        if returns_count > 0:
            print(f"  - Periods tracked: {returns_count}")
            print(f"  - Sample return periods: {list(standard_analysis.keys())[:3]}")
    
    # Sharpe ratio
    if sharpe_analysis and 'sharperatio' in sharpe_analysis:
        sharpe_ratio = sharpe_analysis['sharperatio']
        print(f"Risk-Adjusted Performance:")
        print(f"  - Sharpe Ratio: {sharpe_ratio:.4f}" if sharpe_ratio else "  - Sharpe Ratio: N/A")
    
    # Final summary
    print(f"\n💼 STRATEGY PERFORMANCE:")
    final_value = strategy1.broker.getvalue()
    total_return = ((final_value / 100000.0) - 1.0) * 100
    print(f"  - Initial capital: $100,000.00")
    print(f"  - Final portfolio value: ${final_value:.2f}")
    print(f"  - Total return: {total_return:.2f}%")
    
    print("\n" + "=" * 80)
    print("TAXLOT RETURNS ANALYZER DEMONSTRATION COMPLETE")
    print("=" * 80)
    print("🎉 KEY ACHIEVEMENTS DEMONSTRATED:")
    print("✅ Tax lot aware return calculations using FIFO costs")
    print("✅ Comparison with standard returns for tax efficiency analysis")
    print("✅ European market compliance (no US-specific assumptions)")
    print("✅ Integration with existing Backtrader analyzer ecosystem")
    print("✅ Comprehensive analysis and reporting capabilities")
    print("✅ Performance optimized for production use")
    print("\n💡 NEXT STEPS:")
    print("- Customize parameters for specific tax lot analysis needs")
    print("- Integrate with tax reporting systems for compliance")
    print("- Use tax efficiency metrics for strategy optimization")
    print("- Combine with other analyzers for comprehensive portfolio analysis")
    print("=" * 80)


if __name__ == '__main__':
    run_taxlot_returns_demo() 