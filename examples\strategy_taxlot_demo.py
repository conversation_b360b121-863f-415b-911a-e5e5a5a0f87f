#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
"""
Tax Lot Access Strategy Demo

This example demonstrates how strategies can access tax lot information
using the new get_position_lots() methods added in Phase 2.1.
"""

import datetime
import backtrader as bt


class TaxLotDemoStrategy(bt.Strategy):
    """
    Demo strategy showing tax lot access functionality
    """
    
    params = (
        ('period', 20),  # Moving average period
        ('buy_size', 100),  # Size for buy orders
    )
    
    def __init__(self):
        # Simple moving average for entry signals
        self.sma = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.params.period
        )
        
        self.order = None
        self.trade_count = 0
    
    def next(self):
        # Cancel any pending orders
        if self.order:
            return
        
        # Get current position and tax lots
        position = self.getposition()
        tax_lots = self.get_position_lots()
        
        # Print tax lot information if we have any
        if tax_lots:
            print(f"\n=== Tax Lot Information (Day {len(self.data)}) ===")
            print(f"Position size: {position.size}")
            print(f"Position price: ${position.price:.2f}")
            print(f"Number of tax lots: {len(tax_lots)}")
            
            total_cost_basis = 0
            for i, lot in enumerate(tax_lots):
                cost_basis = lot.qty * lot.price
                total_cost_basis += cost_basis
                print(f"  Lot {i+1}: {lot.qty} shares @ ${lot.price:.2f} "
                      f"(${cost_basis:.2f}) on {lot.datetime.date()}")
            
            avg_cost = total_cost_basis / position.size if position.size else 0
            current_value = position.size * self.data.close[0]
            unrealized_pnl = current_value - total_cost_basis
            
            print(f"Average cost basis: ${avg_cost:.2f}")
            print(f"Current price: ${self.data.close[0]:.2f}")
            print(f"Current value: ${current_value:.2f}")
            print(f"Unrealized P&L: ${unrealized_pnl:.2f}")
        
        # Simple trading logic
        if not position:
            # No position - look for buy signal
            if self.data.close[0] > self.sma[0]:
                print(f"\nBUY SIGNAL: Price ${self.data.close[0]:.2f} > SMA ${self.sma[0]:.2f}")
                self.order = self.buy(size=self.params.buy_size)
                self.trade_count += 1
        
        elif position.size > 0:
            # Long position - look for additional buy or sell signal
            if self.data.close[0] > self.sma[0] and self.trade_count < 3:
                # Add to position
                print(f"\nADD TO POSITION: Price ${self.data.close[0]:.2f} > SMA ${self.sma[0]:.2f}")
                self.order = self.buy(size=self.params.buy_size // 2)
                self.trade_count += 1
            
            elif self.data.close[0] < self.sma[0]:
                # Close position
                print(f"\nSELL SIGNAL: Price ${self.data.close[0]:.2f} < SMA ${self.sma[0]:.2f}")
                self.order = self.close()
    
    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            return
        
        if order.status in [order.Completed]:
            if order.isbuy():
                print(f"BUY EXECUTED: {order.executed.size} shares @ ${order.executed.price:.2f}")
            else:
                print(f"SELL EXECUTED: {order.executed.size} shares @ ${order.executed.price:.2f}")
        
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            print(f"Order {order.status}")
        
        self.order = None
    
    def notify_trade(self, trade):
        if trade.isclosed:
            print(f"TRADE CLOSED: P&L ${trade.pnl:.2f}")


def run_demo():
    """Run the tax lot demo strategy"""
    cerebro = bt.Cerebro()
    
    # Add data
    data = bt.feeds.BacktraderCSVData(
        dataname='datas/2006-day-001.txt',
        fromdate=datetime.datetime(2006, 1, 1),
        todate=datetime.datetime(2006, 3, 31)  # First quarter only
    )
    cerebro.adddata(data)
    
    # Add strategy
    cerebro.addstrategy(TaxLotDemoStrategy)
    
    # Set up broker
    cerebro.broker.setcash(10000.0)
    cerebro.broker.setcommission(commission=0.001)  # 0.1%
    
    print("Starting Portfolio Value: $%.2f" % cerebro.broker.getvalue())
    
    # Run strategy
    cerebro.run()
    
    print("\nFinal Portfolio Value: $%.2f" % cerebro.broker.getvalue())


if __name__ == '__main__':
    run_demo() 