#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
###############################################################################
#
# Tax Lot Analyzer Demo - Phase 2.2.2 Implementation
# European Market Focus - Basic Tax Lot Reporting
#
# This demo shows how to use the TaxLotAnalyzer to get basic tax lot 
# information without US-specific holding period logic.
#
###############################################################################
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import backtrader as bt
import backtrader.feeds as btfeeds
import datetime


class TaxLotAnalyzerStrategy(bt.Strategy):
    '''
    Demo strategy showing tax lot analyzer usage.
    
    This strategy demonstrates:
    1. Basic tax lot tracking
    2. European-friendly lot reporting
    3. Using the TaxLotAnalyzer for reporting
    4. Using the get_basic_lot_info() helper method
    '''
    
    params = (
        ('period', 20),        # SMA period
        ('trade_size', 100),   # Default trade size
    )
    
    def __init__(self):
        # Create a simple SMA indicator for trading signals
        self.sma = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.p.period
        )
        
        # Track our trading activity
        self.trade_count = 0
        self.lot_reports = []
        
        print("=== Tax Lot Analyzer Demo - European Market Focus ===")
        print(f"Strategy initialized with SMA period: {self.p.period}")
        print(f"Trade size: {self.p.trade_size}")
        print()
    
    def next(self):
        '''Main strategy logic with tax lot reporting'''
        
        # Simple trading logic: buy when price > SMA, sell when price < SMA
        if not self.position:
            if self.data.close[0] > self.sma[0]:
                self.buy(size=self.p.trade_size)
                self.trade_count += 1
                print(f"Day {len(self)}: BUY {self.p.trade_size} shares at {self.data.close[0]:.2f}")
        
        elif self.position.size > 0:
            if self.data.close[0] < self.sma[0]:
                # Before selling, capture tax lot information
                lot_info = self.get_basic_lot_info()
                self.lot_reports.append({
                    'day': len(self),
                    'action': 'SELL',
                    'price': self.data.close[0],
                    'lot_info': lot_info
                })
                
                self.sell(size=self.position.size)
                self.trade_count += 1
                print(f"Day {len(self)}: SELL {self.position.size} shares at {self.data.close[0]:.2f}")
                
                # Print lot information
                self._print_lot_info(lot_info)
        
        # Periodic lot reporting (every 50 days)
        if len(self) % 50 == 0 and self.position:
            print(f"\n--- Periodic Lot Report (Day {len(self)}) ---")
            lot_info = self.get_basic_lot_info()
            self._print_lot_info(lot_info)
    
    def _print_lot_info(self, lot_info):
        '''Helper method to print tax lot information'''
        print(f"  Position Summary:")
        print(f"    Total Quantity: {lot_info['total_quantity']}")
        print(f"    Average Cost Basis: {lot_info['avg_cost_basis']:.2f}")
        print(f"    Total Value: {lot_info['total_value']:.2f}")
        print(f"    Number of Tax Lots: {lot_info['lot_count']}")
        
        if lot_info['lots']:
            print(f"  Individual Tax Lots:")
            for i, lot in enumerate(lot_info['lots'], 1):
                print(f"    Lot {i}: {lot['quantity']} shares @ {lot['price']:.2f} "
                      f"(held {lot['days_held']} days, value: {lot['value']:.2f})")
        print()
    
    def stop(self):
        '''Print final summary'''
        print("=== Final Strategy Summary ===")
        print(f"Total trades executed: {self.trade_count}")
        print(f"Final position size: {self.position.size}")
        
        if self.position:
            final_lot_info = self.get_basic_lot_info()
            print(f"Final tax lot summary:")
            self._print_lot_info(final_lot_info)
        
        print(f"Total lot reports captured: {len(self.lot_reports)}")


def run_demo():
    '''Run the tax lot analyzer demo'''
    
    cerebro = bt.Cerebro()
    
    # Add sample data - using 2006 data from the samples
    data = bt.feeds.GenericCSVData(
        dataname='datas/2006-day-001.txt',
        dtformat=('%Y-%m-%d'),
        datetime=0,
        open=1,
        high=2,
        low=3,
        close=4,
        volume=5,
        openinterest=-1
    )
    
    cerebro.adddata(data)
    
    # Add our strategy
    cerebro.addstrategy(TaxLotAnalyzerStrategy)
    
    # Add the TaxLotAnalyzer with European-friendly settings
    cerebro.addanalyzer(
        bt.analyzers.TaxLotAnalyzer,
        headers=True,           # Include headers in results
        lot_details=True,       # Include detailed lot information
        summary_only=False,     # Include both summary and details
        include_zero_positions=False,  # Skip zero positions
        _name='taxlot_analysis'
    )
    
    # Set initial cash
    cerebro.broker.setcash(10000.0)
    
    print("Starting portfolio value: {:.2f}".format(cerebro.broker.getvalue()))
    
    # Run the backtest
    results = cerebro.run()
    
    print("Final portfolio value: {:.2f}".format(cerebro.broker.getvalue()))
    
    # Extract and display analyzer results
    strat = results[0]
    analysis = strat.analyzers.taxlot_analysis.get_analysis()
    
    print("\n=== TaxLotAnalyzer Results ===")
    
    # Print summary
    if 'summary' in analysis:
        summary = analysis['summary']
        print("Summary Statistics:")
        print(f"  Total positions: {summary.get('total_positions', 0)}")
        print(f"  Total tax lots: {summary.get('total_lots', 0)}")
        print(f"  Total value: {summary.get('total_value', 0):.2f}")
        print(f"  Long positions: {summary.get('long_positions', 0)}")
        print(f"  Short positions: {summary.get('short_positions', 0)}")
        print(f"  Average lots per position: {summary.get('avg_lots_per_position', 0):.1f}")
        print(f"  Analysis datetime: {summary.get('analysis_datetime', 'N/A')}")
    
    # Print position details
    if 'positions' in analysis:
        positions = analysis['positions']
        print(f"\nPosition Details:")
        for symbol, pos_data in positions.items():
            print(f"  {symbol}:")
            print(f"    Total quantity: {pos_data['total_qty']}")
            print(f"    Number of lots: {pos_data['lot_count']}")
            print(f"    Average cost basis: {pos_data['avg_cost_basis']:.2f}")
            print(f"    Total value: {pos_data['total_value']:.2f}")
            
            if 'lots' in pos_data and pos_data['lots']:
                print(f"    Individual lots:")
                for lot in pos_data['lots']:
                    print(f"      {lot['lot_qty']} shares @ {lot['lot_price']:.2f} "
                          f"(held {lot['days_held']} days)")
    
    # Demonstrate analyzer convenience methods
    analyzer = strat.analyzers.taxlot_analysis
    
    print(f"\n=== Analyzer Convenience Methods ===")
    print(f"Total lot count: {analyzer.get_total_lot_count()}")
    print(f"Total position value: {analyzer.get_total_position_value():.2f}")
    
    lots_summary = analyzer.get_lots_summary()
    if lots_summary:
        print(f"Lots summary keys: {list(lots_summary.keys())}")
    
    print("\n=== Demo Complete ===")
    print("This demo showed:")
    print("1. ✅ Basic tax lot tracking during trading")
    print("2. ✅ European-friendly reporting (no holding period categories)")
    print("3. ✅ TaxLotAnalyzer integration with strategy")
    print("4. ✅ Strategy helper method usage (get_basic_lot_info)")
    print("5. ✅ Comprehensive lot reporting and analysis")
    
    return results


if __name__ == '__main__':
    try:
        results = run_demo()
        print("\n✅ Tax Lot Analyzer Demo completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc() 