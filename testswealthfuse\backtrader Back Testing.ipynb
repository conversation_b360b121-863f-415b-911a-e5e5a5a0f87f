{"cells": [{"cell_type": "code", "execution_count": 1, "id": "45b847cb-57d6-473e-83a5-451aa59b6b45", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting Portfolio Value: 99990000.00\n", "Final Portfolio Value: 99990000.00\n"]}], "source": ["from __future__ import (absolute_import, division, print_function,\n", "                        unicode_literals)\n", "\n", "import backtrader as bt\n", "\n", "if __name__ == '__main__':\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()\n", "    cerebro.broker.setcash(99990000.0)\n", "    print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())\n", "\n", "    cerebro.run()\n", "\n", "    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())"]}, {"cell_type": "code", "execution_count": 2, "id": "b4277aae-1457-4c78-9a21-f7beea8fd72c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting Portfolio Value: 100000.00\n", "2000-01-24, Close, 24.10\n", "2000-01-25, Close, 25.10\n", "2000-01-26, Close, 24.49\n", "2000-01-27, Close, 23.04\n", "2000-01-27, BUY CREATE, 23.04\n", "2000-01-28, BUY EXECUTED, Price: 22.90, Cost: 229.00, Comm 0.23\n", "2000-01-28, Close, 21.07\n", "2000-01-31, Close, 22.22\n", "2000-02-01, Close, 24.02\n", "2000-02-02, Close, 24.16\n", "2000-02-03, Close, 25.21\n", "2000-02-04, Close, 25.71\n", "2000-02-04, SELL CREATE, 25.71\n", "2000-02-07, SELL EXECUTED, Price: 26.38, Cost: 229.00, Comm 0.26\n", "2000-02-07, OPERATION PROFIT, GROSS 34.80, NET 34.31\n", "2000-02-07, Close, 26.66\n", "2000-02-08, Close, 26.49\n", "2000-02-09, Close, 26.66\n", "2000-02-10, Close, 27.71\n", "2000-02-11, Close, 26.55\n", "2000-02-14, Close, 27.66\n", "2000-02-15, Close, 27.30\n", "2000-02-16, Close, 27.24\n", "2000-02-16, BUY CREATE, 27.24\n", "2000-02-17, BUY EXECUTED, Price: 27.46, Cost: 274.60, Comm 0.27\n", "2000-02-17, Close, 27.41\n", "2000-02-18, Close, 26.05\n", "2000-02-22, Close, 26.38\n", "2000-02-23, Close, 28.05\n", "2000-02-24, Close, 27.55\n", "2000-02-25, Close, 31.41\n", "2000-02-25, SELL CREATE, 31.41\n", "2000-02-28, SELL EXECUTED, Price: 31.69, Cost: 274.60, Comm 0.32\n", "2000-02-28, O<PERSON>ERAT<PERSON> PROFIT, GROSS 42.30, NET 41.71\n", "2000-02-28, Close, 30.52\n", "2000-02-29, Close, 33.02\n", "2000-03-01, Close, 31.80\n", "2000-03-02, Close, 30.47\n", "2000-03-02, BUY CREATE, 30.47\n", "2000-03-03, BUY EXECUTED, Price: 31.63, Cost: 316.30, Comm 0.32\n", "2000-03-03, Close, 33.36\n", "2000-03-06, Close, 33.69\n", "2000-03-07, Close, 33.33\n", "2000-03-08, Close, 36.97\n", "2000-03-09, Close, 37.36\n", "2000-03-10, Close, 36.30\n", "2000-03-10, SELL CREATE, 36.30\n", "2000-03-13, SELL EXECUTED, Price: 34.91, Cost: 316.30, Comm 0.35\n", "2000-03-13, O<PERSON>ERATION PROFIT, GROSS 32.80, NET 32.13\n", "2000-03-13, Close, 35.02\n", "2000-03-13, BUY CREATE, 35.02\n", "2000-03-14, BUY EXECUTED, Price: 36.41, Cost: 364.10, Comm 0.36\n", "2000-03-14, Close, 34.25\n", "2000-03-15, Close, 34.97\n", "2000-03-16, Close, 36.44\n", "2000-03-17, Close, 35.50\n", "2000-03-20, Close, 34.75\n", "2000-03-21, Close, 35.89\n", "2000-03-21, SELL CREATE, 35.89\n", "2000-03-22, SELL EXECUTED, Price: 36.02, Cost: 364.10, Comm 0.36\n", "2000-03-22, OPERATION PROFIT, GROSS -3.90, NET -4.62\n", "2000-03-22, Close, 37.39\n", "2000-03-23, Close, 38.64\n", "2000-03-24, Close, 38.69\n", "2000-03-27, Close, 39.33\n", "2000-03-28, Close, 38.50\n", "2000-03-29, Close, 36.69\n", "2000-03-29, BUY CREATE, 36.69\n", "2000-03-30, BUY EXECUTED, Price: 34.91, Cost: 349.10, Comm 0.35\n", "2000-03-30, Close, 34.88\n", "2000-03-31, Close, 34.72\n", "2000-04-03, Close, 34.19\n", "2000-04-04, Close, 33.77\n", "2000-04-05, Close, 34.80\n", "2000-04-06, Close, 36.55\n", "2000-04-06, SELL CREATE, 36.55\n", "2000-04-07, SELL EXECUTED, Price: 37.22, Cost: 349.10, Comm 0.37\n", "2000-04-07, OPERATION PROFIT, GROSS 23.10, NET 22.38\n", "2000-04-07, Close, 38.75\n", "2000-04-10, Close, 36.69\n", "2000-04-11, Close, 34.41\n", "2000-04-11, BUY CREATE, 34.41\n", "2000-04-12, BUY EXECUTED, Price: 34.66, Cost: 346.60, Comm 0.35\n", "2000-04-12, Close, 32.52\n", "2000-04-13, Close, 31.99\n", "2000-04-14, Close, 27.80\n", "2000-04-17, Close, 33.27\n", "2000-04-18, Close, 35.11\n", "2000-04-19, Close, 33.16\n", "2000-04-19, SELL CREATE, 33.16\n", "2000-04-20, SELL EXECUTED, Price: 32.83, Cost: 346.60, Comm 0.33\n", "2000-04-20, OPERATION PROFIT, GROSS -18.30, NET -18.97\n", "2000-04-20, Close, 31.49\n", "2000-04-20, BUY CREATE, 31.49\n", "2000-04-24, BUY EXECUTED, Price: 29.96, Cost: 299.60, Comm 0.30\n", "2000-04-24, Close, 32.22\n", "2000-04-25, Close, 33.61\n", "2000-04-26, Close, 32.11\n", "2000-04-27, Close, 34.38\n", "2000-04-28, Close, 35.55\n", "2000-05-01, Close, 35.44\n", "2000-05-01, SELL CREATE, 35.44\n", "2000-05-02, SELL EXECUTED, Price: 35.11, Cost: 299.60, Comm 0.35\n", "2000-05-02, OPERATION PROFIT, GROSS 51.50, NET 50.85\n", "2000-05-02, Close, 34.61\n", "2000-05-02, BUY CREATE, 34.61\n", "2000-05-03, BUY EXECUTED, Price: 34.19, Cost: 341.90, Comm 0.34\n", "2000-05-03, Close, 33.72\n", "2000-05-04, Close, 33.02\n", "2000-05-05, Close, 34.16\n", "2000-05-08, Close, 32.16\n", "2000-05-09, Close, 32.02\n", "2000-05-10, Close, 30.08\n", "2000-05-10, SELL CREATE, 30.08\n", "2000-05-11, SELL EXECUTED, Price: 30.66, Cost: 341.90, Comm 0.31\n", "2000-05-11, OPERATION PROFIT, GROSS -35.30, NET -35.95\n", "2000-05-11, Close, 32.19\n", "2000-05-12, Close, 32.99\n", "2000-05-15, Close, 34.25\n", "2000-05-16, Close, 35.22\n", "2000-05-17, Close, 34.77\n", "2000-05-18, Close, 32.49\n", "2000-05-18, BUY CREATE, 32.49\n", "2000-05-19, BUY EXECUTED, Price: 32.02, Cost: 320.20, Comm 0.32\n", "2000-05-19, Close, 31.16\n", "2000-05-22, Close, 30.16\n", "2000-05-23, Close, 27.85\n", "2000-05-24, Close, 28.57\n", "2000-05-25, Close, 29.55\n", "2000-05-26, Close, 29.80\n", "2000-05-26, SELL CREATE, 29.80\n", "2000-05-30, SELL EXECUTED, Price: 30.63, Cost: 320.20, Comm 0.31\n", "2000-05-30, OPERATION PROFIT, GROSS -13.90, NET -14.53\n", "2000-05-30, Close, 32.99\n", "2000-05-31, Close, 31.97\n", "2000-06-01, Close, 34.63\n", "2000-06-02, Close, 35.66\n", "2000-06-05, Close, 36.00\n", "2000-06-06, Close, 34.27\n", "2000-06-07, Close, 35.58\n", "2000-06-08, Close, 36.64\n", "2000-06-09, Close, 36.77\n", "2000-06-12, Close, 35.83\n", "2000-06-13, Close, 36.33\n", "2000-06-14, Close, 35.13\n", "2000-06-15, Close, 36.69\n", "2000-06-16, Close, 36.41\n", "2000-06-19, Close, 38.25\n", "2000-06-20, Close, 38.27\n", "2000-06-21, Close, 38.33\n", "2000-06-22, Close, 36.25\n", "2000-06-23, Close, 35.36\n", "2000-06-23, BUY CREATE, 35.36\n", "2000-06-26, BUY EXECUTED, Price: 35.69, Cost: 356.90, Comm 0.36\n", "2000-06-26, Close, 36.77\n", "2000-06-27, Close, 36.58\n", "2000-06-28, Close, 36.89\n", "2000-06-29, Close, 35.97\n", "2000-06-30, Close, 37.39\n", "2000-07-03, Close, 35.66\n", "2000-07-03, SELL CREATE, 35.66\n", "2000-07-05, SELL EXECUTED, Price: 34.16, Cost: 356.90, Comm 0.34\n", "2000-07-05, OPERATION PROFIT, GROSS -15.30, NET -16.00\n", "2000-07-05, Close, 32.16\n", "2000-07-05, BUY CREATE, 32.16\n", "2000-07-06, BUY EXECUTED, Price: 31.91, Cost: 319.10, Comm 0.32\n", "2000-07-06, Close, 33.63\n", "2000-07-07, Close, 33.75\n", "2000-07-10, Close, 32.97\n", "2000-07-11, Close, 32.16\n", "2000-07-12, Close, 33.22\n", "2000-07-13, Close, 33.69\n", "2000-07-13, SELL CREATE, 33.69\n", "2000-07-14, SELL EXECUTED, Price: 33.88, Cost: 319.10, Comm 0.34\n", "2000-07-14, O<PERSON>ERATION PROFIT, GROSS 19.70, NET 19.04\n", "2000-07-14, Close, 33.86\n", "2000-07-17, Close, 33.86\n", "2000-07-18, Close, 32.99\n", "2000-07-19, Close, 32.80\n", "2000-07-19, BUY CREATE, 32.80\n", "2000-07-20, BUY EXECUTED, Price: 33.27, Cost: 332.70, Comm 0.33\n", "2000-07-20, Close, 34.75\n", "2000-07-21, Close, 33.55\n", "2000-07-24, Close, 33.36\n", "2000-07-25, Close, 33.80\n", "2000-07-26, Close, 34.13\n", "2000-07-27, Close, 33.38\n", "2000-07-27, SELL CREATE, 33.38\n", "2000-07-28, SELL EXECUTED, Price: 33.41, Cost: 332.70, Comm 0.33\n", "2000-07-28, OP<PERSON>ATION PROFIT, GROSS 1.40, NET 0.73\n", "2000-07-28, Close, 32.19\n", "2000-07-28, BUY CREATE, 32.19\n", "2000-07-31, BUY EXECUTED, Price: 31.91, Cost: 319.10, Comm 0.32\n", "2000-07-31, Close, 33.44\n", "2000-08-01, Close, 32.52\n", "2000-08-02, Close, 32.52\n", "2000-08-03, Close, 34.44\n", "2000-08-04, Close, 36.27\n", "2000-08-07, Close, 36.41\n", "2000-08-07, SELL CREATE, 36.41\n", "2000-08-08, SELL EXECUTED, Price: 36.02, Cost: 319.10, Comm 0.36\n", "2000-08-08, OPERATION PROFIT, GROSS 41.10, NET 40.42\n", "2000-08-08, Close, 36.91\n", "2000-08-09, Close, 36.19\n", "2000-08-10, Close, 35.61\n", "2000-08-10, BUY CREATE, 35.61\n", "2000-08-11, BUY EXECUTED, Price: 35.55, Cost: 355.50, Comm 0.36\n", "2000-08-11, Close, 36.08\n", "2000-08-14, Close, 36.64\n", "2000-08-15, Close, 36.14\n", "2000-08-16, Close, 36.11\n", "2000-08-17, Close, 37.33\n", "2000-08-18, Close, 36.16\n", "2000-08-18, SELL CREATE, 36.16\n", "2000-08-21, SELL EXECUTED, Price: 36.52, Cost: 355.50, Comm 0.37\n", "2000-08-21, OPERATION PROFIT, GROSS 9.70, NET 8.98\n", "2000-08-21, Close, 37.00\n", "2000-08-22, Close, 37.16\n", "2000-08-23, Close, 36.86\n", "2000-08-24, Close, 37.66\n", "2000-08-25, Close, 37.64\n", "2000-08-28, Close, 38.58\n", "2000-08-29, Close, 39.03\n", "2000-08-30, Close, 39.25\n", "2000-08-31, Close, 40.44\n", "2000-09-01, Close, 41.19\n", "2000-09-05, Close, 40.50\n", "2000-09-06, Close, 39.69\n", "2000-09-06, BUY CREATE, 39.69\n", "2000-09-07, BUY EXECUTED, Price: 40.08, Cost: 400.80, Comm 0.40\n", "2000-09-07, Close, 40.56\n", "2000-09-08, Close, 38.50\n", "2000-09-11, Close, 37.11\n", "2000-09-12, Close, 35.30\n", "2000-09-13, Close, 36.39\n", "2000-09-14, Close, 37.78\n", "2000-09-14, SELL CREATE, 37.78\n", "2000-09-15, SELL EXECUTED, Price: 36.08, Cost: 400.80, Comm 0.36\n", "2000-09-15, OPERATION PROFIT, GROSS -40.00, NET -40.76\n", "2000-09-15, Close, 34.83\n", "2000-09-18, Close, 34.01\n", "2000-09-18, BUY CREATE, 34.01\n", "2000-09-19, BUY EXECUTED, Price: 34.44, Cost: 344.40, Comm 0.34\n", "2000-09-19, Close, 35.27\n", "2000-09-20, Close, 35.55\n", "2000-09-21, Close, 35.11\n", "2000-09-22, Close, 35.91\n", "2000-09-25, Close, 35.02\n", "2000-09-26, Close, 35.33\n", "2000-09-26, SELL CREATE, 35.33\n", "2000-09-27, SELL EXECUTED, Price: 35.66, Cost: 344.40, Comm 0.36\n", "2000-09-27, OPERATION PROFIT, GROSS 12.20, NET 11.50\n", "2000-09-27, Close, 35.52\n", "2000-09-28, Close, 36.24\n", "2000-09-29, Close, 35.02\n", "2000-10-02, Close, 35.02\n", "2000-10-03, Close, 30.91\n", "2000-10-04, Close, 30.30\n", "2000-10-04, BUY CREATE, 30.30\n", "2000-10-05, BUY EXECUTED, Price: 30.38, Cost: 303.80, Comm 0.30\n", "2000-10-05, Close, 30.38\n", "2000-10-06, Close, 30.08\n", "2000-10-09, Close, 29.69\n", "2000-10-10, Close, 28.74\n", "2000-10-11, Close, 27.69\n", "2000-10-12, Close, 28.02\n", "2000-10-12, SELL CREATE, 28.02\n", "2000-10-13, SELL EXECUTED, Price: 27.57, Cost: 303.80, Comm 0.28\n", "2000-10-13, OPERATION PROFIT, GROSS -28.10, NET -28.68\n", "2000-10-13, Close, 31.69\n", "2000-10-16, Close, 30.74\n", "2000-10-17, Close, 29.96\n", "2000-10-17, BUY CREATE, 29.96\n", "2000-10-18, BUY EXECUTED, Price: 28.07, Cost: 280.70, Comm 0.28\n", "2000-10-18, Close, 29.85\n", "2000-10-19, Close, 32.36\n", "2000-10-20, Close, 31.35\n", "2000-10-23, Close, 30.30\n", "2000-10-24, Close, 31.85\n", "2000-10-25, Close, 30.58\n", "2000-10-25, SELL CREATE, 30.58\n", "2000-10-26, SELL EXECUTED, Price: 30.91, Cost: 280.70, Comm 0.31\n", "2000-10-26, OPERATION PROFIT, GROSS 28.40, NET 27.81\n", "2000-10-26, Close, 30.30\n", "2000-10-26, BUY CREATE, 30.30\n", "2000-10-27, BUY EXECUTED, Price: 30.69, Cost: 306.90, Comm 0.31\n", "2000-10-27, Close, 30.41\n", "2000-10-30, Close, 28.13\n", "2000-10-31, Close, 29.35\n", "2000-11-01, Close, 27.91\n", "2000-11-02, Close, 26.30\n", "2000-11-03, Close, 26.96\n", "2000-11-03, SELL CREATE, 26.96\n", "2000-11-06, SELL EXECUTED, Price: 27.30, Cost: 306.90, Comm 0.27\n", "2000-11-06, OPERATION PROFIT, GROSS -33.90, NET -34.48\n", "2000-11-06, Close, 24.85\n", "2000-11-07, Close, 23.63\n", "2000-11-07, BUY CREATE, 23.63\n", "2000-11-08, BUY EXECUTED, Price: 24.35, Cost: 243.50, Comm 0.24\n", "2000-11-08, Close, 22.07\n", "2000-11-09, Close, 24.18\n", "2000-11-10, Close, 22.63\n", "2000-11-13, Close, 22.01\n", "2000-11-14, Close, 25.24\n", "2000-11-15, Close, 25.68\n", "2000-11-15, SELL CREATE, 25.68\n", "2000-11-16, SELL EXECUTED, Price: 25.57, Cost: 243.50, Comm 0.26\n", "2000-11-16, OPERATION PROFIT, GROSS 12.20, NET 11.70\n", "2000-11-16, Close, 24.35\n", "2000-11-17, Close, 25.63\n", "2000-11-20, Close, 22.01\n", "2000-11-21, Close, 21.24\n", "2000-11-21, BUY CREATE, 21.24\n", "2000-11-22, BUY EXECUTED, Price: 21.01, Cost: 210.10, Comm 0.21\n", "2000-11-22, Close, 19.85\n", "2000-11-24, Close, 21.46\n", "2000-11-27, Close, 20.57\n", "2000-11-28, Close, 20.15\n", "2000-11-29, Close, 20.35\n", "2000-11-30, Close, 23.57\n", "2000-11-30, SELL CREATE, 23.57\n", "2000-12-01, SELL EXECUTED, Price: 23.46, Cost: 210.10, Comm 0.23\n", "2000-12-01, OPERATION PROFIT, GROSS 24.50, NET 24.06\n", "2000-12-01, Close, 23.52\n", "2000-12-04, Close, 25.07\n", "2000-12-05, Close, 28.02\n", "2000-12-06, Close, 26.85\n", "2000-12-07, Close, 25.18\n", "2000-12-07, BUY CREATE, 25.18\n", "2000-12-08, BUY EXECUTED, Price: 26.74, Cost: 267.40, Comm 0.27\n", "2000-12-08, Close, 26.74\n", "2000-12-11, Close, 28.41\n", "2000-12-12, Close, 27.35\n", "2000-12-13, Close, 25.24\n", "2000-12-14, Close, 24.46\n", "2000-12-15, Close, 25.41\n", "2000-12-15, SELL CREATE, 25.41\n", "2000-12-18, SELL EXECUTED, Price: 26.68, Cost: 267.40, Comm 0.27\n", "2000-12-18, OPERATION PROFIT, GROSS -0.60, NET -1.13\n", "2000-12-18, Close, 28.46\n", "2000-12-19, Close, 27.24\n", "2000-12-20, Close, 25.35\n", "2000-12-20, BUY CREATE, 25.35\n", "2000-12-21, BUY EXECUTED, Price: 24.74, Cost: 247.40, Comm 0.25\n", "2000-12-21, Close, 26.24\n", "2000-12-22, Close, 28.35\n", "2000-12-26, Close, 27.52\n", "2000-12-27, Close, 27.30\n", "2000-12-28, Close, 27.63\n", "2000-12-29, Close, 25.85\n", "2000-12-29, SELL CREATE, 25.85\n", "Final Portfolio Value: 100141.34\n"]}], "source": ["from __future__ import (absolute_import, division, print_function,\n", "                        unicode_literals)\n", "\n", "import datetime  # For datetime objects\n", "import os.path  # To manage paths\n", "import sys  # To find out the script name (in argv[0])\n", "\n", "# Import the backtrader platform\n", "import backtrader as bt\n", "\n", "# Create a Stratey\n", "class TestStrategy(bt.Strategy):\n", "    params = (\n", "        ('maperi<PERSON>', 15),\n", "    )\n", "    def log(self, txt, dt=None):\n", "        ''' Logging function fot this strategy'''\n", "        dt = dt or self.datas[0].datetime.date(0)\n", "        print('%s, %s' % (dt.isoformat(), txt))\n", "\n", "    def __init__(self):\n", "        # Keep a reference to the \"close\" line in the data[0] dataseries\n", "        self.dataclose = self.datas[0].close\n", "\n", "        # To keep track of pending orders and buy price/commission\n", "        self.order = None\n", "        self.buyprice = None\n", "        self.buycomm = None\n", "                                                                                                                                                                                                                                                                                                \n", "        # Add a MovingAverageSimple indicator\n", "        self.sma = bt.indicators.SimpleMovingAverage(\n", "            self.datas[0], period=self.params.maperiod)\n", "\n", "    def notify_order(self, order):\n", "        if order.status in [order.Submitted, order.Accepted]:\n", "            # Buy/Sell order submitted/accepted to/by broker - Nothing to do\n", "            return\n", "\n", "        # Check if an order has been completed\n", "        # Attention: broker could reject order if not enough cash\n", "        if order.status in [order.Completed]:\n", "            if order.isbuy():\n", "                self.log(\n", "                    'BUY EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %\n", "                    (order.executed.price,\n", "                     order.executed.value,\n", "                     order.executed.comm))\n", "\n", "                self.buyprice = order.executed.price\n", "                self.buycomm = order.executed.comm\n", "            else:  # Sell\n", "                self.log('SELL EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %\n", "                         (order.executed.price,\n", "                          order.executed.value,\n", "                          order.executed.comm))\n", "\n", "            self.bar_executed = len(self)\n", "\n", "        elif order.status in [order.Canceled, order.Margin, order.Rejected]:\n", "            self.log('Order Canceled/Margin/Rejected')\n", "\n", "        self.order = None\n", "        \n", "    def notify_trade(self, trade):\n", "        if not trade.isclosed:\n", "            return\n", "\n", "        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' %\n", "                 (trade.pnl, trade.pnlcomm))\n", "\n", "    def next(self):\n", "        # Simply log the closing price of the series from the reference\n", "        self.log('Close, %.2f' % self.dataclose[0])\n", "\n", "        # Check if an order is pending ... if yes, we cannot send a 2nd one\n", "        if self.order:\n", "            return\n", "\n", "        # Check if we are in the market\n", "        if not self.position:\n", "\n", "            # Not yet ... we MIGHT BUY if ...\n", "            if self.dataclose[0] < self.dataclose[-1]:\n", "                    # current close less than previous close\n", "\n", "                    if self.dataclose[-1] < self.dataclose[-2]:\n", "                        # previous close less than the previous close\n", "\n", "                        # BUY, BUY, BUY!!! (with default parameters)\n", "                        self.log('BUY CREATE, %.2f' % self.dataclose[0])\n", "\n", "                        # Keep track of the created order to avoid a 2nd order\n", "                        self.order = self.buy()\n", "\n", "        else:\n", "\n", "            # Already in the market ... we might sell\n", "            if len(self) >= (self.bar_executed + 5):\n", "                # SELL, SELL, SELL!!! (with all possible default parameters)\n", "                self.log('SELL CREATE, %.2f' % self.dataclose[0])\n", "\n", "                # Keep track of the created order to avoid a 2nd order\n", "                self.order = self.sell()\n", "\n", "\n", "if __name__ == '__main__':\n", "    # Create a cerebro entity\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()\n", "\n", "    # Add a strategy\n", "    cerebro.addstrategy(TestStrategy)\n", "\n", "    # Datas are in a subfolder of the samples. Need to find where the script is\n", "    # because it could have been called from anywhere\n", "    try:\n", "        # Try to get the directory of the script\n", "        script_directory = os.path.dirname(os.path.abspath(__file__))\n", "    except NameError:\n", "        # Fallback to the current working directory if __file__ is not defined\n", "        script_directory = os.getcwd()\n", "    datapath = os.path.join(script_directory, 'backtrader\\\\datas\\\\orcl-1995-2014.txt')\n", "    # Create a Data Feed\n", "    data = bt.feeds.YahooFinanceCSVData(\n", "        dataname=datapath,\n", "        # Do not pass values before this date\n", "        fromdate=datetime.datetime(2000, 1, 1),\n", "        # Do not pass values after this date\n", "        todate=datetime.datetime(2000, 12, 31),\n", "        reverse=False)\n", "\n", "    # Add the Data Feed to Cerebro\n", "    cerebro.adddata(data)\n", "\n", "    # Set our desired cash start\n", "    cerebro.broker.setcash(100000.0)\n", "    \n", "    # Add a FixedSize sizer according to the stake\n", "    cerebro.addsizer(bt.sizers.FixedSize, stake=10\n", "                    )\n", "    # Set the commission - 0.1% ... divide by 100 to remove the %\n", "    cerebro.broker.setcommission(commission=0.001)\n", "\n", "    # Print out the starting conditions\n", "    print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())\n", "\n", "    # Run over everything\n", "    cerebro.run()\n", "\n", "    # Print out the final result\n", "    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())"]}, {"cell_type": "code", "execution_count": 3, "id": "3e0d795b-ed13-443b-9613-f85caa5cb14b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting Portfolio Value: 1000.00\n", "2000-01-24, Close, 24.10\n", "2000-01-25, Close, 25.10\n", "2000-01-25, BUY CREATE, 25.10\n", "2000-01-26, BUY EXECUTED, Price: 25.24, Cost: 252.40, Comm 0.00\n", "2000-01-26, Close, 24.49\n", "2000-01-27, Close, 23.04\n", "2000-01-27, SELL CREATE, 23.04\n", "2000-01-28, SELL EXECUTED, Price: 22.90, Cost: 252.40, Comm 0.00\n", "2000-01-28, OPERATION PROFIT, GROSS -23.40, NET -23.40\n", "2000-01-28, Close, 21.07\n", "2000-01-31, Close, 22.22\n", "2000-02-01, Close, 24.02\n", "2000-02-02, Close, 24.16\n", "2000-02-02, BUY CREATE, 24.16\n", "2000-02-03, BUY EXECUTED, Price: 24.63, Cost: 246.30, Comm 0.00\n", "2000-02-03, Close, 25.21\n", "2000-02-04, Close, 25.71\n", "2000-02-07, Close, 26.66\n", "2000-02-08, Close, 26.49\n", "2000-02-09, Close, 26.66\n", "2000-02-10, Close, 27.71\n", "2000-02-11, Close, 26.55\n", "2000-02-14, Close, 27.66\n", "2000-02-15, Close, 27.30\n", "2000-02-16, Close, 27.24\n", "2000-02-17, Close, 27.41\n", "2000-02-18, Close, 26.05\n", "2000-02-18, SELL CREATE, 26.05\n", "2000-02-22, SELL EXECUTED, Price: 26.30, Cost: 246.30, Comm 0.00\n", "2000-02-22, OPERATION PROFIT, GROSS 16.70, NET 16.70\n", "2000-02-22, Close, 26.38\n", "2000-02-22, BUY CREATE, 26.38\n", "2000-02-23, BUY EXECUTED, Price: 26.77, Cost: 267.70, Comm 0.00\n", "2000-02-23, Close, 28.05\n", "2000-02-24, Close, 27.55\n", "2000-02-25, Close, 31.41\n", "2000-02-28, Close, 30.52\n", "2000-02-29, Close, 33.02\n", "2000-03-01, Close, 31.80\n", "2000-03-02, Close, 30.47\n", "2000-03-03, Close, 33.36\n", "2000-03-06, Close, 33.69\n", "2000-03-07, Close, 33.33\n", "2000-03-08, Close, 36.97\n", "2000-03-09, Close, 37.36\n", "2000-03-10, Close, 36.30\n", "2000-03-13, Close, 35.02\n", "2000-03-14, Close, 34.25\n", "2000-03-15, Close, 34.97\n", "2000-03-16, Close, 36.44\n", "2000-03-17, Close, 35.50\n", "2000-03-20, Close, 34.75\n", "2000-03-21, Close, 35.89\n", "2000-03-22, Close, 37.39\n", "2000-03-23, Close, 38.64\n", "2000-03-24, Close, 38.69\n", "2000-03-27, Close, 39.33\n", "2000-03-28, Close, 38.50\n", "2000-03-29, Close, 36.69\n", "2000-03-30, Close, 34.88\n", "2000-03-30, SELL CREATE, 34.88\n", "2000-03-31, SELL EXECUTED, Price: 35.66, Cost: 267.70, Comm 0.00\n", "2000-03-31, O<PERSON>ERAT<PERSON> PROFIT, GROSS 88.90, NET 88.90\n", "2000-03-31, Close, 34.72\n", "2000-04-03, Close, 34.19\n", "2000-04-04, Close, 33.77\n", "2000-04-05, Close, 34.80\n", "2000-04-06, Close, 36.55\n", "2000-04-06, BUY CREATE, 36.55\n", "2000-04-07, BUY EXECUTED, Price: 37.22, Cost: 372.20, Comm 0.00\n", "2000-04-07, Close, 38.75\n", "2000-04-10, Close, 36.69\n", "2000-04-11, Close, 34.41\n", "2000-04-11, SELL CREATE, 34.41\n", "2000-04-12, SELL EXECUTED, Price: 34.66, Cost: 372.20, Comm 0.00\n", "2000-04-12, OPERATION PROFIT, GROSS -25.60, NET -25.60\n", "2000-04-12, Close, 32.52\n", "2000-04-13, Close, 31.99\n", "2000-04-14, Close, 27.80\n", "2000-04-17, Close, 33.27\n", "2000-04-18, Close, 35.11\n", "2000-04-18, BUY CREATE, 35.11\n", "2000-04-19, BUY EXECUTED, Price: 34.97, Cost: 349.70, Comm 0.00\n", "2000-04-19, Close, 33.16\n", "2000-04-19, SELL CREATE, 33.16\n", "2000-04-20, SELL EXECUTED, Price: 32.83, Cost: 349.70, Comm 0.00\n", "2000-04-20, OPERATION PROFIT, GROSS -21.40, NET -21.40\n", "2000-04-20, Close, 31.49\n", "2000-04-24, Close, 32.22\n", "2000-04-25, Close, 33.61\n", "2000-04-26, Close, 32.11\n", "2000-04-27, Close, 34.38\n", "2000-04-27, BUY CREATE, 34.38\n", "2000-04-28, BUY EXECUTED, Price: 34.91, Cost: 349.10, Comm 0.00\n", "2000-04-28, Close, 35.55\n", "2000-05-01, Close, 35.44\n", "2000-05-02, Close, 34.61\n", "2000-05-03, Close, 33.72\n", "2000-05-04, Close, 33.02\n", "2000-05-04, SELL CREATE, 33.02\n", "2000-05-05, SELL EXECUTED, Price: 32.91, Cost: 349.10, Comm 0.00\n", "2000-05-05, OPERATION PROFIT, GROSS -20.00, NET -20.00\n", "2000-05-05, Close, 34.16\n", "2000-05-05, BUY CREATE, 34.16\n", "2000-05-08, BUY EXECUTED, Price: 33.49, Cost: 334.90, Comm 0.00\n", "2000-05-08, Close, 32.16\n", "2000-05-08, SELL CREATE, 32.16\n", "2000-05-09, SELL EXECUTED, Price: 32.77, Cost: 334.90, Comm 0.00\n", "2000-05-09, OPERATION PROFIT, GROSS -7.20, NET -7.20\n", "2000-05-09, Close, 32.02\n", "2000-05-10, Close, 30.08\n", "2000-05-11, Close, 32.19\n", "2000-05-12, Close, 32.99\n", "2000-05-15, Close, 34.25\n", "2000-05-15, BUY CREATE, 34.25\n", "2000-05-16, BUY EXECUTED, Price: 34.52, Cost: 345.20, Comm 0.00\n", "2000-05-16, Close, 35.22\n", "2000-05-17, Close, 34.77\n", "2000-05-18, Close, 32.49\n", "2000-05-18, SELL CREATE, 32.49\n", "2000-05-19, SELL EXECUTED, Price: 32.02, Cost: 345.20, Comm 0.00\n", "2000-05-19, OPERATION PROFIT, GROSS -25.00, NET -25.00\n", "2000-05-19, Close, 31.16\n", "2000-05-22, Close, 30.16\n", "2000-05-23, Close, 27.85\n", "2000-05-24, Close, 28.57\n", "2000-05-25, Close, 29.55\n", "2000-05-26, Close, 29.80\n", "2000-05-30, Close, 32.99\n", "2000-05-30, BUY CREATE, 32.99\n", "2000-05-31, BUY EXECUTED, Price: 32.58, Cost: 325.80, Comm 0.00\n", "2000-05-31, Close, 31.97\n", "2000-06-01, Close, 34.63\n", "2000-06-02, Close, 35.66\n", "2000-06-05, Close, 36.00\n", "2000-06-06, Close, 34.27\n", "2000-06-07, Close, 35.58\n", "2000-06-08, Close, 36.64\n", "2000-06-09, Close, 36.77\n", "2000-06-12, Close, 35.83\n", "2000-06-13, Close, 36.33\n", "2000-06-14, Close, 35.13\n", "2000-06-15, Close, 36.69\n", "2000-06-16, Close, 36.41\n", "2000-06-19, Close, 38.25\n", "2000-06-20, Close, 38.27\n", "2000-06-21, Close, 38.33\n", "2000-06-22, Close, 36.25\n", "2000-06-22, SELL CREATE, 36.25\n", "2000-06-23, SELL EXECUTED, Price: 35.94, Cost: 325.80, Comm 0.00\n", "2000-06-23, OPERATION PROFIT, GROSS 33.60, NET 33.60\n", "2000-06-23, Close, 35.36\n", "2000-06-26, Close, 36.77\n", "2000-06-26, BUY CREATE, 36.77\n", "2000-06-27, BUY EXECUTED, Price: 36.64, Cost: 366.40, Comm 0.00\n", "2000-06-27, Close, 36.58\n", "2000-06-27, SELL CREATE, 36.58\n", "2000-06-28, SELL EXECUTED, Price: 36.50, Cost: 366.40, Comm 0.00\n", "2000-06-28, OPERATION PROFIT, GROSS -1.40, NET -1.40\n", "2000-06-28, Close, 36.89\n", "2000-06-28, BUY CREATE, 36.89\n", "2000-06-29, BUY EXECUTED, Price: 36.50, Cost: 365.00, Comm 0.00\n", "2000-06-29, Close, 35.97\n", "2000-06-29, SELL CREATE, 35.97\n", "2000-06-30, SELL EXECUTED, Price: 35.75, Cost: 365.00, Comm 0.00\n", "2000-06-30, OPERATION PROFIT, GROSS -7.50, NET -7.50\n", "2000-06-30, Close, 37.39\n", "2000-06-30, BUY CREATE, 37.39\n", "2000-07-03, BUY EXECUTED, Price: 36.08, Cost: 360.80, Comm 0.00\n", "2000-07-03, Close, 35.66\n", "2000-07-03, SELL CREATE, 35.66\n", "2000-07-05, SELL EXECUTED, Price: 34.16, Cost: 360.80, Comm 0.00\n", "2000-07-05, OPERATION PROFIT, GROSS -19.20, NET -19.20\n", "2000-07-05, Close, 32.16\n", "2000-07-06, Close, 33.63\n", "2000-07-07, Close, 33.75\n", "2000-07-10, Close, 32.97\n", "2000-07-11, Close, 32.16\n", "2000-07-12, Close, 33.22\n", "2000-07-13, Close, 33.69\n", "2000-07-14, Close, 33.86\n", "2000-07-17, Close, 33.86\n", "2000-07-18, Close, 32.99\n", "2000-07-19, Close, 32.80\n", "2000-07-20, Close, 34.75\n", "2000-07-20, BUY CREATE, 34.75\n", "2000-07-21, BUY EXECUTED, Price: 34.44, Cost: 344.40, Comm 0.00\n", "2000-07-21, Close, 33.55\n", "2000-07-21, SELL CREATE, 33.55\n", "2000-07-24, SELL EXECUTED, Price: 34.30, Cost: 344.40, Comm 0.00\n", "2000-07-24, OPERATION PROFIT, GROSS -1.40, NET -1.40\n", "2000-07-24, Close, 33.36\n", "2000-07-25, Close, 33.80\n", "2000-07-25, BUY CREATE, 33.80\n", "2000-07-26, BUY EXECUTED, Price: 33.27, Cost: 332.70, Comm 0.00\n", "2000-07-26, Close, 34.13\n", "2000-07-27, Close, 33.38\n", "2000-07-27, SELL CREATE, 33.38\n", "2000-07-28, SELL EXECUTED, Price: 33.41, Cost: 332.70, Comm 0.00\n", "2000-07-28, OPERATION PROFIT, GROSS 1.40, NET 1.40\n", "2000-07-28, Close, 32.19\n", "2000-07-31, Close, 33.44\n", "2000-07-31, BUY CREATE, 33.44\n", "2000-08-01, BUY EXECUTED, Price: 33.44, Cost: 334.40, Comm 0.00\n", "2000-08-01, Close, 32.52\n", "2000-08-01, SELL CREATE, 32.52\n", "2000-08-02, SELL EXECUTED, Price: 32.47, Cost: 334.40, Comm 0.00\n", "2000-08-02, OPERATION PROFIT, GROSS -9.70, NET -9.70\n", "2000-08-02, Close, 32.52\n", "2000-08-03, Close, 34.44\n", "2000-08-03, BUY CREATE, 34.44\n", "2000-08-04, BUY EXECUTED, Price: 34.83, Cost: 348.30, Comm 0.00\n", "2000-08-04, Close, 36.27\n", "2000-08-07, Close, 36.41\n", "2000-08-08, Close, 36.91\n", "2000-08-09, Close, 36.19\n", "2000-08-10, Close, 35.61\n", "2000-08-11, Close, 36.08\n", "2000-08-14, Close, 36.64\n", "2000-08-15, Close, 36.14\n", "2000-08-16, Close, 36.11\n", "2000-08-17, Close, 37.33\n", "2000-08-18, Close, 36.16\n", "2000-08-21, Close, 37.00\n", "2000-08-22, Close, 37.16\n", "2000-08-23, Close, 36.86\n", "2000-08-24, Close, 37.66\n", "2000-08-25, Close, 37.64\n", "2000-08-28, Close, 38.58\n", "2000-08-29, Close, 39.03\n", "2000-08-30, Close, 39.25\n", "2000-08-31, Close, 40.44\n", "2000-09-01, Close, 41.19\n", "2000-09-05, Close, 40.50\n", "2000-09-06, Close, 39.69\n", "2000-09-07, Close, 40.56\n", "2000-09-08, Close, 38.50\n", "2000-09-08, SELL CREATE, 38.50\n", "2000-09-11, SELL EXECUTED, Price: 38.28, Cost: 348.30, Comm 0.00\n", "2000-09-11, OPERATION PROFIT, GROSS 34.50, NET 34.50\n", "2000-09-11, Close, 37.11\n", "2000-09-12, Close, 35.30\n", "2000-09-13, Close, 36.39\n", "2000-09-14, Close, 37.78\n", "2000-09-15, Close, 34.83\n", "2000-09-18, Close, 34.01\n", "2000-09-19, Close, 35.27\n", "2000-09-20, Close, 35.55\n", "2000-09-21, Close, 35.11\n", "2000-09-22, Close, 35.91\n", "2000-09-25, Close, 35.02\n", "2000-09-26, Close, 35.33\n", "2000-09-27, Close, 35.52\n", "2000-09-28, Close, 36.24\n", "2000-09-28, BUY CREATE, 36.24\n", "2000-09-29, BUY EXECUTED, Price: 36.18, Cost: 361.80, Comm 0.00\n", "2000-09-29, Close, 35.02\n", "2000-09-29, SELL CREATE, 35.02\n", "2000-10-02, SELL EXECUTED, Price: 35.47, Cost: 361.80, Comm 0.00\n", "2000-10-02, OPERATION PROFIT, GROSS -7.10, NET -7.10\n", "2000-10-02, Close, 35.02\n", "2000-10-03, Close, 30.91\n", "2000-10-04, Close, 30.30\n", "2000-10-05, Close, 30.38\n", "2000-10-06, Close, 30.08\n", "2000-10-09, Close, 29.69\n", "2000-10-10, Close, 28.74\n", "2000-10-11, Close, 27.69\n", "2000-10-12, Close, 28.02\n", "2000-10-13, Close, 31.69\n", "2000-10-16, Close, 30.74\n", "2000-10-17, Close, 29.96\n", "2000-10-18, Close, 29.85\n", "2000-10-19, Close, 32.36\n", "2000-10-19, BUY CREATE, 32.36\n", "2000-10-20, BUY EXECUTED, Price: 32.13, Cost: 321.30, Comm 0.00\n", "2000-10-20, Close, 31.35\n", "2000-10-23, Close, 30.30\n", "2000-10-24, Close, 31.85\n", "2000-10-25, Close, 30.58\n", "2000-10-26, Close, 30.30\n", "2000-10-27, Close, 30.41\n", "2000-10-30, Close, 28.13\n", "2000-10-30, SELL CREATE, 28.13\n", "2000-10-31, SELL EXECUTED, Price: 29.02, Cost: 321.30, Comm 0.00\n", "2000-10-31, OPERATION PROFIT, GROSS -31.10, NET -31.10\n", "2000-10-31, Close, 29.35\n", "2000-11-01, Close, 27.91\n", "2000-11-02, Close, 26.30\n", "2000-11-03, Close, 26.96\n", "2000-11-06, Close, 24.85\n", "2000-11-07, Close, 23.63\n", "2000-11-08, Close, 22.07\n", "2000-11-09, Close, 24.18\n", "2000-11-10, Close, 22.63\n", "2000-11-13, Close, 22.01\n", "2000-11-14, Close, 25.24\n", "2000-11-15, Close, 25.68\n", "2000-11-16, Close, 24.35\n", "2000-11-17, Close, 25.63\n", "2000-11-17, BUY CREATE, 25.63\n", "2000-11-20, BUY EXECUTED, Price: 21.63, Cost: 216.30, Comm 0.00\n", "2000-11-20, Close, 22.01\n", "2000-11-20, SELL CREATE, 22.01\n", "2000-11-21, SELL EXECUTED, Price: 22.07, Cost: 216.30, Comm 0.00\n", "2000-11-21, OPERATION PROFIT, GROSS 4.40, NET 4.40\n", "2000-11-21, Close, 21.24\n", "2000-11-22, Close, 19.85\n", "2000-11-24, Close, 21.46\n", "2000-11-27, Close, 20.57\n", "2000-11-28, Close, 20.15\n", "2000-11-29, Close, 20.35\n", "2000-11-30, Close, 23.57\n", "2000-11-30, BUY CREATE, 23.57\n", "2000-12-01, BUY EXECUTED, Price: 23.46, Cost: 234.60, Comm 0.00\n", "2000-12-01, Close, 23.52\n", "2000-12-04, Close, 25.07\n", "2000-12-05, Close, 28.02\n", "2000-12-06, Close, 26.85\n", "2000-12-07, Close, 25.18\n", "2000-12-08, Close, 26.74\n", "2000-12-11, Close, 28.41\n", "2000-12-12, Close, 27.35\n", "2000-12-13, Close, 25.24\n", "2000-12-14, Close, 24.46\n", "2000-12-14, SELL CREATE, 24.46\n", "2000-12-15, SELL EXECUTED, Price: 26.18, Cost: 234.60, Comm 0.00\n", "2000-12-15, OPERATION PROFIT, GROSS 27.20, NET 27.20\n", "2000-12-15, Close, 25.41\n", "2000-12-15, BUY CREATE, 25.41\n", "2000-12-18, BUY EXECUTED, Price: 26.68, Cost: 266.80, Comm 0.00\n", "2000-12-18, Close, 28.46\n", "2000-12-19, Close, 27.24\n", "2000-12-20, Close, 25.35\n", "2000-12-20, SELL CREATE, 25.35\n", "2000-12-21, SELL EXECUTED, Price: 24.74, Cost: 266.80, Comm 0.00\n", "2000-12-21, OPERATION PROFIT, GROSS -19.40, NET -19.40\n", "2000-12-21, Close, 26.24\n", "2000-12-21, BUY CREATE, 26.24\n", "2000-12-22, BUY EXECUTED, Price: 27.02, Cost: 270.20, Comm 0.00\n", "2000-12-22, Close, 28.35\n", "2000-12-26, Close, 27.52\n", "2000-12-27, Close, 27.30\n", "2000-12-28, Close, 27.63\n", "2000-12-29, Close, 25.85\n", "2000-12-29, SELL CREATE, 25.85\n", "Final Portfolio Value: 975.60\n"]}], "source": ["from __future__ import (absolute_import, division, print_function,\n", "                        unicode_literals)\n", "\n", "import datetime  # For datetime objects\n", "import os.path  # To manage paths\n", "import sys  # To find out the script name (in argv[0])\n", "\n", "# Import the backtrader platform\n", "import backtrader as bt\n", "\n", "\n", "# Create a Stratey\n", "class TestStrategy(bt.Strategy):\n", "    params = (\n", "        ('maperi<PERSON>', 15),\n", "    )\n", "\n", "    def log(self, txt, dt=None):\n", "        ''' Logging function fot this strategy'''\n", "        dt = dt or self.datas[0].datetime.date(0)\n", "        print('%s, %s' % (dt.isoformat(), txt))\n", "\n", "    def __init__(self):\n", "        # Keep a reference to the \"close\" line in the data[0] dataseries\n", "        self.dataclose = self.datas[0].close\n", "\n", "        # To keep track of pending orders and buy price/commission\n", "        self.order = None\n", "        self.buyprice = None\n", "        self.buycomm = None\n", "\n", "        # Add a MovingAverageSimple indicator\n", "        self.sma = bt.indicators.SimpleMovingAverage(\n", "            self.datas[0], period=self.params.maperiod)\n", "\n", "    def notify_order(self, order):\n", "        if order.status in [order.Submitted, order.Accepted]:\n", "            # Buy/Sell order submitted/accepted to/by broker - Nothing to do\n", "            return\n", "\n", "        # Check if an order has been completed\n", "        # Attention: broker could reject order if not enough cash\n", "        if order.status in [order.Completed]:\n", "            if order.isbuy():\n", "                self.log(\n", "                    'BUY EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %\n", "                    (order.executed.price,\n", "                     order.executed.value,\n", "                     order.executed.comm))\n", "\n", "                self.buyprice = order.executed.price\n", "                self.buycomm = order.executed.comm\n", "            else:  # Sell\n", "                self.log('SELL EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %\n", "                         (order.executed.price,\n", "                          order.executed.value,\n", "                          order.executed.comm))\n", "\n", "            self.bar_executed = len(self)\n", "\n", "        elif order.status in [order.Canceled, order.Margin, order.Rejected]:\n", "            self.log('Order Canceled/Margin/Rejected')\n", "\n", "        self.order = None\n", "\n", "    def notify_trade(self, trade):\n", "        if not trade.isclosed:\n", "            return\n", "\n", "        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' %\n", "                 (trade.pnl, trade.pnlcomm))\n", "\n", "    def next(self):\n", "        # Simply log the closing price of the series from the reference\n", "        self.log('Close, %.2f' % self.dataclose[0])\n", "\n", "        # Check if an order is pending ... if yes, we cannot send a 2nd one\n", "        if self.order:\n", "            return\n", "\n", "        # Check if we are in the market\n", "        if not self.position:\n", "\n", "            # Not yet ... we MIGHT BUY if ...\n", "            if self.dataclose[0] > self.sma[0]:\n", "\n", "                # BUY, BUY, BUY!!! (with all possible default parameters)\n", "                self.log('BUY CREATE, %.2f' % self.dataclose[0])\n", "\n", "                # Keep track of the created order to avoid a 2nd order\n", "                self.order = self.buy()\n", "\n", "        else:\n", "\n", "            if self.dataclose[0] < self.sma[0]:\n", "                # SELL, SELL, SELL!!! (with all possible default parameters)\n", "                self.log('SELL CREATE, %.2f' % self.dataclose[0])\n", "\n", "                # Keep track of the created order to avoid a 2nd order\n", "                self.order = self.sell()\n", "\n", "\n", "if __name__ == '__main__':\n", "    # Create a cerebro entity\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()\n", "\n", "    # Add a strategy\n", "    cerebro.addstrategy(TestStrategy)\n", "\n", "    # Datas are in a subfolder of the samples. Need to find where the script is\n", "    # because it could have been called from anywhere\n", "    try:\n", "        # Try to get the directory of the script\n", "        script_directory = os.path.dirname(os.path.abspath(__file__))\n", "    except NameError:\n", "        # Fallback to the current working directory if __file__ is not defined\n", "        script_directory = os.getcwd()\n", "    datapath = os.path.join(script_directory, 'backtrader\\\\datas\\\\orcl-1995-2014.txt')\n", "\n", "    # Create a Data Feed\n", "    data = bt.feeds.YahooFinanceCSVData(\n", "        dataname=datapath,\n", "        # Do not pass values before this date\n", "        fromdate=datetime.datetime(2000, 1, 1),\n", "        # Do not pass values before this date\n", "        todate=datetime.datetime(2000, 12, 31),\n", "        # Do not pass values after this date\n", "        reverse=False)\n", "\n", "    # Add the Data Feed to Cerebro\n", "    cerebro.adddata(data)\n", "\n", "    # Set our desired cash start\n", "    cerebro.broker.setcash(1000.0)\n", "\n", "    # Add a FixedSize sizer according to the stake\n", "    cerebro.addsizer(bt.sizers.FixedSize, stake=10)\n", "\n", "    # Set the commission\n", "    cerebro.broker.setcommission(commission=0.0)\n", "\n", "    # Print out the starting conditions\n", "    print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())\n", "\n", "    # Run over everything\n", "    cerebro.run()\n", "\n", "    # Print out the final result\n", "    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())"]}, {"cell_type": "code", "execution_count": 4, "id": "e70e4e50-7728-4736-a5e6-f9620bb3ccf1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting Portfolio Value: 1000.00\n", "2000-02-18, Close, 26.05\n", "2000-02-22, Close, 26.38\n", "2000-02-22, BUY CREATE, 26.38\n", "2000-02-23, BUY EXECUTED, Price: 26.77, Cost: 267.70, Comm 0.00\n", "2000-02-23, Close, 28.05\n", "2000-02-24, Close, 27.55\n", "2000-02-25, Close, 31.41\n", "2000-02-28, Close, 30.52\n", "2000-02-29, Close, 33.02\n", "2000-03-01, Close, 31.80\n", "2000-03-02, Close, 30.47\n", "2000-03-03, Close, 33.36\n", "2000-03-06, Close, 33.69\n", "2000-03-07, Close, 33.33\n", "2000-03-08, Close, 36.97\n", "2000-03-09, Close, 37.36\n", "2000-03-10, Close, 36.30\n", "2000-03-13, Close, 35.02\n", "2000-03-14, Close, 34.25\n", "2000-03-15, Close, 34.97\n", "2000-03-16, Close, 36.44\n", "2000-03-17, Close, 35.50\n", "2000-03-20, Close, 34.75\n", "2000-03-21, Close, 35.89\n", "2000-03-22, Close, 37.39\n", "2000-03-23, Close, 38.64\n", "2000-03-24, Close, 38.69\n", "2000-03-27, Close, 39.33\n", "2000-03-28, Close, 38.50\n", "2000-03-29, Close, 36.69\n", "2000-03-30, Close, 34.88\n", "2000-03-30, SELL CREATE, 34.88\n", "2000-03-31, SELL EXECUTED, Price: 35.66, Cost: 267.70, Comm 0.00\n", "2000-03-31, O<PERSON>ERAT<PERSON> PROFIT, GROSS 88.90, NET 88.90\n", "2000-03-31, Close, 34.72\n", "2000-04-03, Close, 34.19\n", "2000-04-04, Close, 33.77\n", "2000-04-05, Close, 34.80\n", "2000-04-06, Close, 36.55\n", "2000-04-06, BUY CREATE, 36.55\n", "2000-04-07, BUY EXECUTED, Price: 37.22, Cost: 372.20, Comm 0.00\n", "2000-04-07, Close, 38.75\n", "2000-04-10, Close, 36.69\n", "2000-04-11, Close, 34.41\n", "2000-04-11, SELL CREATE, 34.41\n", "2000-04-12, SELL EXECUTED, Price: 34.66, Cost: 372.20, Comm 0.00\n", "2000-04-12, OPERATION PROFIT, GROSS -25.60, NET -25.60\n", "2000-04-12, Close, 32.52\n", "2000-04-13, Close, 31.99\n", "2000-04-14, Close, 27.80\n", "2000-04-17, Close, 33.27\n", "2000-04-18, Close, 35.11\n", "2000-04-18, BUY CREATE, 35.11\n", "2000-04-19, BUY EXECUTED, Price: 34.97, Cost: 349.70, Comm 0.00\n", "2000-04-19, Close, 33.16\n", "2000-04-19, SELL CREATE, 33.16\n", "2000-04-20, SELL EXECUTED, Price: 32.83, Cost: 349.70, Comm 0.00\n", "2000-04-20, OPERATION PROFIT, GROSS -21.40, NET -21.40\n", "2000-04-20, Close, 31.49\n", "2000-04-24, Close, 32.22\n", "2000-04-25, Close, 33.61\n", "2000-04-26, Close, 32.11\n", "2000-04-27, Close, 34.38\n", "2000-04-27, BUY CREATE, 34.38\n", "2000-04-28, BUY EXECUTED, Price: 34.91, Cost: 349.10, Comm 0.00\n", "2000-04-28, Close, 35.55\n", "2000-05-01, Close, 35.44\n", "2000-05-02, Close, 34.61\n", "2000-05-03, Close, 33.72\n", "2000-05-04, Close, 33.02\n", "2000-05-04, SELL CREATE, 33.02\n", "2000-05-05, SELL EXECUTED, Price: 32.91, Cost: 349.10, Comm 0.00\n", "2000-05-05, OPERATION PROFIT, GROSS -20.00, NET -20.00\n", "2000-05-05, Close, 34.16\n", "2000-05-05, BUY CREATE, 34.16\n", "2000-05-08, BUY EXECUTED, Price: 33.49, Cost: 334.90, Comm 0.00\n", "2000-05-08, Close, 32.16\n", "2000-05-08, SELL CREATE, 32.16\n", "2000-05-09, SELL EXECUTED, Price: 32.77, Cost: 334.90, Comm 0.00\n", "2000-05-09, OPERATION PROFIT, GROSS -7.20, NET -7.20\n", "2000-05-09, Close, 32.02\n", "2000-05-10, Close, 30.08\n", "2000-05-11, Close, 32.19\n", "2000-05-12, Close, 32.99\n", "2000-05-15, Close, 34.25\n", "2000-05-15, BUY CREATE, 34.25\n", "2000-05-16, BUY EXECUTED, Price: 34.52, Cost: 345.20, Comm 0.00\n", "2000-05-16, Close, 35.22\n", "2000-05-17, Close, 34.77\n", "2000-05-18, Close, 32.49\n", "2000-05-18, SELL CREATE, 32.49\n", "2000-05-19, SELL EXECUTED, Price: 32.02, Cost: 345.20, Comm 0.00\n", "2000-05-19, OPERATION PROFIT, GROSS -25.00, NET -25.00\n", "2000-05-19, Close, 31.16\n", "2000-05-22, Close, 30.16\n", "2000-05-23, Close, 27.85\n", "2000-05-24, Close, 28.57\n", "2000-05-25, Close, 29.55\n", "2000-05-26, Close, 29.80\n", "2000-05-30, Close, 32.99\n", "2000-05-30, BUY CREATE, 32.99\n", "2000-05-31, BUY EXECUTED, Price: 32.58, Cost: 325.80, Comm 0.00\n", "2000-05-31, Close, 31.97\n", "2000-06-01, Close, 34.63\n", "2000-06-02, Close, 35.66\n", "2000-06-05, Close, 36.00\n", "2000-06-06, Close, 34.27\n", "2000-06-07, Close, 35.58\n", "2000-06-08, Close, 36.64\n", "2000-06-09, Close, 36.77\n", "2000-06-12, Close, 35.83\n", "2000-06-13, Close, 36.33\n", "2000-06-14, Close, 35.13\n", "2000-06-15, Close, 36.69\n", "2000-06-16, Close, 36.41\n", "2000-06-19, Close, 38.25\n", "2000-06-20, Close, 38.27\n", "2000-06-21, Close, 38.33\n", "2000-06-22, Close, 36.25\n", "2000-06-22, SELL CREATE, 36.25\n", "2000-06-23, SELL EXECUTED, Price: 35.94, Cost: 325.80, Comm 0.00\n", "2000-06-23, OPERATION PROFIT, GROSS 33.60, NET 33.60\n", "2000-06-23, Close, 35.36\n", "2000-06-26, Close, 36.77\n", "2000-06-26, BUY CREATE, 36.77\n", "2000-06-27, BUY EXECUTED, Price: 36.64, Cost: 366.40, Comm 0.00\n", "2000-06-27, Close, 36.58\n", "2000-06-27, SELL CREATE, 36.58\n", "2000-06-28, SELL EXECUTED, Price: 36.50, Cost: 366.40, Comm 0.00\n", "2000-06-28, OPERATION PROFIT, GROSS -1.40, NET -1.40\n", "2000-06-28, Close, 36.89\n", "2000-06-28, BUY CREATE, 36.89\n", "2000-06-29, BUY EXECUTED, Price: 36.50, Cost: 365.00, Comm 0.00\n", "2000-06-29, Close, 35.97\n", "2000-06-29, SELL CREATE, 35.97\n", "2000-06-30, SELL EXECUTED, Price: 35.75, Cost: 365.00, Comm 0.00\n", "2000-06-30, OPERATION PROFIT, GROSS -7.50, NET -7.50\n", "2000-06-30, Close, 37.39\n", "2000-06-30, BUY CREATE, 37.39\n", "2000-07-03, BUY EXECUTED, Price: 36.08, Cost: 360.80, Comm 0.00\n", "2000-07-03, Close, 35.66\n", "2000-07-03, SELL CREATE, 35.66\n", "2000-07-05, SELL EXECUTED, Price: 34.16, Cost: 360.80, Comm 0.00\n", "2000-07-05, OPERATION PROFIT, GROSS -19.20, NET -19.20\n", "2000-07-05, Close, 32.16\n", "2000-07-06, Close, 33.63\n", "2000-07-07, Close, 33.75\n", "2000-07-10, Close, 32.97\n", "2000-07-11, Close, 32.16\n", "2000-07-12, Close, 33.22\n", "2000-07-13, Close, 33.69\n", "2000-07-14, Close, 33.86\n", "2000-07-17, Close, 33.86\n", "2000-07-18, Close, 32.99\n", "2000-07-19, Close, 32.80\n", "2000-07-20, Close, 34.75\n", "2000-07-20, BUY CREATE, 34.75\n", "2000-07-21, BUY EXECUTED, Price: 34.44, Cost: 344.40, Comm 0.00\n", "2000-07-21, Close, 33.55\n", "2000-07-21, SELL CREATE, 33.55\n", "2000-07-24, SELL EXECUTED, Price: 34.30, Cost: 344.40, Comm 0.00\n", "2000-07-24, OPERATION PROFIT, GROSS -1.40, NET -1.40\n", "2000-07-24, Close, 33.36\n", "2000-07-25, Close, 33.80\n", "2000-07-25, BUY CREATE, 33.80\n", "2000-07-26, BUY EXECUTED, Price: 33.27, Cost: 332.70, Comm 0.00\n", "2000-07-26, Close, 34.13\n", "2000-07-27, Close, 33.38\n", "2000-07-27, SELL CREATE, 33.38\n", "2000-07-28, SELL EXECUTED, Price: 33.41, Cost: 332.70, Comm 0.00\n", "2000-07-28, OPERATION PROFIT, GROSS 1.40, NET 1.40\n", "2000-07-28, Close, 32.19\n", "2000-07-31, Close, 33.44\n", "2000-07-31, BUY CREATE, 33.44\n", "2000-08-01, BUY EXECUTED, Price: 33.44, Cost: 334.40, Comm 0.00\n", "2000-08-01, Close, 32.52\n", "2000-08-01, SELL CREATE, 32.52\n", "2000-08-02, SELL EXECUTED, Price: 32.47, Cost: 334.40, Comm 0.00\n", "2000-08-02, OPERATION PROFIT, GROSS -9.70, NET -9.70\n", "2000-08-02, Close, 32.52\n", "2000-08-03, Close, 34.44\n", "2000-08-03, BUY CREATE, 34.44\n", "2000-08-04, BUY EXECUTED, Price: 34.83, Cost: 348.30, Comm 0.00\n", "2000-08-04, Close, 36.27\n", "2000-08-07, Close, 36.41\n", "2000-08-08, Close, 36.91\n", "2000-08-09, Close, 36.19\n", "2000-08-10, Close, 35.61\n", "2000-08-11, Close, 36.08\n", "2000-08-14, Close, 36.64\n", "2000-08-15, Close, 36.14\n", "2000-08-16, Close, 36.11\n", "2000-08-17, Close, 37.33\n", "2000-08-18, Close, 36.16\n", "2000-08-21, Close, 37.00\n", "2000-08-22, Close, 37.16\n", "2000-08-23, Close, 36.86\n", "2000-08-24, Close, 37.66\n", "2000-08-25, Close, 37.64\n", "2000-08-28, Close, 38.58\n", "2000-08-29, Close, 39.03\n", "2000-08-30, Close, 39.25\n", "2000-08-31, Close, 40.44\n", "2000-09-01, Close, 41.19\n", "2000-09-05, Close, 40.50\n", "2000-09-06, Close, 39.69\n", "2000-09-07, Close, 40.56\n", "2000-09-08, Close, 38.50\n", "2000-09-08, SELL CREATE, 38.50\n", "2000-09-11, SELL EXECUTED, Price: 38.28, Cost: 348.30, Comm 0.00\n", "2000-09-11, OPERATION PROFIT, GROSS 34.50, NET 34.50\n", "2000-09-11, Close, 37.11\n", "2000-09-12, Close, 35.30\n", "2000-09-13, Close, 36.39\n", "2000-09-14, Close, 37.78\n", "2000-09-15, Close, 34.83\n", "2000-09-18, Close, 34.01\n", "2000-09-19, Close, 35.27\n", "2000-09-20, Close, 35.55\n", "2000-09-21, Close, 35.11\n", "2000-09-22, Close, 35.91\n", "2000-09-25, Close, 35.02\n", "2000-09-26, Close, 35.33\n", "2000-09-27, Close, 35.52\n", "2000-09-28, Close, 36.24\n", "2000-09-28, BUY CREATE, 36.24\n", "2000-09-29, BUY EXECUTED, Price: 36.18, Cost: 361.80, Comm 0.00\n", "2000-09-29, Close, 35.02\n", "2000-09-29, SELL CREATE, 35.02\n", "2000-10-02, SELL EXECUTED, Price: 35.47, Cost: 361.80, Comm 0.00\n", "2000-10-02, OPERATION PROFIT, GROSS -7.10, NET -7.10\n", "2000-10-02, Close, 35.02\n", "2000-10-03, Close, 30.91\n", "2000-10-04, Close, 30.30\n", "2000-10-05, Close, 30.38\n", "2000-10-06, Close, 30.08\n", "2000-10-09, Close, 29.69\n", "2000-10-10, Close, 28.74\n", "2000-10-11, Close, 27.69\n", "2000-10-12, Close, 28.02\n", "2000-10-13, Close, 31.69\n", "2000-10-16, Close, 30.74\n", "2000-10-17, Close, 29.96\n", "2000-10-18, Close, 29.85\n", "2000-10-19, Close, 32.36\n", "2000-10-19, BUY CREATE, 32.36\n", "2000-10-20, BUY EXECUTED, Price: 32.13, Cost: 321.30, Comm 0.00\n", "2000-10-20, Close, 31.35\n", "2000-10-23, Close, 30.30\n", "2000-10-24, Close, 31.85\n", "2000-10-25, Close, 30.58\n", "2000-10-26, Close, 30.30\n", "2000-10-27, Close, 30.41\n", "2000-10-30, Close, 28.13\n", "2000-10-30, SELL CREATE, 28.13\n", "2000-10-31, SELL EXECUTED, Price: 29.02, Cost: 321.30, Comm 0.00\n", "2000-10-31, OPERATION PROFIT, GROSS -31.10, NET -31.10\n", "2000-10-31, Close, 29.35\n", "2000-11-01, Close, 27.91\n", "2000-11-02, Close, 26.30\n", "2000-11-03, Close, 26.96\n", "2000-11-06, Close, 24.85\n", "2000-11-07, Close, 23.63\n", "2000-11-08, Close, 22.07\n", "2000-11-09, Close, 24.18\n", "2000-11-10, Close, 22.63\n", "2000-11-13, Close, 22.01\n", "2000-11-14, Close, 25.24\n", "2000-11-15, Close, 25.68\n", "2000-11-16, Close, 24.35\n", "2000-11-17, Close, 25.63\n", "2000-11-17, BUY CREATE, 25.63\n", "2000-11-20, BUY EXECUTED, Price: 21.63, Cost: 216.30, Comm 0.00\n", "2000-11-20, Close, 22.01\n", "2000-11-20, SELL CREATE, 22.01\n", "2000-11-21, SELL EXECUTED, Price: 22.07, Cost: 216.30, Comm 0.00\n", "2000-11-21, OPERATION PROFIT, GROSS 4.40, NET 4.40\n", "2000-11-21, Close, 21.24\n", "2000-11-22, Close, 19.85\n", "2000-11-24, Close, 21.46\n", "2000-11-27, Close, 20.57\n", "2000-11-28, Close, 20.15\n", "2000-11-29, Close, 20.35\n", "2000-11-30, Close, 23.57\n", "2000-11-30, BUY CREATE, 23.57\n", "2000-12-01, BUY EXECUTED, Price: 23.46, Cost: 234.60, Comm 0.00\n", "2000-12-01, Close, 23.52\n", "2000-12-04, Close, 25.07\n", "2000-12-05, Close, 28.02\n", "2000-12-06, Close, 26.85\n", "2000-12-07, Close, 25.18\n", "2000-12-08, Close, 26.74\n", "2000-12-11, Close, 28.41\n", "2000-12-12, Close, 27.35\n", "2000-12-13, Close, 25.24\n", "2000-12-14, Close, 24.46\n", "2000-12-14, SELL CREATE, 24.46\n", "2000-12-15, SELL EXECUTED, Price: 26.18, Cost: 234.60, Comm 0.00\n", "2000-12-15, OPERATION PROFIT, GROSS 27.20, NET 27.20\n", "2000-12-15, Close, 25.41\n", "2000-12-15, BUY CREATE, 25.41\n", "2000-12-18, BUY EXECUTED, Price: 26.68, Cost: 266.80, Comm 0.00\n", "2000-12-18, Close, 28.46\n", "2000-12-19, Close, 27.24\n", "2000-12-20, Close, 25.35\n", "2000-12-20, SELL CREATE, 25.35\n", "2000-12-21, SELL EXECUTED, Price: 24.74, Cost: 266.80, Comm 0.00\n", "2000-12-21, OPERATION PROFIT, GROSS -19.40, NET -19.40\n", "2000-12-21, Close, 26.24\n", "2000-12-21, BUY CREATE, 26.24\n", "2000-12-22, BUY EXECUTED, Price: 27.02, Cost: 270.20, Comm 0.00\n", "2000-12-22, Close, 28.35\n", "2000-12-26, Close, 27.52\n", "2000-12-27, Close, 27.30\n", "2000-12-28, Close, 27.63\n", "2000-12-29, Close, 25.85\n", "2000-12-29, SELL CREATE, 25.85\n", "Final Portfolio Value: 982.30\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\backtest\\Lib\\site-packages\\backtrader\\cerebro.py:670: SyntaxWarning: invalid escape sequence '\\*'\n", "  '''Adds a callback to get messages which would be handled by the\n", "C:\\Users\\<USER>\\anaconda3\\envs\\backtest\\Lib\\site-packages\\backtrader\\cerebro.py:712: SyntaxWarning: invalid escape sequence '\\*'\n", "  '''Adds a callback to get messages which would be handled by the\n"]}, {"ename": "ImportError", "evalue": "<PERSON><PERSON><PERSON><PERSON><PERSON> seems to be missing. Needed for plotting support", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\backtrader\\plot\\__init__.py:28\u001b[0m\n\u001b[0;32m     27\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 28\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmatplotlib\u001b[39;00m\n\u001b[0;32m     29\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'matplotlib'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[1;32mIn[4], line 164\u001b[0m\n\u001b[0;32m    160\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFinal Portfolio Value: \u001b[39m\u001b[38;5;132;01m%.2f\u001b[39;00m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m%\u001b[39m cerebro\u001b[38;5;241m.\u001b[39mbroker\u001b[38;5;241m.\u001b[39mgetvalue())\n\u001b[0;32m    162\u001b[0m \u001b[38;5;66;03m# Plot the result\u001b[39;00m\n\u001b[0;32m    163\u001b[0m \u001b[38;5;66;03m#%matplotlib widget\u001b[39;00m\n\u001b[1;32m--> 164\u001b[0m cerebro\u001b[38;5;241m.\u001b[39mplot(iplot\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\backtrader\\cerebro.py:979\u001b[0m, in \u001b[0;36mCerebro.plot\u001b[1;34m(self, plotter, numfigs, iplot, start, end, width, height, dpi, tight, use, **kwargs)\u001b[0m\n\u001b[0;32m    976\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m\n\u001b[0;32m    978\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m plotter:\n\u001b[1;32m--> 979\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m plot\n\u001b[0;32m    980\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mp\u001b[38;5;241m.\u001b[39moldsync:\n\u001b[0;32m    981\u001b[0m         plotter \u001b[38;5;241m=\u001b[39m plot\u001b[38;5;241m.\u001b[39mPlot_OldSync(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\backtrader\\plot\\__init__.py:30\u001b[0m\n\u001b[0;32m     28\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmatplotlib\u001b[39;00m\n\u001b[0;32m     29\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[1;32m---> 30\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m(\n\u001b[0;32m     31\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMatplotlib seems to be missing. Needed for plotting support\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     32\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m     33\u001b[0m     touse \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mT<PERSON>gg\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m sys\u001b[38;5;241m.\u001b[39mplatform \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdarwin\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMacOSX\u001b[39m\u001b[38;5;124m'\u001b[39m\n", "\u001b[1;31mImportError\u001b[0m: <PERSON><PERSON><PERSON><PERSON><PERSON> seems to be missing. Needed for plotting support"]}], "source": ["from __future__ import (absolute_import, division, print_function,\n", "                        unicode_literals)\n", "\n", "import datetime  # For datetime objects\n", "import os.path  # To manage paths\n", "import sys  # To find out the script name (in argv[0])\n", "\n", "# Import the backtrader platform\n", "import backtrader as bt\n", "\n", "\n", "# Create a Stratey\n", "class TestStrategy(bt.Strategy):\n", "    params = (\n", "        ('maperi<PERSON>', 15),\n", "    )\n", "\n", "    def log(self, txt, dt=None):\n", "        ''' Logging function fot this strategy'''\n", "        dt = dt or self.datas[0].datetime.date(0)\n", "        print('%s, %s' % (dt.isoformat(), txt))\n", "\n", "    def __init__(self):\n", "        # Keep a reference to the \"close\" line in the data[0] dataseries\n", "        self.dataclose = self.datas[0].close\n", "\n", "        # To keep track of pending orders and buy price/commission\n", "        self.order = None\n", "        self.buyprice = None\n", "        self.buycomm = None\n", "\n", "        # Add a MovingAverageSimple indicator\n", "        self.sma = bt.indicators.SimpleMovingAverage(\n", "            self.datas[0], period=self.params.maperiod)\n", "\n", "        # Indicators for the plotting show\n", "        bt.indicators.ExponentialMovingAverage(self.datas[0], period=25)\n", "        bt.indicators.WeightedMovingAverage(self.datas[0], period=25,\n", "                                            subplot=True)\n", "        bt.indicators.StochasticSlow(self.datas[0])\n", "        bt.indicators.MACDHisto(self.datas[0])\n", "        rsi = bt.indicators.RSI(self.datas[0])\n", "        bt.indicators.SmoothedMovingAverage(rsi, period=10)\n", "        bt.indicators.ATR(self.datas[0], plot=False)\n", "\n", "    def notify_order(self, order):\n", "        if order.status in [order.Submitted, order.Accepted]:\n", "            # Buy/Sell order submitted/accepted to/by broker - Nothing to do\n", "            return\n", "\n", "        # Check if an order has been completed\n", "        # Attention: broker could reject order if not enough cash\n", "        if order.status in [order.Completed]:\n", "            if order.isbuy():\n", "                self.log(\n", "                    'BUY EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %\n", "                    (order.executed.price,\n", "                     order.executed.value,\n", "                     order.executed.comm))\n", "\n", "                self.buyprice = order.executed.price\n", "                self.buycomm = order.executed.comm\n", "            else:  # Sell\n", "                self.log('SELL EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %\n", "                         (order.executed.price,\n", "                          order.executed.value,\n", "                          order.executed.comm))\n", "\n", "            self.bar_executed = len(self)\n", "\n", "        elif order.status in [order.Canceled, order.Margin, order.Rejected]:\n", "            self.log('Order Canceled/Margin/Rejected')\n", "\n", "        # Write down: no pending order\n", "        self.order = None\n", "\n", "    def notify_trade(self, trade):\n", "        if not trade.isclosed:\n", "            return\n", "\n", "        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' %\n", "                 (trade.pnl, trade.pnlcomm))\n", "\n", "    def next(self):\n", "        # Simply log the closing price of the series from the reference\n", "        self.log('Close, %.2f' % self.dataclose[0])\n", "\n", "        # Check if an order is pending ... if yes, we cannot send a 2nd one\n", "        if self.order:\n", "            return\n", "\n", "        # Check if we are in the market\n", "        if not self.position:\n", "\n", "            # Not yet ... we MIGHT BUY if ...\n", "            if self.dataclose[0] > self.sma[0]:\n", "\n", "                # BUY, BUY, BUY!!! (with all possible default parameters)\n", "                self.log('BUY CREATE, %.2f' % self.dataclose[0])\n", "\n", "                # Keep track of the created order to avoid a 2nd order\n", "                self.order = self.buy()\n", "\n", "        else:\n", "\n", "            if self.dataclose[0] < self.sma[0]:\n", "                # SELL, SELL, SELL!!! (with all possible default parameters)\n", "                self.log('SELL CREATE, %.2f' % self.dataclose[0])\n", "\n", "                # Keep track of the created order to avoid a 2nd order\n", "                self.order = self.sell()\n", "\n", "\n", "if __name__ == '__main__':\n", "    # Create a cerebro entity\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()\n", "\n", "    # Add a strategy\n", "    cerebro.addstrategy(TestStrategy)\n", "\n", "    # Datas are in a subfolder of the samples. Need to find where the script is\n", "    # because it could have been called from anywhere\n", "    try:\n", "        # Try to get the directory of the script\n", "        script_directory = os.path.dirname(os.path.abspath(__file__))\n", "    except NameError:\n", "        # Fallback to the current working directory if __file__ is not defined\n", "        script_directory = os.getcwd()\n", "    datapath = os.path.join(script_directory, 'backtrader\\\\datas\\\\orcl-1995-2014.txt')\n", "\n", "    # Create a Data Feed\n", "    data = bt.feeds.YahooFinanceCSVData(\n", "        dataname=datapath,\n", "        # Do not pass values before this date\n", "        fromdate=datetime.datetime(2000, 1, 1),\n", "        # Do not pass values before this date\n", "        todate=datetime.datetime(2000, 12, 31),\n", "        # Do not pass values after this date\n", "        reverse=False)\n", "\n", "    # Add the Data Feed to Cerebro\n", "    cerebro.adddata(data)\n", "\n", "    # Set our desired cash start\n", "    cerebro.broker.setcash(1000.0)\n", "\n", "    # Add a FixedSize sizer according to the stake\n", "    cerebro.addsizer(bt.sizers.FixedSize, stake=10)\n", "\n", "    # Set the commission\n", "    cerebro.broker.setcommission(commission=0.0)\n", "\n", "    # Print out the starting conditions\n", "    print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())\n", "\n", "    # Run over everything\n", "    cerebro.run()\n", "\n", "    # Print out the final result\n", "    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())\n", "\n", "    # Plot the result\n", "    #%matplotlib widget\n", "    cerebro.plot(iplot=False)"]}, {"cell_type": "code", "execution_count": 6, "id": "6f82453b-befb-4e2e-89c3-448821b7754d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting Portfolio Value: 1000.00\n", "2000-02-18, Close, 26.05\n", "2000-02-22, Close, 26.38\n", "2000-02-22, BUY CREATE, 26.38\n", "2000-02-23, BUY EXECUTED, Price: 26.77, Cost: 267.70, Comm 0.00\n", "2000-02-23, Close, 28.05\n", "2000-02-24, Close, 27.55\n", "2000-02-25, Close, 31.41\n", "2000-02-28, Close, 30.52\n", "2000-02-29, Close, 33.02\n", "2000-03-01, Close, 31.80\n", "2000-03-02, Close, 30.47\n", "2000-03-03, Close, 33.36\n", "2000-03-06, Close, 33.69\n", "2000-03-07, Close, 33.33\n", "2000-03-08, Close, 36.97\n", "2000-03-09, Close, 37.36\n", "2000-03-10, Close, 36.30\n", "2000-03-13, Close, 35.02\n", "2000-03-14, Close, 34.25\n", "2000-03-15, Close, 34.97\n", "2000-03-16, Close, 36.44\n", "2000-03-17, Close, 35.50\n", "2000-03-20, Close, 34.75\n", "2000-03-21, Close, 35.89\n", "2000-03-22, Close, 37.39\n", "2000-03-23, Close, 38.64\n", "2000-03-24, Close, 38.69\n", "2000-03-27, Close, 39.33\n", "2000-03-28, Close, 38.50\n", "2000-03-29, Close, 36.69\n", "2000-03-30, Close, 34.88\n", "2000-03-30, SELL CREATE, 34.88\n", "2000-03-31, SELL EXECUTED, Price: 35.66, Cost: 267.70, Comm 0.00\n", "2000-03-31, O<PERSON>ERAT<PERSON> PROFIT, GROSS 88.90, NET 88.90\n", "2000-03-31, Close, 34.72\n", "2000-04-03, Close, 34.19\n", "2000-04-04, Close, 33.77\n", "2000-04-05, Close, 34.80\n", "2000-04-06, Close, 36.55\n", "2000-04-06, BUY CREATE, 36.55\n", "2000-04-07, BUY EXECUTED, Price: 37.22, Cost: 372.20, Comm 0.00\n", "2000-04-07, Close, 38.75\n", "2000-04-10, Close, 36.69\n", "2000-04-11, Close, 34.41\n", "2000-04-11, SELL CREATE, 34.41\n", "2000-04-12, SELL EXECUTED, Price: 34.66, Cost: 372.20, Comm 0.00\n", "2000-04-12, OPERATION PROFIT, GROSS -25.60, NET -25.60\n", "2000-04-12, Close, 32.52\n", "2000-04-13, Close, 31.99\n", "2000-04-14, Close, 27.80\n", "2000-04-17, Close, 33.27\n", "2000-04-18, Close, 35.11\n", "2000-04-18, BUY CREATE, 35.11\n", "2000-04-19, BUY EXECUTED, Price: 34.97, Cost: 349.70, Comm 0.00\n", "2000-04-19, Close, 33.16\n", "2000-04-19, SELL CREATE, 33.16\n", "2000-04-20, SELL EXECUTED, Price: 32.83, Cost: 349.70, Comm 0.00\n", "2000-04-20, OPERATION PROFIT, GROSS -21.40, NET -21.40\n", "2000-04-20, Close, 31.49\n", "2000-04-24, Close, 32.22\n", "2000-04-25, Close, 33.61\n", "2000-04-26, Close, 32.11\n", "2000-04-27, Close, 34.38\n", "2000-04-27, BUY CREATE, 34.38\n", "2000-04-28, BUY EXECUTED, Price: 34.91, Cost: 349.10, Comm 0.00\n", "2000-04-28, Close, 35.55\n", "2000-05-01, Close, 35.44\n", "2000-05-02, Close, 34.61\n", "2000-05-03, Close, 33.72\n", "2000-05-04, Close, 33.02\n", "2000-05-04, SELL CREATE, 33.02\n", "2000-05-05, SELL EXECUTED, Price: 32.91, Cost: 349.10, Comm 0.00\n", "2000-05-05, OPERATION PROFIT, GROSS -20.00, NET -20.00\n", "2000-05-05, Close, 34.16\n", "2000-05-05, BUY CREATE, 34.16\n", "2000-05-08, BUY EXECUTED, Price: 33.49, Cost: 334.90, Comm 0.00\n", "2000-05-08, Close, 32.16\n", "2000-05-08, SELL CREATE, 32.16\n", "2000-05-09, SELL EXECUTED, Price: 32.77, Cost: 334.90, Comm 0.00\n", "2000-05-09, OPERATION PROFIT, GROSS -7.20, NET -7.20\n", "2000-05-09, Close, 32.02\n", "2000-05-10, Close, 30.08\n", "2000-05-11, Close, 32.19\n", "2000-05-12, Close, 32.99\n", "2000-05-15, Close, 34.25\n", "2000-05-15, BUY CREATE, 34.25\n", "2000-05-16, BUY EXECUTED, Price: 34.52, Cost: 345.20, Comm 0.00\n", "2000-05-16, Close, 35.22\n", "2000-05-17, Close, 34.77\n", "2000-05-18, Close, 32.49\n", "2000-05-18, SELL CREATE, 32.49\n", "2000-05-19, SELL EXECUTED, Price: 32.02, Cost: 345.20, Comm 0.00\n", "2000-05-19, OPERATION PROFIT, GROSS -25.00, NET -25.00\n", "2000-05-19, Close, 31.16\n", "2000-05-22, Close, 30.16\n", "2000-05-23, Close, 27.85\n", "2000-05-24, Close, 28.57\n", "2000-05-25, Close, 29.55\n", "2000-05-26, Close, 29.80\n", "2000-05-30, Close, 32.99\n", "2000-05-30, BUY CREATE, 32.99\n", "2000-05-31, BUY EXECUTED, Price: 32.58, Cost: 325.80, Comm 0.00\n", "2000-05-31, Close, 31.97\n", "2000-06-01, Close, 34.63\n", "2000-06-02, Close, 35.66\n", "2000-06-05, Close, 36.00\n", "2000-06-06, Close, 34.27\n", "2000-06-07, Close, 35.58\n", "2000-06-08, Close, 36.64\n", "2000-06-09, Close, 36.77\n", "2000-06-12, Close, 35.83\n", "2000-06-13, Close, 36.33\n", "2000-06-14, Close, 35.13\n", "2000-06-15, Close, 36.69\n", "2000-06-16, Close, 36.41\n", "2000-06-19, Close, 38.25\n", "2000-06-20, Close, 38.27\n", "2000-06-21, Close, 38.33\n", "2000-06-22, Close, 36.25\n", "2000-06-22, SELL CREATE, 36.25\n", "2000-06-23, SELL EXECUTED, Price: 35.94, Cost: 325.80, Comm 0.00\n", "2000-06-23, OPERATION PROFIT, GROSS 33.60, NET 33.60\n", "2000-06-23, Close, 35.36\n", "2000-06-26, Close, 36.77\n", "2000-06-26, BUY CREATE, 36.77\n", "2000-06-27, BUY EXECUTED, Price: 36.64, Cost: 366.40, Comm 0.00\n", "2000-06-27, Close, 36.58\n", "2000-06-27, SELL CREATE, 36.58\n", "2000-06-28, SELL EXECUTED, Price: 36.50, Cost: 366.40, Comm 0.00\n", "2000-06-28, OPERATION PROFIT, GROSS -1.40, NET -1.40\n", "2000-06-28, Close, 36.89\n", "2000-06-28, BUY CREATE, 36.89\n", "2000-06-29, BUY EXECUTED, Price: 36.50, Cost: 365.00, Comm 0.00\n", "2000-06-29, Close, 35.97\n", "2000-06-29, SELL CREATE, 35.97\n", "2000-06-30, SELL EXECUTED, Price: 35.75, Cost: 365.00, Comm 0.00\n", "2000-06-30, OPERATION PROFIT, GROSS -7.50, NET -7.50\n", "2000-06-30, Close, 37.39\n", "2000-06-30, BUY CREATE, 37.39\n", "2000-07-03, BUY EXECUTED, Price: 36.08, Cost: 360.80, Comm 0.00\n", "2000-07-03, Close, 35.66\n", "2000-07-03, SELL CREATE, 35.66\n", "2000-07-05, SELL EXECUTED, Price: 34.16, Cost: 360.80, Comm 0.00\n", "2000-07-05, OPERATION PROFIT, GROSS -19.20, NET -19.20\n", "2000-07-05, Close, 32.16\n", "2000-07-06, Close, 33.63\n", "2000-07-07, Close, 33.75\n", "2000-07-10, Close, 32.97\n", "2000-07-11, Close, 32.16\n", "2000-07-12, Close, 33.22\n", "2000-07-13, Close, 33.69\n", "2000-07-14, Close, 33.86\n", "2000-07-17, Close, 33.86\n", "2000-07-18, Close, 32.99\n", "2000-07-19, Close, 32.80\n", "2000-07-20, Close, 34.75\n", "2000-07-20, BUY CREATE, 34.75\n", "2000-07-21, BUY EXECUTED, Price: 34.44, Cost: 344.40, Comm 0.00\n", "2000-07-21, Close, 33.55\n", "2000-07-21, SELL CREATE, 33.55\n", "2000-07-24, SELL EXECUTED, Price: 34.30, Cost: 344.40, Comm 0.00\n", "2000-07-24, OPERATION PROFIT, GROSS -1.40, NET -1.40\n", "2000-07-24, Close, 33.36\n", "2000-07-25, Close, 33.80\n", "2000-07-25, BUY CREATE, 33.80\n", "2000-07-26, BUY EXECUTED, Price: 33.27, Cost: 332.70, Comm 0.00\n", "2000-07-26, Close, 34.13\n", "2000-07-27, Close, 33.38\n", "2000-07-27, SELL CREATE, 33.38\n", "2000-07-28, SELL EXECUTED, Price: 33.41, Cost: 332.70, Comm 0.00\n", "2000-07-28, OPERATION PROFIT, GROSS 1.40, NET 1.40\n", "2000-07-28, Close, 32.19\n", "2000-07-31, Close, 33.44\n", "2000-07-31, BUY CREATE, 33.44\n", "2000-08-01, BUY EXECUTED, Price: 33.44, Cost: 334.40, Comm 0.00\n", "2000-08-01, Close, 32.52\n", "2000-08-01, SELL CREATE, 32.52\n", "2000-08-02, SELL EXECUTED, Price: 32.47, Cost: 334.40, Comm 0.00\n", "2000-08-02, OPERATION PROFIT, GROSS -9.70, NET -9.70\n", "2000-08-02, Close, 32.52\n", "2000-08-03, Close, 34.44\n", "2000-08-03, BUY CREATE, 34.44\n", "2000-08-04, BUY EXECUTED, Price: 34.83, Cost: 348.30, Comm 0.00\n", "2000-08-04, Close, 36.27\n", "2000-08-07, Close, 36.41\n", "2000-08-08, Close, 36.91\n", "2000-08-09, Close, 36.19\n", "2000-08-10, Close, 35.61\n", "2000-08-11, Close, 36.08\n", "2000-08-14, Close, 36.64\n", "2000-08-15, Close, 36.14\n", "2000-08-16, Close, 36.11\n", "2000-08-17, Close, 37.33\n", "2000-08-18, Close, 36.16\n", "2000-08-21, Close, 37.00\n", "2000-08-22, Close, 37.16\n", "2000-08-23, Close, 36.86\n", "2000-08-24, Close, 37.66\n", "2000-08-25, Close, 37.64\n", "2000-08-28, Close, 38.58\n", "2000-08-29, Close, 39.03\n", "2000-08-30, Close, 39.25\n", "2000-08-31, Close, 40.44\n", "2000-09-01, Close, 41.19\n", "2000-09-05, Close, 40.50\n", "2000-09-06, Close, 39.69\n", "2000-09-07, Close, 40.56\n", "2000-09-08, Close, 38.50\n", "2000-09-08, SELL CREATE, 38.50\n", "2000-09-11, SELL EXECUTED, Price: 38.28, Cost: 348.30, Comm 0.00\n", "2000-09-11, OPERATION PROFIT, GROSS 34.50, NET 34.50\n", "2000-09-11, Close, 37.11\n", "2000-09-12, Close, 35.30\n", "2000-09-13, Close, 36.39\n", "2000-09-14, Close, 37.78\n", "2000-09-15, Close, 34.83\n", "2000-09-18, Close, 34.01\n", "2000-09-19, Close, 35.27\n", "2000-09-20, Close, 35.55\n", "2000-09-21, Close, 35.11\n", "2000-09-22, Close, 35.91\n", "2000-09-25, Close, 35.02\n", "2000-09-26, Close, 35.33\n", "2000-09-27, Close, 35.52\n", "2000-09-28, Close, 36.24\n", "2000-09-28, BUY CREATE, 36.24\n", "2000-09-29, BUY EXECUTED, Price: 36.18, Cost: 361.80, Comm 0.00\n", "2000-09-29, Close, 35.02\n", "2000-09-29, SELL CREATE, 35.02\n", "2000-10-02, SELL EXECUTED, Price: 35.47, Cost: 361.80, Comm 0.00\n", "2000-10-02, OPERATION PROFIT, GROSS -7.10, NET -7.10\n", "2000-10-02, Close, 35.02\n", "2000-10-03, Close, 30.91\n", "2000-10-04, Close, 30.30\n", "2000-10-05, Close, 30.38\n", "2000-10-06, Close, 30.08\n", "2000-10-09, Close, 29.69\n", "2000-10-10, Close, 28.74\n", "2000-10-11, Close, 27.69\n", "2000-10-12, Close, 28.02\n", "2000-10-13, Close, 31.69\n", "2000-10-16, Close, 30.74\n", "2000-10-17, Close, 29.96\n", "2000-10-18, Close, 29.85\n", "2000-10-19, Close, 32.36\n", "2000-10-19, BUY CREATE, 32.36\n", "2000-10-20, BUY EXECUTED, Price: 32.13, Cost: 321.30, Comm 0.00\n", "2000-10-20, Close, 31.35\n", "2000-10-23, Close, 30.30\n", "2000-10-24, Close, 31.85\n", "2000-10-25, Close, 30.58\n", "2000-10-26, Close, 30.30\n", "2000-10-27, Close, 30.41\n", "2000-10-30, Close, 28.13\n", "2000-10-30, SELL CREATE, 28.13\n", "2000-10-31, SELL EXECUTED, Price: 29.02, Cost: 321.30, Comm 0.00\n", "2000-10-31, OPERATION PROFIT, GROSS -31.10, NET -31.10\n", "2000-10-31, Close, 29.35\n", "2000-11-01, Close, 27.91\n", "2000-11-02, Close, 26.30\n", "2000-11-03, Close, 26.96\n", "2000-11-06, Close, 24.85\n", "2000-11-07, Close, 23.63\n", "2000-11-08, Close, 22.07\n", "2000-11-09, Close, 24.18\n", "2000-11-10, Close, 22.63\n", "2000-11-13, Close, 22.01\n", "2000-11-14, Close, 25.24\n", "2000-11-15, Close, 25.68\n", "2000-11-16, Close, 24.35\n", "2000-11-17, Close, 25.63\n", "2000-11-17, BUY CREATE, 25.63\n", "2000-11-20, BUY EXECUTED, Price: 21.63, Cost: 216.30, Comm 0.00\n", "2000-11-20, Close, 22.01\n", "2000-11-20, SELL CREATE, 22.01\n", "2000-11-21, SELL EXECUTED, Price: 22.07, Cost: 216.30, Comm 0.00\n", "2000-11-21, OPERATION PROFIT, GROSS 4.40, NET 4.40\n", "2000-11-21, Close, 21.24\n", "2000-11-22, Close, 19.85\n", "2000-11-24, Close, 21.46\n", "2000-11-27, Close, 20.57\n", "2000-11-28, Close, 20.15\n", "2000-11-29, Close, 20.35\n", "2000-11-30, Close, 23.57\n", "2000-11-30, BUY CREATE, 23.57\n", "2000-12-01, BUY EXECUTED, Price: 23.46, Cost: 234.60, Comm 0.00\n", "2000-12-01, Close, 23.52\n", "2000-12-04, Close, 25.07\n", "2000-12-05, Close, 28.02\n", "2000-12-06, Close, 26.85\n", "2000-12-07, Close, 25.18\n", "2000-12-08, Close, 26.74\n", "2000-12-11, Close, 28.41\n", "2000-12-12, Close, 27.35\n", "2000-12-13, Close, 25.24\n", "2000-12-14, Close, 24.46\n", "2000-12-14, SELL CREATE, 24.46\n", "2000-12-15, SELL EXECUTED, Price: 26.18, Cost: 234.60, Comm 0.00\n", "2000-12-15, OPERATION PROFIT, GROSS 27.20, NET 27.20\n", "2000-12-15, Close, 25.41\n", "2000-12-15, BUY CREATE, 25.41\n", "2000-12-18, BUY EXECUTED, Price: 26.68, Cost: 266.80, Comm 0.00\n", "2000-12-18, Close, 28.46\n", "2000-12-19, Close, 27.24\n", "2000-12-20, Close, 25.35\n", "2000-12-20, SELL CREATE, 25.35\n", "2000-12-21, SELL EXECUTED, Price: 24.74, Cost: 266.80, Comm 0.00\n", "2000-12-21, OPERATION PROFIT, GROSS -19.40, NET -19.40\n", "2000-12-21, Close, 26.24\n", "2000-12-21, BUY CREATE, 26.24\n", "2000-12-22, BUY EXECUTED, Price: 27.02, Cost: 270.20, Comm 0.00\n", "2000-12-22, Close, 28.35\n", "2000-12-26, Close, 27.52\n", "2000-12-27, Close, 27.30\n", "2000-12-28, Close, 27.63\n", "2000-12-29, Close, 25.85\n", "2000-12-29, SELL CREATE, 25.85\n", "Final Portfolio Value: 982.30\n"]}, {"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 163\u001b[0m\n\u001b[0;32m    160\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFinal Portfolio Value: \u001b[39m\u001b[38;5;132;01m%.2f\u001b[39;00m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m%\u001b[39m cerebro\u001b[38;5;241m.\u001b[39mbroker\u001b[38;5;241m.\u001b[39mgetvalue())\n\u001b[0;32m    162\u001b[0m \u001b[38;5;66;03m# Plot the result\u001b[39;00m\n\u001b[1;32m--> 163\u001b[0m get_ipython()\u001b[38;5;241m.\u001b[39mrun_line_magic(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmatplotlib\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mwidget\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m    164\u001b[0m cerebro\u001b[38;5;241m.\u001b[39mplot(iplot\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON><PERSON><PERSON>\u001b[39;00m)\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:2480\u001b[0m, in \u001b[0;36mInteractiveShell.run_line_magic\u001b[1;34m(self, magic_name, line, _stack_depth)\u001b[0m\n\u001b[0;32m   2478\u001b[0m     kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlocal_ns\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_local_scope(stack_depth)\n\u001b[0;32m   2479\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbuiltin_trap:\n\u001b[1;32m-> 2480\u001b[0m     result \u001b[38;5;241m=\u001b[39m fn(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m   2482\u001b[0m \u001b[38;5;66;03m# The code below prevents the output from being displayed\u001b[39;00m\n\u001b[0;32m   2483\u001b[0m \u001b[38;5;66;03m# when using magics with decorator @output_can_be_silenced\u001b[39;00m\n\u001b[0;32m   2484\u001b[0m \u001b[38;5;66;03m# when the last Python token in the expression is a ';'.\u001b[39;00m\n\u001b[0;32m   2485\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(fn, magic\u001b[38;5;241m.\u001b[39mMAGIC_OUTPUT_CAN_BE_SILENCED, \u001b[38;5;28;01mFalse\u001b[39;00m):\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\IPython\\core\\magics\\pylab.py:103\u001b[0m, in \u001b[0;36mPylabMagics.matplotlib\u001b[1;34m(self, line)\u001b[0m\n\u001b[0;32m     98\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\n\u001b[0;32m     99\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAvailable matplotlib backends: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    100\u001b[0m         \u001b[38;5;241m%\u001b[39m _list_matplotlib_backends_and_gui_loops()\n\u001b[0;32m    101\u001b[0m     )\n\u001b[0;32m    102\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 103\u001b[0m     gui, backend \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mshell\u001b[38;5;241m.\u001b[39menable_matplotlib(args\u001b[38;5;241m.\u001b[39mgui\u001b[38;5;241m.\u001b[39mlower() \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(args\u001b[38;5;241m.\u001b[39mgui, \u001b[38;5;28mstr\u001b[39m) \u001b[38;5;28;01melse\u001b[39;00m args\u001b[38;5;241m.\u001b[39mgui)\n\u001b[0;32m    104\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_show_matplotlib_backend(args\u001b[38;5;241m.\u001b[39mgui, backend)\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3665\u001b[0m, in \u001b[0;36mInteractiveShell.enable_matplotlib\u001b[1;34m(self, gui)\u001b[0m\n\u001b[0;32m   3662\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmatplotlib_inline\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend_inline\u001b[39;00m\n\u001b[0;32m   3664\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mIPython\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m pylabtools \u001b[38;5;28;01mas\u001b[39;00m pt\n\u001b[1;32m-> 3665\u001b[0m gui, backend \u001b[38;5;241m=\u001b[39m pt\u001b[38;5;241m.\u001b[39mfind_gui_and_backend(gui, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpylab_gui_select)\n\u001b[0;32m   3667\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m gui \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m   3668\u001b[0m     \u001b[38;5;66;03m# If we have our first gui selection, store it\u001b[39;00m\n\u001b[0;32m   3669\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpylab_gui_select \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[1;32m~\\anaconda3\\envs\\backtest\\Lib\\site-packages\\IPython\\core\\pylabtools.py:338\u001b[0m, in \u001b[0;36mfind_gui_and_backend\u001b[1;34m(gui, gui_select)\u001b[0m\n\u001b[0;32m    321\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mfind_gui_and_backend\u001b[39m(gui\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, gui_select\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m    322\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Given a gui string return the gui and mpl backend.\u001b[39;00m\n\u001b[0;32m    323\u001b[0m \n\u001b[0;32m    324\u001b[0m \u001b[38;5;124;03m    Parameters\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    335\u001b[0m \u001b[38;5;124;03m    'WXAgg','Qt4Agg','module://matplotlib_inline.backend_inline','agg').\u001b[39;00m\n\u001b[0;32m    336\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 338\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmatplotlib\u001b[39;00m\n\u001b[0;32m    340\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m _matplotlib_manages_backends():\n\u001b[0;32m    341\u001b[0m         backend_registry \u001b[38;5;241m=\u001b[39m matplotlib\u001b[38;5;241m.\u001b[39mbackends\u001b[38;5;241m.\u001b[39mregistry\u001b[38;5;241m.\u001b[39mbackend_registry\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'matplotlib'"]}], "source": ["from __future__ import (absolute_import, division, print_function,\n", "                        unicode_literals)\n", "\n", "import datetime  # For datetime objects\n", "import os.path  # To manage paths\n", "import sys  # To find out the script name (in argv[0])\n", "\n", "# Import the backtrader platform\n", "import backtrader as bt\n", "\n", "\n", "# Create a Stratey\n", "class TestStrategy(bt.Strategy):\n", "    params = (\n", "        ('maperi<PERSON>', 15),\n", "    )\n", "\n", "    def log(self, txt, dt=None):\n", "        ''' Logging function fot this strategy'''\n", "        dt = dt or self.datas[0].datetime.date(0)\n", "        print('%s, %s' % (dt.isoformat(), txt))\n", "\n", "    def __init__(self):\n", "        # Keep a reference to the \"close\" line in the data[0] dataseries\n", "        self.dataclose = self.datas[0].close\n", "\n", "        # To keep track of pending orders and buy price/commission\n", "        self.order = None\n", "        self.buyprice = None\n", "        self.buycomm = None\n", "\n", "        # Add a MovingAverageSimple indicator\n", "        self.sma = bt.indicators.SimpleMovingAverage(\n", "            self.datas[0], period=self.params.maperiod)\n", "\n", "        # Indicators for the plotting show\n", "        bt.indicators.ExponentialMovingAverage(self.datas[0], period=25)\n", "        bt.indicators.WeightedMovingAverage(self.datas[0], period=25,\n", "                                            subplot=True)\n", "        bt.indicators.StochasticSlow(self.datas[0])\n", "        bt.indicators.MACDHisto(self.datas[0])\n", "        rsi = bt.indicators.RSI(self.datas[0])\n", "        bt.indicators.SmoothedMovingAverage(rsi, period=10)\n", "        bt.indicators.ATR(self.datas[0], plot=False)\n", "\n", "    def notify_order(self, order):\n", "        if order.status in [order.Submitted, order.Accepted]:\n", "            # Buy/Sell order submitted/accepted to/by broker - Nothing to do\n", "            return\n", "\n", "        # Check if an order has been completed\n", "        # Attention: broker could reject order if not enough cash\n", "        if order.status in [order.Completed]:\n", "            if order.isbuy():\n", "                self.log(\n", "                    'BUY EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %\n", "                    (order.executed.price,\n", "                     order.executed.value,\n", "                     order.executed.comm))\n", "\n", "                self.buyprice = order.executed.price\n", "                self.buycomm = order.executed.comm\n", "            else:  # Sell\n", "                self.log('SELL EXECUTED, Price: %.2f, Cost: %.2f, Comm %.2f' %\n", "                         (order.executed.price,\n", "                          order.executed.value,\n", "                          order.executed.comm))\n", "\n", "            self.bar_executed = len(self)\n", "\n", "        elif order.status in [order.Canceled, order.Margin, order.Rejected]:\n", "            self.log('Order Canceled/Margin/Rejected')\n", "\n", "        # Write down: no pending order\n", "        self.order = None\n", "\n", "    def notify_trade(self, trade):\n", "        if not trade.isclosed:\n", "            return\n", "\n", "        self.log('OPERATION PROFIT, GROSS %.2f, NET %.2f' %\n", "                 (trade.pnl, trade.pnlcomm))\n", "\n", "    def next(self):\n", "        # Simply log the closing price of the series from the reference\n", "        self.log('Close, %.2f' % self.dataclose[0])\n", "\n", "        # Check if an order is pending ... if yes, we cannot send a 2nd one\n", "        if self.order:\n", "            return\n", "\n", "        # Check if we are in the market\n", "        if not self.position:\n", "\n", "            # Not yet ... we MIGHT BUY if ...\n", "            if self.dataclose[0] > self.sma[0]:\n", "\n", "                # BUY, BUY, BUY!!! (with all possible default parameters)\n", "                self.log('BUY CREATE, %.2f' % self.dataclose[0])\n", "\n", "                # Keep track of the created order to avoid a 2nd order\n", "                self.order = self.buy()\n", "\n", "        else:\n", "\n", "            if self.dataclose[0] < self.sma[0]:\n", "                # SELL, SELL, SELL!!! (with all possible default parameters)\n", "                self.log('SELL CREATE, %.2f' % self.dataclose[0])\n", "\n", "                # Keep track of the created order to avoid a 2nd order\n", "                self.order = self.sell()\n", "\n", "\n", "if __name__ == '__main__':\n", "    # Create a cerebro entity\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()\n", "\n", "    # Add a strategy\n", "    cerebro.addstrategy(TestStrategy)\n", "\n", "    # Datas are in a subfolder of the samples. Need to find where the script is\n", "    # because it could have been called from anywhere\n", "    try:\n", "        # Try to get the directory of the script\n", "        script_directory = os.path.dirname(os.path.abspath(__file__))\n", "    except NameError:\n", "        # Fallback to the current working directory if __file__ is not defined\n", "        script_directory = os.getcwd()\n", "    datapath = os.path.join(script_directory, 'backtrader\\\\datas\\\\orcl-1995-2014.txt')\n", "\n", "    # Create a Data Feed\n", "    data = bt.feeds.YahooFinanceCSVData(\n", "        dataname=datapath,\n", "        # Do not pass values before this date\n", "        fromdate=datetime.datetime(2000, 1, 1),\n", "        # Do not pass values before this date\n", "        todate=datetime.datetime(2000, 12, 31),\n", "        # Do not pass values after this date\n", "        reverse=False)\n", "\n", "    # Add the Data Feed to Cerebro\n", "    cerebro.adddata(data)\n", "\n", "    # Set our desired cash start\n", "    cerebro.broker.setcash(1000.0)\n", "\n", "    # Add a FixedSize sizer according to the stake\n", "    cerebro.addsizer(bt.sizers.FixedSize, stake=10)\n", "\n", "    # Set the commission\n", "    cerebro.broker.setcommission(commission=0.0)\n", "\n", "    # Print out the starting conditions\n", "    print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())\n", "\n", "    # Run over everything\n", "    cerebro.run()\n", "\n", "    # Print out the final result\n", "    print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())\n", "\n", "    # Plot the result\n", "    #%matplotlib widget\n", "    cerebro.plot(iplot=False)"]}, {"cell_type": "code", "execution_count": null, "id": "25bf1a39-7d91-46fc-86df-d5e6d71c0fe7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}