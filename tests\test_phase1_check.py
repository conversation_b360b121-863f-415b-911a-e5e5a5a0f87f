#!/usr/bin/env python
import datetime
from backtrader.position import Position, TaxLot

def test_phase1_implementation():
    print("=== Phase 1 Tax Lot Implementation Check ===")
    
    # Test 1: Position has tax lot attributes
    p = Position()
    print(f"1. Position has taxlots attribute: {hasattr(p, 'taxlots')}")
    print(f"2. Tax lot methods available: {[m for m in dir(p) if 'tax' in m.lower()]}")
    
    # Test 2: Tax lot creation
    dt = datetime.datetime.now()
    try:
        p.update(100, 10.5, dt)
        print(f"3. Tax lots after buy (100 @ 10.5): {len(p.taxlots)}")
        if p.taxlots:
            print(f"   First lot: {p.taxlots[0]}")
    except Exception as e:
        print(f"3. Error during buy: {e}")
    
    # Test 3: Tax lot sell
    try:
        p.update(-50, 11.0, dt)
        print(f"4. Tax lots after sell (-50 @ 11.0): {len(p.taxlots)}")
        if p.taxlots:
            print(f"   Remaining lot: {p.taxlots[0]}")
    except Exception as e:
        print(f"4. Error during sell: {e}")
    
    # Test 4: TaxLot class
    try:
        lot = TaxLot(100, 10.5, dt)
        print(f"5. TaxLot creation: {lot}")
        lot.reduce_qty(30)
        print(f"   After reducing 30: {lot}")
    except Exception as e:
        print(f"5. Error with TaxLot: {e}")

if __name__ == "__main__":
    test_phase1_implementation() 