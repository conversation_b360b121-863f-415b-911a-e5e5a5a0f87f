{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7f4d88e5-3d33-419a-a992-757b0ac4933b", "metadata": {}, "outputs": [], "source": ["import backtrader as bt\n", "import pandas as pd\n", "import yfinance as yf"]}, {"cell_type": "code", "execution_count": 2, "id": "43f874e4-521e-40d5-8375-34321d1701ac", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n"]}], "source": ["tsla_df = yf.download('TSLA', group_by='column', auto_adjust=False)"]}, {"cell_type": "code", "execution_count": 3, "id": "eae7e3ff-a27e-4442-b262-a5ff05b1cd65", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>TSLA</th>\n", "      <th>TSLA</th>\n", "      <th>TSLA</th>\n", "      <th>TSLA</th>\n", "      <th>TSLA</th>\n", "      <th>TSLA</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2010-06-29</th>\n", "      <td>1.592667</td>\n", "      <td>1.592667</td>\n", "      <td>1.666667</td>\n", "      <td>1.169333</td>\n", "      <td>1.266667</td>\n", "      <td>281494500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-06-30</th>\n", "      <td>1.588667</td>\n", "      <td>1.588667</td>\n", "      <td>2.028000</td>\n", "      <td>1.553333</td>\n", "      <td>1.719333</td>\n", "      <td>257806500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-01</th>\n", "      <td>1.464000</td>\n", "      <td>1.464000</td>\n", "      <td>1.728000</td>\n", "      <td>1.351333</td>\n", "      <td>1.666667</td>\n", "      <td>123282000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-02</th>\n", "      <td>1.280000</td>\n", "      <td>1.280000</td>\n", "      <td>1.540000</td>\n", "      <td>1.247333</td>\n", "      <td>1.533333</td>\n", "      <td>77097000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-06</th>\n", "      <td>1.074000</td>\n", "      <td>1.074000</td>\n", "      <td>1.333333</td>\n", "      <td>1.055333</td>\n", "      <td>1.333333</td>\n", "      <td>103003500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-23</th>\n", "      <td>348.679993</td>\n", "      <td>348.679993</td>\n", "      <td>357.540009</td>\n", "      <td>327.480011</td>\n", "      <td>327.540009</td>\n", "      <td>190716800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-24</th>\n", "      <td>340.470001</td>\n", "      <td>340.470001</td>\n", "      <td>356.260010</td>\n", "      <td>340.440002</td>\n", "      <td>356.170013</td>\n", "      <td>114736200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-25</th>\n", "      <td>327.549988</td>\n", "      <td>327.549988</td>\n", "      <td>343.000000</td>\n", "      <td>320.399994</td>\n", "      <td>342.700012</td>\n", "      <td>119845100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-26</th>\n", "      <td>325.779999</td>\n", "      <td>325.779999</td>\n", "      <td>331.049988</td>\n", "      <td>323.609985</td>\n", "      <td>324.609985</td>\n", "      <td>80440900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27</th>\n", "      <td>323.630005</td>\n", "      <td>323.630005</td>\n", "      <td>329.339996</td>\n", "      <td>317.500000</td>\n", "      <td>324.510010</td>\n", "      <td>88826400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3773 rows × 6 columns</p>\n", "</div>"], "text/plain": ["Price        Adj Close       Close        High         Low        Open  \\\n", "Ticker            TSLA        TSLA        TSLA        TSLA        TSLA   \n", "Date                                                                     \n", "2010-06-29    1.592667    1.592667    1.666667    1.169333    1.266667   \n", "2010-06-30    1.588667    1.588667    2.028000    1.553333    1.719333   \n", "2010-07-01    1.464000    1.464000    1.728000    1.351333    1.666667   \n", "2010-07-02    1.280000    1.280000    1.540000    1.247333    1.533333   \n", "2010-07-06    1.074000    1.074000    1.333333    1.055333    1.333333   \n", "...                ...         ...         ...         ...         ...   \n", "2025-06-23  348.679993  348.679993  357.540009  327.480011  327.540009   \n", "2025-06-24  340.470001  340.470001  356.260010  340.440002  356.170013   \n", "2025-06-25  327.549988  327.549988  343.000000  320.399994  342.700012   \n", "2025-06-26  325.779999  325.779999  331.049988  323.609985  324.609985   \n", "2025-06-27  323.630005  323.630005  329.339996  317.500000  324.510010   \n", "\n", "Price          Volume  \n", "Ticker           TSLA  \n", "Date                   \n", "2010-06-29  281494500  \n", "2010-06-30  257806500  \n", "2010-07-01  123282000  \n", "2010-07-02   77097000  \n", "2010-07-06  103003500  \n", "...               ...  \n", "2025-06-23  190716800  \n", "2025-06-24  114736200  \n", "2025-06-25  119845100  \n", "2025-06-26   80440900  \n", "2025-06-27   88826400  \n", "\n", "[3773 rows x 6 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["tsla_df"]}, {"cell_type": "code", "execution_count": 4, "id": "f2b3c186", "metadata": {}, "outputs": [], "source": ["# If columns are multi-index, flatten them\n", "if isinstance(tsla_df.columns, pd.MultiIndex):\n", "    tsla_df.columns = ['_'.join(col).strip() for col in tsla_df.columns.values]"]}, {"cell_type": "code", "execution_count": 5, "id": "48590e34", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Adj Close_TSLA</th>\n", "      <th>Close_TSLA</th>\n", "      <th>High_TSLA</th>\n", "      <th>Low_TSLA</th>\n", "      <th>Open_TSLA</th>\n", "      <th>Volume_TSLA</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2010-06-29</th>\n", "      <td>1.592667</td>\n", "      <td>1.592667</td>\n", "      <td>1.666667</td>\n", "      <td>1.169333</td>\n", "      <td>1.266667</td>\n", "      <td>281494500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-06-30</th>\n", "      <td>1.588667</td>\n", "      <td>1.588667</td>\n", "      <td>2.028000</td>\n", "      <td>1.553333</td>\n", "      <td>1.719333</td>\n", "      <td>257806500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-01</th>\n", "      <td>1.464000</td>\n", "      <td>1.464000</td>\n", "      <td>1.728000</td>\n", "      <td>1.351333</td>\n", "      <td>1.666667</td>\n", "      <td>123282000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-02</th>\n", "      <td>1.280000</td>\n", "      <td>1.280000</td>\n", "      <td>1.540000</td>\n", "      <td>1.247333</td>\n", "      <td>1.533333</td>\n", "      <td>77097000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-06</th>\n", "      <td>1.074000</td>\n", "      <td>1.074000</td>\n", "      <td>1.333333</td>\n", "      <td>1.055333</td>\n", "      <td>1.333333</td>\n", "      <td>103003500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-23</th>\n", "      <td>348.679993</td>\n", "      <td>348.679993</td>\n", "      <td>357.540009</td>\n", "      <td>327.480011</td>\n", "      <td>327.540009</td>\n", "      <td>190716800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-24</th>\n", "      <td>340.470001</td>\n", "      <td>340.470001</td>\n", "      <td>356.260010</td>\n", "      <td>340.440002</td>\n", "      <td>356.170013</td>\n", "      <td>114736200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-25</th>\n", "      <td>327.549988</td>\n", "      <td>327.549988</td>\n", "      <td>343.000000</td>\n", "      <td>320.399994</td>\n", "      <td>342.700012</td>\n", "      <td>119845100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-26</th>\n", "      <td>325.779999</td>\n", "      <td>325.779999</td>\n", "      <td>331.049988</td>\n", "      <td>323.609985</td>\n", "      <td>324.609985</td>\n", "      <td>80440900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27</th>\n", "      <td>323.630005</td>\n", "      <td>323.630005</td>\n", "      <td>329.339996</td>\n", "      <td>317.500000</td>\n", "      <td>324.510010</td>\n", "      <td>88826400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3773 rows × 6 columns</p>\n", "</div>"], "text/plain": ["            Adj Close_TSLA  Close_TSLA   High_TSLA    Low_TSLA   Open_TSLA  \\\n", "Date                                                                         \n", "2010-06-29        1.592667    1.592667    1.666667    1.169333    1.266667   \n", "2010-06-30        1.588667    1.588667    2.028000    1.553333    1.719333   \n", "2010-07-01        1.464000    1.464000    1.728000    1.351333    1.666667   \n", "2010-07-02        1.280000    1.280000    1.540000    1.247333    1.533333   \n", "2010-07-06        1.074000    1.074000    1.333333    1.055333    1.333333   \n", "...                    ...         ...         ...         ...         ...   \n", "2025-06-23      348.679993  348.679993  357.540009  327.480011  327.540009   \n", "2025-06-24      340.470001  340.470001  356.260010  340.440002  356.170013   \n", "2025-06-25      327.549988  327.549988  343.000000  320.399994  342.700012   \n", "2025-06-26      325.779999  325.779999  331.049988  323.609985  324.609985   \n", "2025-06-27      323.630005  323.630005  329.339996  317.500000  324.510010   \n", "\n", "            Volume_TSLA  \n", "Date                     \n", "2010-06-29    281494500  \n", "2010-06-30    257806500  \n", "2010-07-01    123282000  \n", "2010-07-02     77097000  \n", "2010-07-06    103003500  \n", "...                 ...  \n", "2025-06-23    190716800  \n", "2025-06-24    114736200  \n", "2025-06-25    119845100  \n", "2025-06-26     80440900  \n", "2025-06-27     88826400  \n", "\n", "[3773 rows x 6 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["tsla_df"]}, {"cell_type": "code", "execution_count": 6, "id": "66fe8a42-541b-45a2-bf7f-825260328711", "metadata": {}, "outputs": [], "source": ["tsla_df_parsed = bt.feeds.PandasData(dataname=tsla_df, datetime=None, open=4, high=2, low=3, close=0, volume=5, openinterest=-1)"]}, {"cell_type": "code", "execution_count": 7, "id": "d9f56e83-759e-4667-876e-6aaa87bb0c3e", "metadata": {}, "outputs": [], "source": ["tsla_df.to_csv('tsla.csv')"]}, {"cell_type": "code", "execution_count": 8, "id": "d50d7992-aa11-40e7-b27c-00781bf3a38f", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('tsla.csv')"]}, {"cell_type": "code", "execution_count": 9, "id": "c96ed5ff-bac2-4c8a-8a5a-ad98d061ac92", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Adj Close_TSLA</th>\n", "      <th>Close_TSLA</th>\n", "      <th>High_TSLA</th>\n", "      <th>Low_TSLA</th>\n", "      <th>Open_TSLA</th>\n", "      <th>Volume_TSLA</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2010-06-29</td>\n", "      <td>1.592667</td>\n", "      <td>1.592667</td>\n", "      <td>1.666667</td>\n", "      <td>1.169333</td>\n", "      <td>1.266667</td>\n", "      <td>281494500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2010-06-30</td>\n", "      <td>1.588667</td>\n", "      <td>1.588667</td>\n", "      <td>2.028000</td>\n", "      <td>1.553333</td>\n", "      <td>1.719333</td>\n", "      <td>257806500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2010-07-01</td>\n", "      <td>1.464000</td>\n", "      <td>1.464000</td>\n", "      <td>1.728000</td>\n", "      <td>1.351333</td>\n", "      <td>1.666667</td>\n", "      <td>123282000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2010-07-02</td>\n", "      <td>1.280000</td>\n", "      <td>1.280000</td>\n", "      <td>1.540000</td>\n", "      <td>1.247333</td>\n", "      <td>1.533333</td>\n", "      <td>77097000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2010-07-06</td>\n", "      <td>1.074000</td>\n", "      <td>1.074000</td>\n", "      <td>1.333333</td>\n", "      <td>1.055333</td>\n", "      <td>1.333333</td>\n", "      <td>103003500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3768</th>\n", "      <td>2025-06-23</td>\n", "      <td>348.679993</td>\n", "      <td>348.679993</td>\n", "      <td>357.540009</td>\n", "      <td>327.480011</td>\n", "      <td>327.540009</td>\n", "      <td>190716800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3769</th>\n", "      <td>2025-06-24</td>\n", "      <td>340.470001</td>\n", "      <td>340.470001</td>\n", "      <td>356.260010</td>\n", "      <td>340.440002</td>\n", "      <td>356.170013</td>\n", "      <td>114736200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3770</th>\n", "      <td>2025-06-25</td>\n", "      <td>327.549988</td>\n", "      <td>327.549988</td>\n", "      <td>343.000000</td>\n", "      <td>320.399994</td>\n", "      <td>342.700012</td>\n", "      <td>119845100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3771</th>\n", "      <td>2025-06-26</td>\n", "      <td>325.779999</td>\n", "      <td>325.779999</td>\n", "      <td>331.049988</td>\n", "      <td>323.609985</td>\n", "      <td>324.609985</td>\n", "      <td>80440900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3772</th>\n", "      <td>2025-06-27</td>\n", "      <td>323.630005</td>\n", "      <td>323.630005</td>\n", "      <td>329.339996</td>\n", "      <td>317.500000</td>\n", "      <td>324.510010</td>\n", "      <td>88826400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3773 rows × 7 columns</p>\n", "</div>"], "text/plain": ["            Date  Adj Close_TSLA  Close_TSLA   High_TSLA    Low_TSLA  \\\n", "0     2010-06-29        1.592667    1.592667    1.666667    1.169333   \n", "1     2010-06-30        1.588667    1.588667    2.028000    1.553333   \n", "2     2010-07-01        1.464000    1.464000    1.728000    1.351333   \n", "3     2010-07-02        1.280000    1.280000    1.540000    1.247333   \n", "4     2010-07-06        1.074000    1.074000    1.333333    1.055333   \n", "...          ...             ...         ...         ...         ...   \n", "3768  2025-06-23      348.679993  348.679993  357.540009  327.480011   \n", "3769  2025-06-24      340.470001  340.470001  356.260010  340.440002   \n", "3770  2025-06-25      327.549988  327.549988  343.000000  320.399994   \n", "3771  2025-06-26      325.779999  325.779999  331.049988  323.609985   \n", "3772  2025-06-27      323.630005  323.630005  329.339996  317.500000   \n", "\n", "       Open_TSLA  Volume_TSLA  \n", "0       1.266667    281494500  \n", "1       1.719333    257806500  \n", "2       1.666667    123282000  \n", "3       1.533333     77097000  \n", "4       1.333333    103003500  \n", "...          ...          ...  \n", "3768  327.540009    190716800  \n", "3769  356.170013    114736200  \n", "3770  342.700012    119845100  \n", "3771  324.609985     80440900  \n", "3772  324.510010     88826400  \n", "\n", "[3773 rows x 7 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 10, "id": "f25842c2-ca2a-42e8-b936-49643c7395a3", "metadata": {}, "outputs": [], "source": ["tsla_csv_parsed = bt.feeds.GenericCSVData(\n", "    dataname='tsla.csv',\n", "    datetime=0, open=1, high=2, low=3, close=5, volume=6, openinterest=-1,\n", "    dtformat='%Y-%m-%d',\n", "    skiprows=2\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "301e7e7e-9799-4a54-9bb2-bdef47126322", "metadata": {}, "outputs": [{"data": {"text/plain": ["<backtrader.feeds.csvgeneric.GenericCSVData at 0x20995d5ead0>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro = bt.<PERSON><PERSON><PERSON>()\n", "cerebro.adddata(tsla_csv_parsed)"]}, {"cell_type": "code", "execution_count": 12, "id": "cc334bd4", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<backtrader.strategy.Strategy at 0x20995d38cd0>]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro.run()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "138a2109", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[[<Figure size 640x480 with 4 Axes>]]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib inline\n", "cerebro.plot(iplot=False)"]}, {"cell_type": "code", "execution_count": null, "id": "aad00dee-7554-48a5-90f5-8ee19c07611d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}