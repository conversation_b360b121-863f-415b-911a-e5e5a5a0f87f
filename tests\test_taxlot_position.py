import unittest
import datetime
from backtrader.position import Position, TaxLot

class TestTaxLot(unittest.TestCase):
    def test_creation_and_str(self):
        dt = datetime.datetime(2021, 1, 1)
        lot = TaxLot(100, 10.5, dt)
        self.assertEqual(lot.qty, 100)
        self.assertEqual(lot.price, 10.5)
        self.assertEqual(lot.datetime, dt)
        self.assertEqual(lot.remaining_qty, 100)
        self.assertIn('TaxLot(qty=100, price=10.5', str(lot))

    def test_reduce_qty(self):
        lot = TaxLot(100, 10, datetime.datetime.now())
        lot.reduce_qty(30)
        self.assertEqual(lot.remaining_qty, 70)
        
        # Test reducing to zero
        lot.reduce_qty(70)
        self.assertEqual(lot.remaining_qty, 0)
        
        # Test error when reducing beyond remaining
        with self.assertRaises(ValueError):
            lot.reduce_qty(1)

    def test_reduce_qty_edge_cases(self):
        lot = TaxLot(100, 10, datetime.datetime.now())
        
        # Test reducing by zero (should be allowed)
        lot.reduce_qty(0)
        self.assertEqual(lot.remaining_qty, 100)
        
        # Test reducing exact remaining amount
        lot.reduce_qty(100)
        self.assertEqual(lot.remaining_qty, 0)

class TestPositionTaxLotBasics(unittest.TestCase):
    def test_add_and_get_taxlots(self):
        pos = Position()
        dt = datetime.datetime(2022, 2, 2)
        lot = pos.add_taxlot(50, 20.0, dt)
        self.assertEqual(len(pos.get_taxlots()), 1)
        self.assertIs(pos.get_taxlots()[0], lot)
        self.assertIn('Tax Lots:', str(pos))

    def test_multiple_tax_lots(self):
        pos = Position()
        dt1 = datetime.datetime(2022, 1, 1)
        dt2 = datetime.datetime(2022, 1, 2)
        dt3 = datetime.datetime(2022, 1, 3)
        
        # Add multiple lots
        lot1 = pos.add_taxlot(100, 10.0, dt1)
        lot2 = pos.add_taxlot(50, 11.0, dt2)
        lot3 = pos.add_taxlot(75, 12.0, dt3)
        
        lots = pos.get_taxlots()
        self.assertEqual(len(lots), 3)
        self.assertEqual(lots[0].price, 10.0)
        self.assertEqual(lots[1].price, 11.0)
        self.assertEqual(lots[2].price, 12.0)

class TestPositionUpdateIntegration(unittest.TestCase):
    def test_single_buy_creates_lot(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        pos.update(100, 15.0, dt)
        
        self.assertEqual(len(pos.taxlots), 1)
        self.assertEqual(pos.taxlots[0].qty, 100)
        self.assertEqual(pos.taxlots[0].price, 15.0)
        self.assertEqual(pos.taxlots[0].remaining_qty, 100)

    def test_multiple_buys_create_multiple_lots(self):
        pos = Position()
        dt1 = datetime.datetime(2022, 1, 1)
        dt2 = datetime.datetime(2022, 1, 2)
        
        pos.update(100, 15.0, dt1)
        pos.update(50, 16.0, dt2)
        
        self.assertEqual(len(pos.taxlots), 2)
        self.assertEqual(pos.taxlots[0].qty, 100)
        self.assertEqual(pos.taxlots[0].price, 15.0)
        self.assertEqual(pos.taxlots[1].qty, 50)
        self.assertEqual(pos.taxlots[1].price, 16.0)
        self.assertEqual(pos.size, 150)

    def test_partial_sell_reduces_first_lot_fifo(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Buy 100 shares at $15
        pos.update(100, 15.0, dt)
        # Sell 30 shares
        pos.update(-30, 16.0, dt)
        
        self.assertEqual(len(pos.taxlots), 1)
        self.assertEqual(pos.taxlots[0].remaining_qty, 70)
        self.assertEqual(pos.size, 70)

    def test_full_lot_sell_removes_lot(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Buy 100 shares
        pos.update(100, 15.0, dt)
        # Sell all 100 shares
        pos.update(-100, 16.0, dt)
        
        self.assertEqual(len(pos.taxlots), 0)
        self.assertEqual(pos.size, 0)

    def test_sell_across_multiple_lots_fifo(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Buy in multiple lots
        pos.update(100, 15.0, dt)  # Lot 1: 100 @ $15
        pos.update(50, 16.0, dt)   # Lot 2: 50 @ $16
        pos.update(75, 17.0, dt)   # Lot 3: 75 @ $17
        
        # Sell 130 shares (should close lot 1 completely and partially close lot 2)
        pos.update(-130, 18.0, dt)
        
        self.assertEqual(len(pos.taxlots), 2)  # Lot 1 removed, lots 2 & 3 remain
        self.assertEqual(pos.taxlots[0].price, 16.0)  # First remaining lot is lot 2
        self.assertEqual(pos.taxlots[0].remaining_qty, 20)  # 50 - 30 = 20
        self.assertEqual(pos.taxlots[1].price, 17.0)  # Lot 3 untouched
        self.assertEqual(pos.taxlots[1].remaining_qty, 75)
        self.assertEqual(pos.size, 95)  # 225 - 130 = 95

class TestPositionReversalScenarios(unittest.TestCase):
    def test_position_reversal_long_to_short(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Buy 100 shares
        pos.update(100, 15.0, dt)
        self.assertEqual(len(pos.taxlots), 1)
        
        # Sell 150 shares (reversal to short 50)
        pos.update(-150, 16.0, dt)
        
        # All long lots should be closed
        self.assertEqual(len(pos.taxlots), 0)
        self.assertEqual(pos.size, -50)

    def test_position_reversal_short_to_long(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Short 100 shares
        pos.update(-100, 15.0, dt)
        self.assertEqual(pos.size, -100)
        # Note: Short positions don't create tax lots in current implementation
        
        # Buy 150 shares (reversal to long 50)
        pos.update(150, 16.0, dt)
        
        self.assertEqual(pos.size, 50)
        # Should create a new lot for the net long position
        self.assertEqual(len(pos.taxlots), 1)
        self.assertEqual(pos.taxlots[0].qty, 50)

class TestEdgeCases(unittest.TestCase):
    def test_zero_quantity_transactions(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Zero quantity update should not create lots
        pos.update(0, 15.0, dt)
        self.assertEqual(len(pos.taxlots), 0)
        self.assertEqual(pos.size, 0)

    def test_position_goes_to_zero_clears_lots(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Build position
        pos.update(100, 15.0, dt)
        pos.update(50, 16.0, dt)
        self.assertEqual(len(pos.taxlots), 2)
        
        # Sell everything
        pos.update(-150, 17.0, dt)
        
        self.assertEqual(len(pos.taxlots), 0)
        self.assertEqual(pos.size, 0)

    def test_large_quantities(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Test with large numbers
        pos.update(1000000, 100.0, dt)
        pos.update(-500000, 105.0, dt)
        
        self.assertEqual(len(pos.taxlots), 1)
        self.assertEqual(pos.taxlots[0].remaining_qty, 500000)
        self.assertEqual(pos.size, 500000)

    def test_fractional_prices(self):
        pos = Position()
        dt = datetime.datetime.now()
        
        # Test with fractional prices
        pos.update(100, 15.375, dt)
        pos.update(50, 16.125, dt)
        
        self.assertEqual(len(pos.taxlots), 2)
        self.assertEqual(pos.taxlots[0].price, 15.375)
        self.assertEqual(pos.taxlots[1].price, 16.125)

class TestComplexTradingScenarios(unittest.TestCase):
    def test_day_trading_scenario(self):
        """Test multiple buys and sells in same day"""
        pos = Position()
        dt = datetime.datetime.now()
        
        # Morning: Buy 100 @ $50
        pos.update(100, 50.0, dt)
        self.assertEqual(len(pos.taxlots), 1)
        
        # Mid-day: Sell 50 @ $52
        pos.update(-50, 52.0, dt)
        self.assertEqual(len(pos.taxlots), 1)
        self.assertEqual(pos.taxlots[0].remaining_qty, 50)
        
        # Afternoon: Buy 75 @ $51
        pos.update(75, 51.0, dt)
        self.assertEqual(len(pos.taxlots), 2)
        
        # End of day: Sell all 125 @ $53
        pos.update(-125, 53.0, dt)
        self.assertEqual(len(pos.taxlots), 0)
        self.assertEqual(pos.size, 0)

    def test_averaging_down_scenario(self):
        """Test buying more as price falls"""
        pos = Position()
        dt = datetime.datetime.now()
        
        # Initial buy
        pos.update(100, 100.0, dt)
        
        # Price drops, buy more
        pos.update(100, 90.0, dt)
        pos.update(100, 80.0, dt)
        
        self.assertEqual(len(pos.taxlots), 3)
        self.assertEqual(pos.size, 300)
        
        # Sell some shares (should sell from first lot first - FIFO)
        pos.update(-150, 85.0, dt)
        
        self.assertEqual(len(pos.taxlots), 2)
        self.assertEqual(pos.taxlots[0].price, 90.0)  # Second lot now first
        self.assertEqual(pos.taxlots[0].remaining_qty, 50)  # Partially sold
        self.assertEqual(pos.taxlots[1].price, 80.0)  # Third lot intact
        self.assertEqual(pos.taxlots[1].remaining_qty, 100)

    def test_scaling_out_scenario(self):
        """Test gradual position reduction"""
        pos = Position()
        dt = datetime.datetime.now()
        
        # Build large position
        pos.update(1000, 50.0, dt)
        
        # Scale out gradually
        pos.update(-100, 55.0, dt)  # Sell 10%
        pos.update(-200, 60.0, dt)  # Sell 20% more
        pos.update(-300, 65.0, dt)  # Sell 30% more
        
        self.assertEqual(len(pos.taxlots), 1)
        self.assertEqual(pos.taxlots[0].remaining_qty, 400)  # 60% remaining
        self.assertEqual(pos.size, 400)

class TestFIFOLogic(unittest.TestCase):
    def test_fifo_order_maintained(self):
        """Test that FIFO order is strictly maintained"""
        pos = Position()
        dt_base = datetime.datetime(2022, 1, 1)
        
        # Create lots with different dates and prices
        pos.update(100, 10.0, dt_base)  # Lot 1: oldest
        pos.update(100, 20.0, dt_base + datetime.timedelta(days=1))  # Lot 2
        pos.update(100, 30.0, dt_base + datetime.timedelta(days=2))  # Lot 3: newest
        
        # Sell 150 shares - should close lot 1 and partially close lot 2
        pos.update(-150, 25.0, dt_base + datetime.timedelta(days=3))
        
        self.assertEqual(len(pos.taxlots), 2)
        # First remaining lot should be the partially closed lot 2
        self.assertEqual(pos.taxlots[0].price, 20.0)
        self.assertEqual(pos.taxlots[0].remaining_qty, 50)
        # Second remaining lot should be untouched lot 3
        self.assertEqual(pos.taxlots[1].price, 30.0)
        self.assertEqual(pos.taxlots[1].remaining_qty, 100)

    def test_reduce_taxlots_fifo_return_value(self):
        """Test the return value of reduce_taxlots_fifo method"""
        pos = Position()
        dt = datetime.datetime.now()
        
        pos.add_taxlot(100, 10.0, dt)  # Use add_taxlot directly to avoid update() logic
        pos.add_taxlot(50, 15.0, dt)
        
        # Test the direct method call - reducing 75 should:
        # - Close first lot completely (100)
        # - Not touch second lot since 75 < 100
        closed_lots = pos.reduce_taxlots_fifo(75)
        
        self.assertEqual(len(closed_lots), 1)  # Only first lot affected
        # First lot partially closed (75 out of 100)
        self.assertEqual(closed_lots[0][1], 75)  # Quantity closed
        
        # Verify remaining state
        self.assertEqual(len(pos.taxlots), 2)  # Both lots still exist
        self.assertEqual(pos.taxlots[0].remaining_qty, 25)  # First lot reduced
        self.assertEqual(pos.taxlots[1].remaining_qty, 50)  # Second lot untouched

class TestErrorConditions(unittest.TestCase):
    def test_reduce_more_than_available(self):
        """Test behavior when trying to reduce more than available"""
        pos = Position()
        dt = datetime.datetime.now()
        
        pos.update(100, 10.0, dt)
        
        # This should not raise an error but should reduce all available
        closed_lots = pos.reduce_taxlots_fifo(150)
        
        self.assertEqual(len(closed_lots), 1)
        self.assertEqual(closed_lots[0][1], 100)  # Only 100 was available
        self.assertEqual(len(pos.taxlots), 0)  # All lots closed

    def test_negative_quantities(self):
        """Test handling of negative quantities in tax lot methods"""
        lot = TaxLot(100, 10.0, datetime.datetime.now())
        
        # Negative reduction should raise error
        with self.assertRaises(ValueError):
            lot.reduce_qty(-10)

if __name__ == "__main__":
    unittest.main()
