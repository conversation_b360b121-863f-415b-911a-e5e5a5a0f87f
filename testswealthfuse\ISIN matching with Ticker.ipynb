{"cells": [{"cell_type": "code", "execution_count": 1, "id": "08af4da3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              ISIN     WKN                                          Name  \\\n", "0     DE0006632003  663200                                  MorphoSys AG   \n", "1     DE0006069008  606900                                     FRoSTA AG   \n", "2     DE0005897300  589730                             Greiffenberger AG   \n", "3     DE0005760029  576002                                   ABO Wind AG   \n", "4     DE0005140008  514000                              Deutsche Bank AG   \n", "...            ...     ...                                           ...   \n", "3807  US98983V1061  A2JHJJ                            Zuora Inc. Class A   \n", "3808  US98985Y1082  A3DSSN                       Zymeworks Delaware Inc.   \n", "3809  USN070592100  A1J85V    ASML Holding N.V. Aand.aan toon.(N.Y.Reg.)   \n", "3810  USY384721251  885166  Hyundai Motor Co. Ltd. R.Shs GDRs 1/2 SW5000   \n", "3811  USY5217N1183  895354                    Larsen and Toubro Ltd. GDR   \n", "\n", "          SheetName  \n", "0     German Stocks  \n", "1     German Stocks  \n", "2     German Stocks  \n", "3     German Stocks  \n", "4     German Stocks  \n", "...             ...  \n", "3807      US Stocks  \n", "3808      US Stocks  \n", "3809      US Stocks  \n", "3810      US Stocks  \n", "3811      US Stocks  \n", "\n", "[3812 rows x 4 columns]\n"]}], "source": ["import pandas as pd\n", "\n", "# Load the Excel file\n", "file_path = '[EXT] Upvest Instrument Universe.xlsx'\n", "\n", "# Read all sheets\n", "all_sheets = pd.read_excel(file_path, sheet_name=None)\n", "\n", "# Initialize an empty list to store DataFrames\n", "dataframes = []\n", "\n", "# Iterate over all sheets and read specific columns\n", "for sheet_name, df in all_sheets.items():\n", "    # Read the specific columns\n", "    df_selected = df[['ISIN', 'WKN', 'Name']]\n", "    # Add a column for the sheet name\n", "    df_selected['SheetName'] = sheet_name\n", "    # Append the DataFrame to the list\n", "    dataframes.append(df_selected)\n", "\n", "# Concatenate all DataFrames into a single DataFrame\n", "combined_df = pd.concat(dataframes, ignore_index=True)\n", "\n", "# Display the combined DataFrame\n", "print(combined_df)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "37847fe0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ISIN</th>\n", "      <th>WKN</th>\n", "      <th>Name</th>\n", "      <th>She<PERSON><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DE0006632003</td>\n", "      <td>663200</td>\n", "      <td>MorphoSys AG</td>\n", "      <td>German Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DE0006069008</td>\n", "      <td>606900</td>\n", "      <td>FRoSTA AG</td>\n", "      <td>German Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DE0005897300</td>\n", "      <td>589730</td>\n", "      <td>Greiffenberger AG</td>\n", "      <td>German Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DE0005760029</td>\n", "      <td>576002</td>\n", "      <td>ABO Wind AG</td>\n", "      <td>German Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DE0005140008</td>\n", "      <td>514000</td>\n", "      <td>Deutsche Bank AG</td>\n", "      <td>German Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3807</th>\n", "      <td>US98983V1061</td>\n", "      <td>A2JHJJ</td>\n", "      <td>Zuora Inc. Class A</td>\n", "      <td>US Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3808</th>\n", "      <td>US98985Y1082</td>\n", "      <td>A3DSSN</td>\n", "      <td>Zymeworks Delaware Inc.</td>\n", "      <td>US Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3809</th>\n", "      <td>USN070592100</td>\n", "      <td>A1J85V</td>\n", "      <td>ASML Holding N.V. Aand.aan toon.(N.Y.Reg.)</td>\n", "      <td>US Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3810</th>\n", "      <td>USY384721251</td>\n", "      <td>885166</td>\n", "      <td>Hyundai Motor Co. Ltd. R.Shs GDRs 1/2 SW5000</td>\n", "      <td>US Stocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3811</th>\n", "      <td>USY5217N1183</td>\n", "      <td>895354</td>\n", "      <td>Larsen and Toubro Ltd. GDR</td>\n", "      <td>US Stocks</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3812 rows × 4 columns</p>\n", "</div>"], "text/plain": ["              ISIN     WKN                                          Name  \\\n", "0     DE0006632003  663200                                  MorphoSys AG   \n", "1     DE0006069008  606900                                     FRoSTA AG   \n", "2     DE0005897300  589730                             Greiffenberger AG   \n", "3     DE0005760029  576002                                   ABO Wind AG   \n", "4     DE0005140008  514000                              Deutsche Bank AG   \n", "...            ...     ...                                           ...   \n", "3807  US98983V1061  A2JHJJ                            Zuora Inc. Class A   \n", "3808  US98985Y1082  A3DSSN                       Zymeworks Delaware Inc.   \n", "3809  USN070592100  A1J85V    ASML Holding N.V. Aand.aan toon.(N.Y.Reg.)   \n", "3810  USY384721251  885166  Hyundai Motor Co. Ltd. R.Shs GDRs 1/2 SW5000   \n", "3811  USY5217N1183  895354                    Larsen and Toubro Ltd. GDR   \n", "\n", "          SheetName  \n", "0     German Stocks  \n", "1     German Stocks  \n", "2     German Stocks  \n", "3     German Stocks  \n", "4     German Stocks  \n", "...             ...  \n", "3807      US Stocks  \n", "3808      US Stocks  \n", "3809      US Stocks  \n", "3810      US Stocks  \n", "3811      US Stocks  \n", "\n", "[3812 rows x 4 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["combined_df"]}, {"cell_type": "code", "execution_count": 4, "id": "f6f8eda3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: 503\n", "              ISIN     WKN                                          Name  \\\n", "0     DE0006632003  663200                                  MorphoSys AG   \n", "1     DE0006069008  606900                                     FRoSTA AG   \n", "2     DE0005897300  589730                             Greiffenberger AG   \n", "3     DE0005760029  576002                                   ABO Wind AG   \n", "4     DE0005140008  514000                              Deutsche Bank AG   \n", "...            ...     ...                                           ...   \n", "3807  US98983V1061  A2JHJJ                            Zuora Inc. Class A   \n", "3808  US98985Y1082  A3DSSN                       Zymeworks Delaware Inc.   \n", "3809  USN070592100  A1J85V    ASML Holding N.V. Aand.aan toon.(N.Y.Reg.)   \n", "3810  USY384721251  885166  Hyundai Motor Co. Ltd. R.Shs GDRs 1/2 SW5000   \n", "3811  USY5217N1183  895354                    Larsen and Toubro Ltd. GDR   \n", "\n", "          SheetName Ticker  \n", "0     German Stocks    MOR  \n", "1     German Stocks    NLM  \n", "2     German Stocks    GRF  \n", "3     German Stocks    AB9  \n", "4     German Stocks    DBK  \n", "...             ...    ...  \n", "3807      US Stocks    ZUO  \n", "3808      US Stocks   ZYME  \n", "3809      US Stocks   ASML  \n", "3810      US Stocks   HYUN  \n", "3811      US Stocks   LTOD  \n", "\n", "[3812 rows x 5 columns]\n"]}], "source": ["import requests\n", "\n", "def get_tickers_from_isin(isin_list, api_key):\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'X-OPENFIGI-APIKEY': api_key\n", "    }\n", "\n", "    url = 'https://api.openfigi.com/v3/mapping'\n", "    tickers = []\n", "\n", "    for isin in isin_list:\n", "        data = [{'idType': 'ID_ISIN', 'idValue': isin}]\n", "        response = requests.post(url, json=data, headers=headers)\n", "        if response.status_code != 200:\n", "            print(f\"Error: {response.status_code}\")\n", "            tickers.append(None)\n", "            continue\n", "\n", "        result = response.json()\n", "        if result and 'data' in result[0]:\n", "            tickers.append(result[0]['data'][0]['ticker'])\n", "        else:\n", "            tickers.append(None)\n", "\n", "    return tickers\n", "\n", "# Your OpenFIGI API key\n", "api_key = 'f0758424-3d2a-4466-82b7-33f65c90f6fb'\n", "\n", "# Get the list of ISINs from the DataFrame\n", "isin_list = combined_df['ISIN'].tolist()\n", "\n", "# Fetch tickers for all ISINs\n", "tickers = get_tickers_from_isin(isin_list, api_key)\n", "\n", "# Add the tickers as a new column in the DataFrame\n", "combined_df['Ticker'] = tickers\n", "\n", "# Display the DataFrame with tickers\n", "print(combined_df)\n", "# Optionally, save the DataFrame to a new Excel file\n", "combined_df.to_excel('upvest_instrument_universe_with_tickers.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "29d4b328", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}