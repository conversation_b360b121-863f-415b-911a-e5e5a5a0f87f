{"cells": [{"cell_type": "code", "execution_count": 3, "id": "ca3ad34d-f7f8-4635-b1a6-c3fa96d36a34", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "\n", "# Your plotting code here\n", "plt.plot([1, 2, 3, 4], [10, 20, 25, 30])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "dda42561-0f4c-40df-acd9-92f98e9eac3e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}