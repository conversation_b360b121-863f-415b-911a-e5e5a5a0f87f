#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
###############################################################################
#
# Copyright (C) 2015-2023 <PERSON>
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
#
###############################################################################
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)


import collections
import datetime

import backtrader as bt
from backtrader import Order, Position


class Transactions(bt.Analyzer):
    '''This analyzer reports the transactions occurred with each an every data in
    the system

    It looks at the order execution bits to create a ``Position`` starting from
    0 during each ``next`` cycle.

    The result is used during next to record the transactions

    Params:

      - headers (default: ``True``)

        Add an initial key to the dictionary holding the results with the names
        of the datas

        This analyzer was modeled to facilitate the integration with
        ``pyfolio`` and the header names are taken from the samples used for
        it::

          'date', 'amount', 'price', 'sid', 'symbol', 'value'

      - include_taxlot_details (default: ``False``)

        Include tax lot information in transaction records when available.
        This adds additional fields to each transaction record:
        
          'lot_id', 'cost_basis_per_share', 'acquisition_date', 'days_held', 'realized_pnl'
        
        When enabled, transactions will include detailed tax lot allocation
        information for positions that have tax lot tracking enabled.

    Methods:

      - get_analysis

        Returns a dictionary with returns as values and the datetime points for
        each return as keys
    '''
    params = (
        ('headers', False),
        ('include_taxlot_details', False),
        ('_pfheaders', ('date', 'amount', 'price', 'sid', 'symbol', 'value')),
        ('_taxlot_headers', ('lot_id', 'cost_basis_per_share', 'acquisition_date', 'days_held', 'realized_pnl')),
    )

    def start(self):
        super(Transactions, self).start()
        
        # Determine headers based on tax lot inclusion
        if self.p.include_taxlot_details:
            headers = list(self.p._pfheaders[1:]) + list(self.p._taxlot_headers)
        else:
            headers = list(self.p._pfheaders[1:])
            
        if self.p.headers:
            self.rets[self.p._pfheaders[0]] = [headers]

        self._positions = collections.defaultdict(Position)
        self._idnames = list(enumerate(self.strategy.getdatanames()))

    def notify_order(self, order):
        # An order could have several partial executions per cycle (unlikely
        # but possible) and therefore: collect each new execution notification
        # and let the work for next

        # We use a fresh Position object for each round to get summary of what
        # the execution bits have done in that round
        if order.status not in [Order.Partial, Order.Completed]:
            return  # It's not an execution

        pos = self._positions[order.data._name]
        for exbit in order.executed.iterpending():
            if exbit is None:
                break  # end of pending reached

            pos.update(exbit.size, exbit.price)

    def _get_current_datetime(self):
        '''Get current backtest datetime (never use system time!)'''
        try:
            if hasattr(self.strategy, 'datetime') and hasattr(self.strategy.datetime, 'datetime'):
                return self.strategy.datetime.datetime()
            elif len(self.strategy.datas) > 0 and hasattr(self.strategy.datas[0], 'datetime'):
                return self.strategy.datas[0].datetime.datetime()
        except (AttributeError, IndexError):
            pass
        return None

    def _get_taxlot_details(self, position, transaction_size):
        '''Extract tax lot details for the transaction if available'''
        taxlot_details = []
        
        if not hasattr(position, 'get_taxlots') or not position.get_taxlots():
            # No tax lot information available
            return [None, None, None, None, None]
        
        current_dt = self._get_current_datetime()
        if current_dt is None:
            return [None, None, None, None, None]
        
        # For sales (negative transaction_size), get tax lot allocation details
        if transaction_size < 0:
            try:
                # Get the lot allocation for this sale using FIFO
                abs_size = abs(transaction_size)
                taxlots = position.get_taxlots()
                
                # Calculate total realized PnL using tax lot cost basis
                total_realized_pnl = 0.0
                avg_cost_basis = 0.0
                avg_acquisition_date = None
                avg_days_held = 0.0
                lot_ids = []
                
                # Calculate weighted averages for the lots being sold
                total_weight = 0.0
                for lot in taxlots:
                    if lot.remaining_qty > 0:
                        # How much of this lot will be used for this sale
                        lot_qty_used = min(lot.remaining_qty, abs_size)
                        if lot_qty_used > 0:
                            weight = lot_qty_used / abs_size
                            total_weight += weight
                            
                            avg_cost_basis += lot.price * weight
                            lot_ids.append(f"L{id(lot)}")
                            
                            # Calculate days held for this lot
                            if lot.datetime and current_dt:
                                # Ensure lot.datetime is a datetime object for proper calculation
                                if hasattr(lot.datetime, 'year'):
                                    # Already a datetime object
                                    lot_dt = lot.datetime
                                elif hasattr(lot.datetime, 'date'):
                                    # It's a date object, convert to datetime
                                    lot_dt = datetime.datetime.combine(lot.datetime, datetime.time())
                                else:
                                    # Unknown format, skip this lot
                                    continue
                                days_held = (current_dt - lot_dt).days
                                avg_days_held += days_held * weight
                                
                                # Weighted average acquisition date
                                if avg_acquisition_date is None:
                                    avg_acquisition_date = lot_dt
                                else:
                                    # This is a simplification - in reality you'd need more complex date averaging
                                    pass
                            
                            # Calculate realized PnL for this lot portion
                            realized_pnl = lot_qty_used * (position.price - lot.price)
                            total_realized_pnl += realized_pnl
                            
                            abs_size -= lot_qty_used
                            if abs_size <= 0:
                                break
                
                # Return aggregated tax lot information for this transaction
                lot_id = "+".join(lot_ids[:3]) + ("..." if len(lot_ids) > 3 else "")  # Limit length
                acquisition_date_str = avg_acquisition_date.strftime('%Y-%m-%d') if avg_acquisition_date else None
                
                return [
                    lot_id,                      # lot_id
                    round(avg_cost_basis, 4),    # cost_basis_per_share
                    acquisition_date_str,       # acquisition_date
                    int(avg_days_held),          # days_held
                    round(total_realized_pnl, 2) # realized_pnl
                ]
                
            except Exception:
                # If any error occurs in tax lot processing, return empty values
                pass
        
        # For purchases or when tax lot details can't be determined
        return [None, None, None, None, None]

    def next(self):
        # super(Transactions, self).next()  # let dtkey update
        entries = []
        for i, dname in self._idnames:
            pos = self._positions.get(dname, None)
            if pos is not None:
                size, price = pos.size, pos.price
                if size:
                    # Basic transaction data (backward compatible)
                    entry = [size, price, i, dname, -size * price]
                    
                    # Add tax lot details if requested
                    if self.p.include_taxlot_details:
                        # Try to get actual position from strategy to access tax lots
                        try:
                            strategy_pos = getattr(self.strategy.broker, 'positions', {}).get(self.strategy.datas[i], None)
                            if strategy_pos and hasattr(strategy_pos, 'get_taxlots'):
                                taxlot_details = self._get_taxlot_details(strategy_pos, size)
                            else:
                                taxlot_details = [None, None, None, None, None]
                        except (AttributeError, IndexError):
                            taxlot_details = [None, None, None, None, None]
                        
                        entry.extend(taxlot_details)
                    
                    entries.append(entry)

        if entries:
            self.rets[self.strategy.datetime.datetime()] = entries

        self._positions.clear()
