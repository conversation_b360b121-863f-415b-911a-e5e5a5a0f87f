#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
###############################################################################
# Test: 5-stock portfolio with dynamic sell/buy logic
###############################################################################
from __future__ import (absolute_import, division, print_function, unicode_literals)

import datetime
import testcommon
import backtrader as bt
import yfinance as yf
import pandas as pd
import time

# --- Test Parameters ---
TICKERS = ['AAPL', 'GOOGL', 'AMZN', 'NVDA', 'MSFT']
START = datetime.datetime.now() - datetime.timedelta(days=3*365)
END = datetime.datetime.now()

class FiveStockStrategy(bt.Strategy):
    params = dict(
        tickers=None,
        rebuy_wait=5,  # days to wait before rebuy
        drop_pct=0.05, # 5% drop from all-time high
        main=False,
        plot=False,
    )

    def __init__(self):
        self.highs = [None] * len(self.datas)
        self.buy_dates = [None] * len(self.datas)
        self.rebuy_dates = [None] * len(self.datas)
        self.invested = [False] * len(self.datas)
        self.first = True
        self._start_time = None

    def next(self):
        if self.first:
            self._start_time = time.perf_counter()
            value = self.broker.getvalue()
            stake = value / len(self.datas)
            for i, data in enumerate(self.datas):
                if len(data):  # Make sure data is available
                    size = stake // data.close[0]
                    if size > 0:
                        self.buy(data=data, size=size)
                        self.invested[i] = True
                        self.highs[i] = data.close[0]
                        self.buy_dates[i] = self.datas[0].datetime.date(0)
            self.first = False  # <-- Only do this once

        for i, data in enumerate(self.datas):
            if not data:
                continue
            if self.invested[i]:
                # Track all-time high since bought
                if data.close[0] > self.highs[i]:
                    self.highs[i] = data.close[0]
                # Sell if dropped 5% from high
                if data.close[0] < self.highs[i] * (1 - self.p.drop_pct):
                    self.close(data=data)
                    self.invested[i] = False
                    self.rebuy_dates[i] = self.datas[0].datetime.date(0) + datetime.timedelta(days=self.p.rebuy_wait)
            else:
                # Rebuy after 5 days
                if self.rebuy_dates[i] and self.datas[0].datetime.date(0) >= self.rebuy_dates[i]:
                    stake = self.broker.getvalue() / (len(self.datas) - sum(self.invested) or 1)
                    size = stake // data.close[0]
                    if size > 0:
                        self.buy(data=data, size=size)
                        self.invested[i] = True
                        self.highs[i] = data.close[0]
                        self.buy_dates[i] = self.datas[0].datetime.date(0)
                        self.rebuy_dates[i] = None

    def stop(self):
        # Print performance results
        value = self.broker.getvalue()
        cash = self.broker.getcash()
        start_value = self.broker.startingcash if hasattr(self.broker, 'startingcash') else None
        if start_value is None:
            # Try to estimate from initial value
            start_value = value  # fallback
        ret = (value - start_value) / start_value * 100 if start_value else 0
        print(f"\n--- Performance Results ---")
        print(f"Final Portfolio Value: {value:.2f}")
        print(f"Final Cash: {cash:.2f}")
        print(f"Total Return: {ret:.2f}%\n")
        if self._start_time is not None:
            elapsed = time.perf_counter() - self._start_time
            print(f"Strategy run time: {elapsed:.2f} seconds\n")

    def notify_trade(self, trade):
        if trade.isclosed:
            dt = self.data.datetime.date(0)
            ticker = self.data._name if hasattr(self.data, '_name') else 'Unknown'
            print(f"TRADE CLOSED | Date: {dt} | Ticker: {ticker} | Size: {trade.size} | Price: {trade.price:.2f} | Value: {trade.value:.2f} | PnL: {trade.pnl:.2f}")
        elif trade.isopen:
            dt = self.data.datetime.date(0)
            ticker = self.data._name if hasattr(self.data, '_name') else 'Unknown'
            print(f"TRADE OPENED | Date: {dt} | Ticker: {ticker} | Size: {trade.size} | Price: {trade.price:.2f} | Value: {trade.value:.2f}")

class BuyAndHoldStrategy(bt.Strategy):
    params = dict(
        tickers=None,
        main=False,
        plot=False,
    )
    def __init__(self):
        self.first = True
        self._start_time = None

    def next(self):
        if self.first:
            self._start_time = time.perf_counter()
            value = self.broker.getvalue()
            stake = value / len(self.datas)
            for i, data in enumerate(self.datas):
                if len(data):
                    size = stake // data.close[0]
                    if size > 0:
                        self.buy(data=data, size=size)
            self.first = False

    def stop(self):
        value = self.broker.getvalue()
        cash = self.broker.getcash()
        start_value = self.broker.startingcash if hasattr(self.broker, 'startingcash') else None
        if start_value is None:
            start_value = value  # fallback
        ret = (value - start_value) / start_value * 100 if start_value else 0
        print(f"\n--- Buy & Hold Performance Results ---")
        print(f"Final Portfolio Value: {value:.2f}")
        print(f"Final Cash: {cash:.2f}")
        print(f"Total Return: {ret:.2f}%\n")
        if self._start_time is not None:
            elapsed = time.perf_counter() - self._start_time
            print(f"Strategy run time: {elapsed:.2f} seconds\n")

def get_yahoo_data(ticker, fromdate, todate):
    df = yf.download(ticker, start=fromdate.strftime('%Y-%m-%d'), end=todate.strftime('%Y-%m-%d'), group_by='column', auto_adjust=False)
    # Flatten MultiIndex columns if present
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = [col[0] for col in df.columns.values]
    # Rename columns to match Backtrader's expectations
    df.rename(columns={'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}, inplace=True) 
    df = df[['open', 'high', 'low', 'close', 'volume']]

    return bt.feeds.PandasData(dataname=df)

def test_run(main=False):
    datas = [get_yahoo_data(ticker, START, END) for ticker in TICKERS]
    print("\nRunning FiveStockStrategy (dynamic trading)...")
    testcommon.runtest(
        datas,
        FiveStockStrategy,
        main=main,
        plot=False,  # Disable plotting
        tickers=TICKERS
    )
    print("\nRunning BuyAndHoldStrategy (buy & hold)...")
    testcommon.runtest(
        datas,
        BuyAndHoldStrategy,
        main=main,
        plot=False,
        tickers=TICKERS
    )

if __name__ == '__main__':
    test_run(main=True) 