{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["%matplotlib inline"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [], "source": ["from __future__ import (absolute_import, division, print_function,\n", "                        unicode_literals)\n", "\n", "\n", "import argparse\n", "import collections\n", "import datetime\n", "\n", "\n", "import backtrader as bt\n", "\n", "\n", "class St(bt.SignalStrategy):\n", "    params = (\n", "        ('pfast', 13),\n", "        ('ps<PERSON>', 50),\n", "        ('printdata', False),\n", "        ('stake', 1000),\n", "        ('short', False),\n", "    )\n", "\n", "    def __init__(self):\n", "        self.sfast = bt.indicators.SMA(period=self.p.pfast)\n", "        self.sslow = bt.indicators.SMA(period=self.p.pslow)\n", "        self.cover = bt.indicators.CrossOver(self.sfast, self.sslow)\n", "        if self.p.short:\n", "            self.signal_add(bt.SIGNAL_LONGSHORT, self.cover)\n", "        else:\n", "            self.signal_add(bt.SIGNAL_LONG, self.cover)\n", "\n", "    def start(self):\n", "        super(self.__class__, self).start()\n", "        if self.p.printdata:\n", "            txtfields = list()\n", "            txtfields.append('Len')\n", "            txtfields.append('Datetime')\n", "            txtfields.append('Open')\n", "            txtfields.append('High')\n", "            txtfields.append('Low')\n", "            txtfields.append('Close')\n", "            txtfields.append('Volume')\n", "            txtfields.append('OpenInterest')\n", "            print(','.join(txtfields))\n", "\n", "    def next(self):\n", "        super(self.__class__, self).next()\n", "        if self.p.printdata:\n", "            # Print only 1st data ... is just a check that things are running\n", "            txtfields = list()\n", "            txtfields.append('%04d' % len(self))\n", "            txtfields.append(self.data.datetime.datetime(0).isoformat())\n", "            txtfields.append('%.2f' % self.data0.open[0])\n", "            txtfields.append('%.2f' % self.data0.high[0])\n", "            txtfields.append('%.2f' % self.data0.low[0])\n", "            txtfields.append('%.2f' % self.data0.close[0])\n", "            txtfields.append('%.2f' % self.data0.volume[0])\n", "            txtfields.append('%.2f' % self.data0.openinterest[0])\n", "            print(','.join(txtfields))\n", "\n", "\n", "_TFRAMES = collections.OrderedDict(\n", "    (\n", "        ('minutes', bt.TimeFrame.Minutes),\n", "        ('days', bt.TimeFrame.Days),\n", "        ('weeks', bt.TimeFrame<PERSON>Weeks),\n", "        ('months', bt.TimeFrame.Months),\n", "        ('years', bt.TimeFrame.Years),\n", "    )\n", ")\n", "\n", "_TFS = _TFRAMES.keys()\n", "\n", "\n", "def runstrat(args=None):\n", "    args = parse_args(args)\n", "\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()\n", "    cerebro.broker.set_cash(args.cash)\n", "\n", "    dkwargs = dict()\n", "    if args.fromdate:\n", "        fromdate = datetime.datetime.strptime(args.fromdate, '%Y-%m-%d')\n", "        dkwargs['fromdate'] = fromdate\n", "\n", "    if args.todate:\n", "        todate = datetime.datetime.strptime(args.todate, '%Y-%m-%d')\n", "        dkwargs['todate'] = todate\n", "\n", "    if args.timeframe:\n", "        dkwargs['timeframe'] = _TFRAMES[args.timeframe]\n", "\n", "    if args.compression:\n", "        dkwargs['compression'] = args.compression\n", "\n", "    # data0 = bt.feeds.BacktraderCSVData(dataname=args.data0, **dkwargs)\n", "    data0 = bt.feeds.VCData(dataname=args.data0, historical=True, **dkwargs)\n", "    cerebro.adddata(data0, name='Data0')\n", "\n", "    cerebro.addstrategy(St, short=args.short, printdata=args.printdata)\n", "    cerebro.addsizer(bt.sizers.FixedSize, stake=args.stake)\n", "\n", "    # Own analyzerset\n", "    cerebro.addanalyzer(bt.analyzers.TimeReturn, timeframe=bt.TimeFrame.Years)\n", "    cerebro.addanalyzer(bt.analyzers.SharpeRatio, timeframe=bt.TimeFrame.Years)\n", "    cerebro.addanalyzer(bt.analyzers.SQN,)\n", "\n", "    if args.pyfolio:\n", "        cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio',\n", "                            timeframe=_TFRAMES[args.pftimeframe])\n", "\n", "    if args.printout:\n", "        print('Start run')\n", "    results = cerebro.run()\n", "    if args.printout:\n", "        print('End Run')\n", "    strat = results[0]\n", "\n", "    # Results of own analyzers\n", "    al = strat.analyzers.timereturn\n", "    print('-- Time Return:')\n", "    for k, v in al.get_analysis().items():\n", "        print('{}: {}'.format(k, v))\n", "\n", "    al = strat.analyzers.sharperatio\n", "    print('-- <PERSON>:')\n", "    for k, v in al.get_analysis().items():\n", "        print('{}: {}'.format(k, v))\n", "\n", "    al = strat.analyzers.sqn\n", "    print('-- SQN:')\n", "    for k, v in al.get_analysis().items():\n", "        print('{}: {}'.format(k, v))\n", "\n", "    if args.pyfolio:\n", "        pyfoliozer = strat.analyzers.getbyname('pyfolio',)\n", "\n", "        returns, positions, transactions, gross_lev = pyfoliozer.get_pf_items()\n", "        if args.printout:\n", "            print('-- RETURNS')\n", "            print(returns)\n", "            print('-- POSITIONS')\n", "            print(positions)\n", "            print('-- TRANSACTIONS')\n", "            print(transactions)\n", "            print('-- G<PERSON><PERSON> LEVERAGE')\n", "            print(gross_lev)\n", "\n", "        if True:\n", "            import pyfolio as pf\n", "            pf.create_full_tear_sheet(\n", "                returns,\n", "                positions=positions,\n", "                transactions=transactions,\n", "                gross_lev=gross_lev,\n", "                round_trips=True)\n", "\n", "    if args.plot:\n", "        pkwargs = dict(style='bar')\n", "        if args.plot is not True:  # evals to True but is not True\n", "            pkwargs = eval('dict(' + args.plot + ')')  # args were passed\n", "\n", "        cerebro.plot(**pkwargs)\n", "\n", "\n", "def parse_args(pargs=None):\n", "\n", "    parser = argparse.ArgumentParser(\n", "        formatter_class=argparse.ArgumentDefaultsHelpFormatter,\n", "        description='Sample for pivot point and cross plotting')\n", "\n", "    parser.add_argument('--data0', required=True,\n", "                        # default='../../datas/yhoo-1996-2015.txt',\n", "                        help='Data to be read in')\n", "\n", "    parser.add_argument('--timeframe', required=False,\n", "                        default=next(iter(_TFS)), choices=_TFS,\n", "                        help='Starting date in YYYY-MM-DD format')\n", "\n", "    parser.add_argument('--compression', required=False,\n", "                        default=1, type=int,\n", "                        help='Starting date in YYYY-MM-DD format')\n", "\n", "    if False:\n", "        parser.add_argument('--data1', required=False,\n", "                            default='../../datas/orcl-1995-2014.txt',\n", "                            help='Data to be read in')\n", "\n", "    parser.add_argument('--fromdate', required=False,\n", "                        default='2013-01-01',\n", "                        help='Starting date in YYYY-MM-DD format')\n", "\n", "    parser.add_argument('--todate', required=False,\n", "                        default='2015-12-31',\n", "                        help='Ending date in YYYY-MM-DD format')\n", "\n", "    parser.add_argument('--stake', required=False, action='store',\n", "                        default=10, type=int,\n", "                        help=('Stake size'))\n", "\n", "    parser.add_argument('--short', required=False, action='store_true',\n", "                        help=('Go short too'))\n", "\n", "    parser.add_argument('--cash', required=False, action='store',\n", "                        type=float, default=50000,\n", "                        help=('Cash to start with'))\n", "\n", "    parser.add_argument('--pyfolio', required=False, action='store_true',\n", "                        help=('Do pyfolio things'))\n", "\n", "    parser.add_argument('--pftimeframe', required=False,\n", "                        default='days', choices=_TFS,\n", "                        help='Starting date in YYYY-MM-DD format')\n", "\n", "    parser.add_argument('--printout', required=False, action='store_true',\n", "                        help=('Print infos'))\n", "\n", "    parser.add_argument('--printdata', required=False, action='store_true',\n", "                        help=('Print data lines'))\n", "\n", "    # Plot options\n", "    parser.add_argument('--plot', '-p', nargs='?', required=False,\n", "                        metavar='kwargs', const=True,\n", "                        help=('Plot the read data applying any kwargs passed\\n'\n", "                              '\\n'\n", "                              'For example:\\n'\n", "                              '\\n'\n", "                              '  --plot style=\"candle\" (to plot candles)\\n'))\n", "\n", "    if pargs is not None:\n", "        return parser.parse_args(pargs)\n", "\n", "    return parser.parse_args()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"ename": "UnboundLocalError", "evalue": "local variable 'txt' referenced before assignment", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mUnboundLocalError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-7-3298d240ea06>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mrunstrat\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'--data0 015ES --timeframe days --compression 1 --pyfolio --printout --cash 200000 --short'\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msplit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m<ipython-input-6-c5b3d4ae126d>\u001b[0m in \u001b[0;36mrunstrat\u001b[0;34m(args)\u001b[0m\n\u001b[1;32m     94\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     95\u001b[0m     \u001b[0;31m# data0 = bt.feeds.BacktraderCSVData(dataname=args.data0, **dkwargs)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 96\u001b[0;31m     \u001b[0mdata0\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbt\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfeeds\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mVCData\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdataname\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdata0\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mhistorical\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mdkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     97\u001b[0m     \u001b[0mcerebro\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0madddata\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata0\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'Data0'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     98\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32md:\\dro\\01-docs\\01-home\\src\\backtrader\\backtrader\\metabase.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(cls, *args, **kwargs)\u001b[0m\n\u001b[1;32m     85\u001b[0m         \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdonew\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     86\u001b[0m         \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdopreinit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 87\u001b[0;31m         \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdoinit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     88\u001b[0m         \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdopostinit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     89\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0m_obj\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32md:\\dro\\01-docs\\01-home\\src\\backtrader\\backtrader\\metabase.py\u001b[0m in \u001b[0;36mdoinit\u001b[0;34m(cls, _obj, *args, **kwargs)\u001b[0m\n\u001b[1;32m     75\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     76\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mdoinit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcls\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 77\u001b[0;31m         \u001b[0m_obj\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__init__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     78\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     79\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32md:\\dro\\01-docs\\01-home\\src\\backtrader\\backtrader\\feeds\\vcdata.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m    245\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    246\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m__init__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 247\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstore\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mvcstore\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mVCStore\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    248\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    249\u001b[0m         \u001b[0;31m# Correct a copy past directly from VisualChart\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32md:\\dro\\01-docs\\01-home\\src\\backtrader\\backtrader\\stores\\vcstore.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(cls, *args, **kwargs)\u001b[0m\n\u001b[1;32m    180\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_singleton\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    181\u001b[0m             cls._singleton = (\n\u001b[0;32m--> 182\u001b[0;31m                 super(Met<PERSON><PERSON><PERSON>leton, cls).__call__(*args, **kwargs))\n\u001b[0m\u001b[1;32m    183\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    184\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_singleton\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32md:\\dro\\01-docs\\01-home\\src\\backtrader\\backtrader\\metabase.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(cls, *args, **kwargs)\u001b[0m\n\u001b[1;32m     85\u001b[0m         \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdonew\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     86\u001b[0m         \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdopreinit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 87\u001b[0;31m         \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdoinit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     88\u001b[0m         \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdopostinit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     89\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0m_obj\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32md:\\dro\\01-docs\\01-home\\src\\backtrader\\backtrader\\metabase.py\u001b[0m in \u001b[0;36mdoinit\u001b[0;34m(cls, _obj, *args, **kwargs)\u001b[0m\n\u001b[1;32m     75\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     76\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mdoinit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcls\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 77\u001b[0;31m         \u001b[0m_obj\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__init__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     78\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0m_obj\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     79\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32md:\\dro\\01-docs\\01-home\\src\\backtrader\\backtrader\\stores\\vcstore.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    305\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    306\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_load_comtypes\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 307\u001b[0;31m             \u001b[0mmsg\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_RT_TYPELIB\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtxt\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    308\u001b[0m             \u001b[0mtxt\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m'Failed to import comtypes'\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    309\u001b[0m             \u001b[0mmsg\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_RT_COMTYPES\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtxt\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mUnboundLocalError\u001b[0m: local variable 'txt' referenced before assignment"]}], "source": ["runstrat('--data0 015ES --timeframe days --compression 1 --pyfolio --printout --cash 200000 --short'.split())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}}, "nbformat": 4, "nbformat_minor": 0}