{"cells": [{"cell_type": "code", "execution_count": 1, "id": "52cea146-1948-4d6e-8dc8-c3e639829d93", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "plt.style.use('seaborn-v0_8-notebook')\n", "import yfinance as yf\n", "import backtrader as bt\n", "import datetime"]}, {"cell_type": "markdown", "id": "cecd3207-b197-411c-b552-a2762a5d4667", "metadata": {}, "source": ["### Momentum\n", "\n", "if close[2] > close [1] ---> buy if close [2] < close [1] sell"]}, {"cell_type": "code", "execution_count": 2, "id": "b4a9a285-72f0-48ca-98ae-023ea609b696", "metadata": {}, "outputs": [], "source": ["class momentum(bt.Strategy):\n", "    def __init__(self):\n", "        self.dataclose = self.datas[0].close\n", "    def next(self):\n", "        if self.dataclose[-2] > self.dataclose[-1]:\n", "            self.order = self.buy()\n", "            print(f'buy executed @ {self.dataclose[0]}')\n", "        if self.dataclose[-2] < self.dataclose[-1]:\n", "            self.order = self.sell()\n", "            print(f'sell executed @ {self.dataclose[0]}')\n", "        else:\n", "            self.order = self.close()"]}, {"cell_type": "code", "execution_count": 3, "id": "ce7b8446-753a-4e0c-9f49-8600158a13f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro = bt.<PERSON><PERSON><PERSON>()\n", "cerebro.addstrategy(momentum)"]}, {"cell_type": "code", "execution_count": 6, "id": "9be81bcc-fc40-49f6-aadf-e9008ad2efaa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n"]}, {"data": {"text/plain": ["<backtrader.feeds.pandafeed.PandasData at 0x2201ad43d90>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["tsla_daily = yf.download(tickers='TSLA',group_by='column', auto_adjust=False)\n", "if isinstance(tsla_daily.columns, pd.MultiIndex):\n", "    tsla_daily.columns = [col[0] for col in tsla_daily.columns.values]\n", "tsla_bt = bt.feeds.PandasData(dataname = tsla_daily, datetime=None, open=0, high=1, low=2, close=4, volume=5, openinterest=None)\n", "cerebro.adddata(tsla_bt)"]}, {"cell_type": "code", "execution_count": 7, "id": "b18d527a-6889-474b-a310-29b62c8eda67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["buy executed @ 1.2666670083999634\n", "buy executed @ 1.7193330526351929\n", "sell executed @ 1.6666669845581055\n", "buy executed @ 1.5333329439163208\n", "buy executed @ 1.3333330154418945\n", "buy executed @ 1.0933330059051514\n", "buy executed @ 1.0759999752044678\n", "buy executed @ 1.1720000505447388\n", "sell executed @ 1.196666955947876\n", "sell executed @ 1.1593329906463623\n", "buy executed @ 1.1959999799728394\n", "sell executed @ 1.329332947731018\n", "sell executed @ 1.3799999952316284\n", "sell executed @ 1.4246670007705688\n", "sell executed @ 1.4566669464111328\n", "sell executed @ 1.3773330450057983\n", "buy executed @ 1.3666670322418213\n", "buy executed @ 1.4126670360565186\n", "sell executed @ 1.4333330392837524\n", "sell executed @ 1.3940000534057617\n", "buy executed @ 1.3700000047683716\n", "buy executed @ 1.3846670389175415\n", "sell executed @ 1.3466670513153076\n", "buy executed @ 1.3666670322418213\n", "sell executed @ 1.399999976158142\n", "sell executed @ 1.463333010673523\n", "sell executed @ 1.4359999895095825\n", "buy executed @ 1.340000033378601\n", "buy executed @ 1.3266669511795044\n", "buy executed @ 1.309999942779541\n", "buy executed @ 1.246000051498413\n", "buy executed @ 1.1866669654846191\n", "buy executed @ 1.2120000123977661\n", "sell executed @ 1.2300000190734863\n", "sell executed @ 1.2640000581741333\n", "sell executed @ 1.305999994277954\n", "sell executed @ 1.2359999418258667\n", "buy executed @ 1.2433329820632935\n", "sell executed @ 1.2726670503616333\n", "sell executed @ 1.2833329439163208\n", "sell executed @ 1.2773330211639404\n", "buy executed @ 1.3259999752044678\n", "sell executed @ 1.3166669607162476\n", "buy executed @ 1.3133330345153809\n", "buy executed @ 1.3106670379638672\n", "buy executed @ 1.3079999685287476\n", "buy executed @ 1.3580000400543213\n", "sell executed @ 1.391332983970642\n", "sell executed @ 1.3739999532699585\n", "buy executed @ 1.3773330450057983\n", "sell executed @ 1.399999976158142\n", "sell executed @ 1.3833329677581787\n", "buy executed @ 1.3926670551300049\n", "sell executed @ 1.369333028793335\n", "buy executed @ 1.3986669778823853\n", "sell executed @ 1.476667046546936\n", "sell executed @ 1.401332974433899\n", "buy executed @ 1.378000020980835\n", "buy executed @ 1.3926670551300049\n", "sell executed @ 1.391332983970642\n", "buy executed @ 1.3259999752044678\n", "buy executed @ 1.3300000429153442\n", "sell executed @ 1.3600000143051147\n", "sell executed @ 1.4026670455932617\n", "sell executed @ 1.4126670360565186\n", "sell executed @ 1.4666670560836792\n", "sell executed @ 1.3793330192565918\n", "buy executed @ 1.3619999885559082\n", "buy executed @ 1.409999966621399\n", "sell executed @ 1.4040000438690186\n", "buy executed @ 1.3713330030441284\n", "buy executed @ 1.3619999885559082\n", "buy executed @ 1.3626669645309448\n", "sell executed @ 1.3466670513153076\n", "buy executed @ 1.3760000467300415\n", "sell executed @ 1.399999976158142\n", "sell executed @ 1.3926670551300049\n", "buy executed @ 1.3680000305175781\n", "buy executed @ 1.3466670513153076\n", "buy executed @ 1.343999981880188\n", "buy executed @ 1.3739999532699585\n", "sell executed @ 1.3786669969558716\n", "sell executed @ 1.3960000276565552\n", "sell executed @ 1.386667013168335\n", "buy executed @ 1.4166669845581055\n", "sell executed @ 1.4259999990463257\n", "sell executed @ 1.4093329906463623\n", "buy executed @ 1.4626669883728027\n", "sell executed @ 1.4453330039978027\n", "buy executed @ 1.418666958808899\n", "buy executed @ 1.5066670179367065\n", "sell executed @ 1.6579999923706055\n", "sell executed @ 1.6333329677581787\n", "buy executed @ 1.6666669845581055\n", "sell executed @ 1.6319999694824219\n", "buy executed @ 1.9066669940948486\n", "sell executed @ 1.8833329677581787\n", "buy executed @ 2.01466703414917\n", "sell executed @ 2.066667079925537\n", "sell executed @ 2.0133330821990967\n", "buy executed @ 2.0446670055389404\n", "sell executed @ 2.010667085647583\n", "buy executed @ 2.1046669483184814\n", "sell executed @ 2.2193329334259033\n", "sell executed @ 2.351332902908325\n", "sell executed @ 2.373332977294922\n", "sell executed @ 2.3606669902801514\n", "buy executed @ 2.249332904815674\n", "buy executed @ 2.3913331031799316\n", "sell executed @ 2.2673330307006836\n", "buy executed @ 2.134000062942505\n", "buy executed @ 2.0899999141693115\n", "buy executed @ 2.0326669216156006\n", "buy executed @ 2.1653330326080322\n", "sell executed @ 2.167332887649536\n", "sell executed @ 2.136667013168335\n", "buy executed @ 2.109333038330078\n", "buy executed @ 2.0193328857421875\n", "buy executed @ 1.9113329648971558\n", "buy executed @ 2.0\n", "sell executed @ 2.0893330574035645\n", "sell executed @ 2.109333038330078\n", "sell executed @ 2.119999885559082\n", "sell executed @ 2.1500000953674316\n", "sell executed @ 2.0840001106262207\n", "buy executed @ 1.8680000305175781\n", "buy executed @ 1.7233330011367798\n", "buy executed @ 1.8020000457763672\n", "sell executed @ 1.8466670513153076\n", "sell executed @ 1.7713329792022705\n", "buy executed @ 1.7893329858779907\n", "sell executed @ 1.7773330211639404\n", "buy executed @ 1.7653330564498901\n", "buy executed @ 1.7886669635772705\n", "sell executed @ 1.8666670322418213\n", "sell executed @ 1.878000020980835\n", "sell executed @ 1.906000018119812\n", "sell executed @ 1.8006670475006104\n", "buy executed @ 1.797333002090454\n", "buy executed @ 1.7433329820632935\n", "buy executed @ 1.698667049407959\n", "buy executed @ 1.6846669912338257\n", "buy executed @ 1.6019999980926514\n", "buy executed @ 1.5413329601287842\n", "buy executed @ 1.5686670541763306\n", "sell executed @ 1.6433329582214355\n", "sell executed @ 1.647333025932312\n", "sell executed @ 1.6493330001831055\n", "sell executed @ 1.658666968345642\n", "sell executed @ 1.6033329963684082\n", "buy executed @ 1.6206669807434082\n", "sell executed @ 1.6106669902801514\n", "buy executed @ 1.5880000591278076\n", "buy executed @ 1.5626670122146606\n", "buy executed @ 1.5506670475006104\n", "buy executed @ 1.585332989692688\n", "sell executed @ 1.608667016029358\n", "sell executed @ 1.5506670475006104\n", "buy executed @ 1.5499999523162842\n", "buy executed @ 1.5759999752044678\n", "sell executed @ 1.534000039100647\n", "buy executed @ 1.5399999618530273\n", "sell executed @ 1.6419999599456787\n", "sell executed @ 1.5553330183029175\n", "buy executed @ 1.525333046913147\n", "buy executed @ 1.4786670207977295\n", "buy executed @ 1.4520000219345093\n", "buy executed @ 1.5206669569015503\n", "sell executed @ 1.5826669931411743\n", "sell executed @ 1.6033329963684082\n", "sell executed @ 1.5880000591278076\n", "buy executed @ 1.6319999694824219\n", "sell executed @ 1.6319999694824219\n", "sell executed @ 1.6399999856948853\n", "buy executed @ 1.6440000534057617\n", "sell executed @ 1.6293330192565918\n", "buy executed @ 1.590000033378601\n", "buy executed @ 1.5880000591278076\n", "buy executed @ 1.4800000190734863\n", "buy executed @ 1.5240000486373901\n", "sell executed @ 1.5493329763412476\n", "sell executed @ 1.5460000038146973\n", "buy executed @ 1.536666989326477\n", "buy executed @ 1.5153330564498901\n", "buy executed @ 1.4739999771118164\n", "buy executed @ 1.4759999513626099\n", "sell executed @ 1.495332956314087\n", "sell executed @ 1.5133329629898071\n", "sell executed @ 1.553333044052124\n", "sell executed @ 1.6073329448699951\n", "sell executed @ 1.7699999809265137\n", "sell executed @ 1.8300000429153442\n", "sell executed @ 1.7886669635772705\n", "buy executed @ 1.726667046546936\n", "buy executed @ 1.7993329763412476\n", "sell executed @ 1.7899999618530273\n", "buy executed @ 1.8386670351028442\n", "sell executed @ 1.76466703414917\n", "buy executed @ 1.6720000505447388\n", "buy executed @ 1.675333023071289\n", "sell executed @ 1.6579999923706055\n", "buy executed @ 1.7100000381469727\n", "sell executed @ 1.675333023071289\n", "buy executed @ 1.684000015258789\n", "sell executed @ 1.713333010673523\n", "sell executed @ 1.7233330011367798\n", "sell executed @ 1.7799999713897705\n", "sell executed @ 1.7773330211639404\n", "buy executed @ 1.7953330278396606\n", "sell executed @ 1.8046669960021973\n", "sell executed @ 1.8459999561309814\n", "sell executed @ 1.840000033378601\n", "buy executed @ 1.8253329992294312\n", "buy executed @ 1.7853330373764038\n", "buy executed @ 1.8133330345153809\n", "sell executed @ 1.7933330535888672\n", "buy executed @ 1.7999999523162842\n", "sell executed @ 1.8826669454574585\n", "sell executed @ 1.8799999952316284\n", "buy executed @ 1.8046669960021973\n", "buy executed @ 1.8666670322418213\n", "sell executed @ 1.8660000562667847\n", "buy executed @ 1.7999999523162842\n", "buy executed @ 1.7400000095367432\n", "buy executed @ 1.8020000457763672\n", "sell executed @ 1.8839999437332153\n", "sell executed @ 1.841333031654358\n", "buy executed @ 1.801332950592041\n", "buy executed @ 1.7933330535888672\n", "buy executed @ 1.9213329553604126\n", "sell executed @ 1.9693330526351929\n", "sell executed @ 1.9793330430984497\n", "sell executed @ 2.0\n", "sell executed @ 1.901332974433899\n", "buy executed @ 1.9966670274734497\n", "sell executed @ 2.006666898727417\n", "sell executed @ 1.929332971572876\n", "buy executed @ 1.8960000276565552\n", "buy executed @ 1.8286670446395874\n", "buy executed @ 1.8346669673919678\n", "sell executed @ 1.8713330030441284\n", "sell executed @ 1.9026670455932617\n", "sell executed @ 1.8960000276565552\n", "buy executed @ 1.8446669578552246\n", "buy executed @ 1.7913329601287842\n", "buy executed @ 1.75266695022583\n", "buy executed @ 1.7493330240249634\n", "buy executed @ 1.824666976928711\n", "sell executed @ 1.8133330345153809\n", "buy executed @ 1.8426669836044312\n", "sell executed @ 1.848667025566101\n", "sell executed @ 1.852666974067688\n", "sell executed @ 1.899999976158142\n", "sell executed @ 1.899999976158142\n", "sell executed @ 1.9346669912338257\n", "buy executed @ 1.942667007446289\n", "sell executed @ 1.942667007446289\n", "sell executed @ 1.8933329582214355\n", "buy executed @ 1.891332983970642\n", "buy executed @ 1.8953330516815186\n", "sell executed @ 1.9019999504089355\n", "sell executed @ 1.852666974067688\n", "buy executed @ 1.8226670026779175\n", "buy executed @ 1.8386670351028442\n", "sell executed @ 1.8666670322418213\n", "sell executed @ 1.9273329973220825\n", "sell executed @ 1.9133330583572388\n", "buy executed @ 1.934000015258789\n", "sell executed @ 1.8873330354690552\n", "buy executed @ 1.899999976158142\n", "sell executed @ 1.840000033378601\n", "buy executed @ 1.8533329963684082\n", "sell executed @ 1.9113329648971558\n", "sell executed @ 1.9126670360565186\n", "sell executed @ 1.8333330154418945\n", "buy executed @ 1.7673330307006836\n", "buy executed @ 1.6660000085830688\n", "buy executed @ 1.5399999618530273\n", "buy executed @ 1.6100000143051147\n", "sell executed @ 1.6959999799728394\n", "sell executed @ 1.602666974067688\n", "buy executed @ 1.7066669464111328\n", "sell executed @ 1.7746670246124268\n", "sell executed @ 1.7419999837875366\n", "buy executed @ 1.7593330144882202\n", "sell executed @ 1.6666669845581055\n", "buy executed @ 1.5906670093536377\n", "buy executed @ 1.5406670570373535\n", "buy executed @ 1.4620000123977661\n", "buy executed @ 1.5399999618530273\n", "sell executed @ 1.591333031654358\n", "sell executed @ 1.5140000581741333\n", "buy executed @ 1.6146670579910278\n", "sell executed @ 1.6333329677581787\n", "sell executed @ 1.6533329486846924\n", "sell executed @ 1.6440000534057617\n", "buy executed @ 1.5773329734802246\n", "buy executed @ 1.5\n", "buy executed @ 1.5593329668045044\n", "sell executed @ 1.5720000267028809\n", "sell executed @ 1.5579999685287476\n", "buy executed @ 1.5\n", "buy executed @ 1.534000039100647\n", "sell executed @ 1.6166670322418213\n", "sell executed @ 1.6386669874191284\n", "sell executed @ 1.6519999504089355\n", "sell executed @ 1.6633330583572388\n", "sell executed @ 1.7319999933242798\n", "sell executed @ 1.7300000190734863\n", "buy executed @ 1.7093329429626465\n", "buy executed @ 1.6993329524993896\n", "buy executed @ 1.7680000066757202\n", "sell executed @ 1.7333329916000366\n", "buy executed @ 1.7333329916000366\n", "buy executed @ 1.6533329486846924\n", "buy executed @ 1.6633330583572388\n", "sell executed @ 1.5526670217514038\n", "buy executed @ 1.6019999980926514\n", "sell executed @ 1.6913330554962158\n", "sell executed @ 1.7986669540405273\n", "sell executed @ 1.820667028427124\n", "sell executed @ 1.8339999914169312\n", "sell executed @ 1.8166669607162476\n", "buy executed @ 1.8420000076293945\n", "sell executed @ 1.8666670322418213\n", "sell executed @ 1.8573329448699951\n", "buy executed @ 1.8200000524520874\n", "buy executed @ 1.8680000305175781\n", "sell executed @ 1.829332947731018\n", "buy executed @ 1.8266669511795044\n", "buy executed @ 1.8580000400543213\n", "sell executed @ 1.8819999694824219\n", "sell executed @ 1.8793330192565918\n", "buy executed @ 1.8893330097198486\n", "sell executed @ 1.899999976158142\n", "sell executed @ 1.9666670560836792\n", "sell executed @ 1.8926670551300049\n", "buy executed @ 1.9333330392837524\n", "sell executed @ 2.0\n", "sell executed @ 2.0973329544067383\n", "sell executed @ 2.109333038330078\n", "sell executed @ 2.0913329124450684\n", "buy executed @ 2.058000087738037\n", "buy executed @ 2.062666893005371\n", "sell executed @ 2.126667022705078\n", "sell executed @ 2.200000047683716\n", "sell executed @ 2.194667100906372\n", "buy executed @ 2.2320001125335693\n", "sell executed @ 2.299999952316284\n", "sell executed @ 2.2426669597625732\n", "buy executed @ 2.1626670360565186\n", "buy executed @ 2.117332935333252\n", "buy executed @ 2.117332935333252\n", "buy executed @ 2.1333329677581787\n", "sell executed @ 2.1659998893737793\n", "sell executed @ 2.1666669845581055\n", "sell executed @ 2.171333074569702\n", "sell executed @ 2.188667058944702\n", "sell executed @ 2.23533296585083\n", "sell executed @ 2.2799999713897705\n", "sell executed @ 2.308666944503784\n", "sell executed @ 2.055999994277954\n", "buy executed @ 2.0360000133514404\n", "buy executed @ 2.0293331146240234\n", "buy executed @ 2.0380001068115234\n", "sell executed @ 1.9666670560836792\n", "buy executed @ 1.9113329648971558\n", "buy executed @ 1.9193329811096191\n", "sell executed @ 1.8726669549942017\n", "buy executed @ 1.8700000047683716\n", "buy executed @ 1.8606669902801514\n", "buy executed @ 1.840000033378601\n", "buy executed @ 1.8666670322418213\n", "sell executed @ 1.843999981880188\n", "buy executed @ 1.9326670169830322\n", "sell executed @ 1.906000018119812\n", "buy executed @ 1.8993330001831055\n", "buy executed @ 1.929332971572876\n", "sell executed @ 1.880666971206665\n", "buy executed @ 1.8506669998168945\n", "buy executed @ 1.8133330345153809\n", "buy executed @ 1.7999999523162842\n", "buy executed @ 1.829332947731018\n", "sell executed @ 1.841333031654358\n", "sell executed @ 1.8986669778823853\n", "sell executed @ 1.8933329582214355\n", "buy executed @ 1.7746670246124268\n", "buy executed @ 1.7793329954147339\n", "sell executed @ 1.8126670122146606\n", "sell executed @ 1.7933330535888672\n", "buy executed @ 1.7873330116271973\n", "buy executed @ 1.775333046913147\n", "buy executed @ 1.8179999589920044\n", "sell executed @ 1.8713330030441284\n", "sell executed @ 1.899999976158142\n", "sell executed @ 1.965999960899353\n", "sell executed @ 1.9933329820632935\n", "sell executed @ 1.937999963760376\n", "buy executed @ 1.9813330173492432\n", "sell executed @ 2.0273330211639404\n", "sell executed @ 2.0733330249786377\n", "sell executed @ 2.119999885559082\n", "sell executed @ 2.1066670417785645\n", "buy executed @ 2.1333329677581787\n", "sell executed @ 2.1506669521331787\n", "sell executed @ 2.103332996368408\n", "buy executed @ 2.115999937057495\n", "sell executed @ 2.206666946411133\n", "sell executed @ 2.233333110809326\n", "sell executed @ 2.2660000324249268\n", "sell executed @ 2.324666976928711\n", "sell executed @ 2.299999952316284\n", "buy executed @ 2.2660000324249268\n", "buy executed @ 2.2820000648498535\n", "sell executed @ 2.2273330688476562\n", "buy executed @ 2.2426669597625732\n", "sell executed @ 2.253999948501587\n", "sell executed @ 2.2339999675750732\n", "buy executed @ 2.293333053588867\n", "sell executed @ 2.2899999618530273\n", "buy executed @ 2.2166669368743896\n", "buy executed @ 2.2079999446868896\n", "buy executed @ 2.2073330879211426\n", "buy executed @ 2.2133328914642334\n", "sell executed @ 2.312666893005371\n", "sell executed @ 2.434000015258789\n", "sell executed @ 2.4000000953674316\n", "buy executed @ 2.3519999980926514\n", "buy executed @ 2.326667070388794\n", "buy executed @ 2.3506669998168945\n", "sell executed @ 2.3320000171661377\n", "buy executed @ 2.3293330669403076\n", "buy executed @ 2.3313329219818115\n", "sell executed @ 2.2839999198913574\n", "buy executed @ 2.372667074203491\n", "sell executed @ 2.4773330688476562\n", "sell executed @ 2.518666982650757\n", "sell executed @ 2.5460000038146973\n", "sell executed @ 2.501332998275757\n", "buy executed @ 2.4886670112609863\n", "buy executed @ 2.446666955947876\n", "buy executed @ 2.351332902908325\n", "buy executed @ 2.3399999141693115\n", "buy executed @ 2.2733330726623535\n", "buy executed @ 2.2100000381469727\n", "buy executed @ 2.2160000801086426\n", "sell executed @ 2.251332998275757\n", "sell executed @ 2.262666940689087\n", "sell executed @ 2.2273330688476562\n", "buy executed @ 2.1619999408721924\n", "buy executed @ 2.1393330097198486\n", "buy executed @ 2.183332920074463\n", "sell executed @ 2.2093329429626465\n", "sell executed @ 2.190666913986206\n", "buy executed @ 2.121332883834839\n", "buy executed @ 2.138000011444092\n", "sell executed @ 2.1973330974578857\n", "sell executed @ 2.240000009536743\n", "sell executed @ 2.2179999351501465\n", "buy executed @ 2.208667039871216\n", "buy executed @ 2.233333110809326\n", "sell executed @ 2.260667085647583\n", "sell executed @ 2.1546669006347656\n", "buy executed @ 2.130666971206665\n", "buy executed @ 2.1666669845581055\n", "sell executed @ 2.0199999809265137\n", "buy executed @ 2.197999954223633\n", "sell executed @ 2.1659998893737793\n", "buy executed @ 2.128000020980835\n", "buy executed @ 2.0173330307006836\n", "buy executed @ 1.972000002861023\n", "buy executed @ 1.9533330202102661\n", "buy executed @ 1.891332983970642\n", "buy executed @ 1.8386670351028442\n", "buy executed @ 2.006666898727417\n", "sell executed @ 2.0373330116271973\n", "sell executed @ 2.0833330154418945\n", "sell executed @ 2.010667085647583\n", "buy executed @ 2.000667095184326\n", "buy executed @ 2.072000026702881\n", "sell executed @ 2.004667043685913\n", "buy executed @ 1.9019999504089355\n", "buy executed @ 1.8686670064926147\n", "buy executed @ 1.8559999465942383\n", "buy executed @ 1.8799999952316284\n", "sell executed @ 1.987333059310913\n", "sell executed @ 1.9240000247955322\n", "buy executed @ 2.02066707611084\n", "sell executed @ 1.948667049407959\n", "buy executed @ 1.9700000286102295\n", "sell executed @ 2.01200008392334\n", "sell executed @ 1.9593329429626465\n", "buy executed @ 1.996000051498413\n", "sell executed @ 2.134666919708252\n", "sell executed @ 2.233333110809326\n", "sell executed @ 2.2839999198913574\n", "sell executed @ 2.173332929611206\n", "buy executed @ 2.262666940689087\n", "sell executed @ 2.136667013168335\n", "buy executed @ 2.126667022705078\n", "buy executed @ 2.126667022705078\n", "sell executed @ 2.0899999141693115\n", "buy executed @ 2.0399999618530273\n", "buy executed @ 2.053999900817871\n", "sell executed @ 2.065999984741211\n", "sell executed @ 2.062666893005371\n", "buy executed @ 2.1026670932769775\n", "sell executed @ 2.1046669483184814\n", "sell executed @ 2.0859999656677246\n", "buy executed @ 2.197999954223633\n", "sell executed @ 2.2880001068115234\n", "sell executed @ 2.3333330154418945\n", "sell executed @ 2.0946669578552246\n", "buy executed @ 2.181333065032959\n", "sell executed @ 2.138000011444092\n", "buy executed @ 2.069999933242798\n", "buy executed @ 2.0439999103546143\n", "buy executed @ 1.9946670532226562\n", "buy executed @ 1.9933329820632935\n", "buy executed @ 1.9140000343322754\n", "buy executed @ 1.9673329591751099\n", "sell executed @ 1.8359999656677246\n", "buy executed @ 1.8660000562667847\n", "sell executed @ 1.7893329858779907\n", "buy executed @ 1.7933330535888672\n", "sell executed @ 1.8366669416427612\n", "sell executed @ 1.9179999828338623\n", "sell executed @ 1.9933329820632935\n", "sell executed @ 1.968000054359436\n", "buy executed @ 1.9539999961853027\n", "buy executed @ 1.9793330430984497\n", "sell executed @ 2.049999952316284\n", "sell executed @ 1.9593329429626465\n", "buy executed @ 1.9686670303344727\n", "sell executed @ 2.0193328857421875\n", "sell executed @ 2.009999990463257\n", "buy executed @ 1.972000002861023\n", "buy executed @ 1.934000015258789\n", "buy executed @ 2.0\n", "sell executed @ 2.003999948501587\n", "sell executed @ 1.9713330268859863\n", "buy executed @ 1.8933329582214355\n", "buy executed @ 1.8993330001831055\n", "sell executed @ 1.9066669940948486\n", "sell executed @ 1.9073330163955688\n", "sell executed @ 1.901332974433899\n", "buy executed @ 1.8673330545425415\n", "buy executed @ 1.8666670322418213\n", "buy executed @ 1.9033329486846924\n", "sell executed @ 1.946666955947876\n", "sell executed @ 1.8506669998168945\n", "buy executed @ 1.8600000143051147\n", "sell executed @ 1.9046670198440552\n", "sell executed @ 2.0\n", "sell executed @ 2.1566669940948486\n", "sell executed @ 2.125333070755005\n", "buy executed @ 2.066667079925537\n", "buy executed @ 2.062000036239624\n", "buy executed @ 2.0733330249786377\n", "sell executed @ 1.9673329591751099\n", "buy executed @ 1.9079999923706055\n", "buy executed @ 1.843999981880188\n", "buy executed @ 1.8546669483184814\n", "sell executed @ 1.9153330326080322\n", "sell executed @ 1.9666670560836792\n", "sell executed @ 1.9520000219345093\n", "buy executed @ 1.9833329916000366\n", "sell executed @ 2.0\n", "sell executed @ 1.9800000190734863\n", "buy executed @ 1.9240000247955322\n", "buy executed @ 1.9413330554962158\n", "sell executed @ 1.8926670551300049\n", "buy executed @ 1.929332971572876\n", "sell executed @ 1.8880000114440918\n", "buy executed @ 1.8680000305175781\n", "buy executed @ 1.8446669578552246\n", "buy executed @ 1.8833329677581787\n", "sell executed @ 1.9326670169830322\n", "sell executed @ 1.8553329706192017\n", "buy executed @ 1.8660000562667847\n", "sell executed @ 1.8253329992294312\n", "buy executed @ 1.901332974433899\n", "sell executed @ 1.8533329963684082\n", "buy executed @ 1.835332989692688\n", "buy executed @ 1.8466670513153076\n", "sell executed @ 1.8833329677581787\n", "sell executed @ 1.9513330459594727\n", "sell executed @ 1.9866670370101929\n", "sell executed @ 2.0406670570373535\n", "sell executed @ 2.066667079925537\n", "sell executed @ 2.0673329830169678\n", "sell executed @ 2.0399999618530273\n", "buy executed @ 2.0193328857421875\n", "buy executed @ 2.0859999656677246\n", "sell executed @ 2.130666971206665\n", "sell executed @ 2.086667060852051\n", "buy executed @ 2.076667070388794\n", "buy executed @ 2.138000011444092\n", "sell executed @ 2.186666965484619\n", "sell executed @ 2.1740000247955322\n", "buy executed @ 2.173332929611206\n", "buy executed @ 2.140000104904175\n", "buy executed @ 2.1419999599456787\n", "sell executed @ 2.1333329677581787\n", "buy executed @ 2.22933292388916\n", "sell executed @ 2.242000102996826\n", "sell executed @ 2.2593328952789307\n", "sell executed @ 2.2720000743865967\n", "sell executed @ 2.254667043685913\n", "buy executed @ 2.254667043685913\n", "sell executed @ 2.295332908630371\n", "sell executed @ 2.3066670894622803\n", "sell executed @ 2.3473329544067383\n", "sell executed @ 2.3506669998168945\n", "sell executed @ 2.252000093460083\n", "buy executed @ 2.251332998275757\n", "buy executed @ 2.2839999198913574\n", "sell executed @ 2.316667079925537\n", "sell executed @ 2.3006670475006104\n", "buy executed @ 2.262666940689087\n", "buy executed @ 2.2426669597625732\n", "buy executed @ 2.2639999389648438\n", "sell executed @ 2.233333110809326\n", "buy executed @ 2.2253329753875732\n", "buy executed @ 2.200000047683716\n", "buy executed @ 2.3333330154418945\n", "sell executed @ 2.3453330993652344\n", "sell executed @ 2.319999933242798\n", "buy executed @ 2.319999933242798\n", "buy executed @ 2.2673330307006836\n", "buy executed @ 2.257999897003174\n", "buy executed @ 2.2693328857421875\n", "sell executed @ 2.2053329944610596\n", "buy executed @ 2.2073330879211426\n", "sell executed @ 2.256666898727417\n", "sell executed @ 2.2773330211639404\n", "sell executed @ 2.315999984741211\n", "sell executed @ 2.303999900817871\n", "buy executed @ 2.3346669673919678\n", "sell executed @ 2.4000000953674316\n", "sell executed @ 2.4666669368743896\n", "sell executed @ 2.4573330879211426\n", "buy executed @ 2.5399999618530273\n", "sell executed @ 2.5233330726623535\n", "buy executed @ 2.5246670246124268\n", "sell executed @ 2.5446670055389404\n", "sell executed @ 2.559999942779541\n", "sell executed @ 2.5333330631256104\n", "buy executed @ 2.545332908630371\n", "sell executed @ 2.6126670837402344\n", "sell executed @ 2.630000114440918\n", "sell executed @ 2.5320000648498535\n", "buy executed @ 2.563333034515381\n", "sell executed @ 2.553333044052124\n", "buy executed @ 2.5759999752044678\n", "sell executed @ 2.566667079925537\n", "buy executed @ 2.4906671047210693\n", "buy executed @ 2.619999885559082\n", "sell executed @ 2.4326670169830322\n", "buy executed @ 2.381333112716675\n", "buy executed @ 2.4100000858306885\n", "sell executed @ 2.297333002090454\n", "buy executed @ 2.2939999103546143\n", "buy executed @ 2.392667055130005\n", "sell executed @ 2.3333330154418945\n", "buy executed @ 2.318000078201294\n", "buy executed @ 2.4000000953674316\n", "sell executed @ 2.4673330783843994\n", "sell executed @ 2.5153329372406006\n", "sell executed @ 2.5373330116271973\n", "sell executed @ 2.5913329124450684\n", "sell executed @ 2.5933330059051514\n", "sell executed @ 2.5999999046325684\n", "sell executed @ 2.5933330059051514\n", "buy executed @ 2.442667007446289\n", "buy executed @ 2.353332996368408\n", "buy executed @ 2.3499999046325684\n", "buy executed @ 2.3506669998168945\n", "sell executed @ 2.396667003631592\n", "sell executed @ 2.413332939147949\n", "sell executed @ 2.4733328819274902\n", "sell executed @ 2.5320000648498535\n", "sell executed @ 2.5293331146240234\n", "buy executed @ 2.5486669540405273\n", "sell executed @ 2.8239998817443848\n", "sell executed @ 2.9066669940948486\n", "sell executed @ 2.873332977294922\n", "buy executed @ 2.7406671047210693\n", "buy executed @ 2.799999952316284\n", "sell executed @ 2.7980000972747803\n", "buy executed @ 2.7866671085357666\n", "buy executed @ 2.7133328914642334\n", "buy executed @ 2.803999900817871\n", "sell executed @ 2.8833329677581787\n", "sell executed @ 2.9000000953674316\n", "sell executed @ 2.946000099182129\n", "sell executed @ 3.0333330631256104\n", "sell executed @ 3.0653328895568848\n", "sell executed @ 3.1640000343322754\n", "sell executed @ 3.240000009536743\n", "sell executed @ 3.4000000953674316\n", "sell executed @ 3.3933329582214355\n", "buy executed @ 3.3666670322418213\n", "buy executed @ 3.5420000553131104\n", "sell executed @ 3.450666904449463\n", "buy executed @ 3.733333110809326\n", "sell executed @ 3.7326669692993164\n", "buy executed @ 3.5899999141693115\n", "buy executed @ 3.76466703414917\n", "sell executed @ 3.7593328952789307\n", "buy executed @ 4.133333206176758\n", "sell executed @ 3.8333330154418945\n", "buy executed @ 4.674666881561279\n", "sell executed @ 4.6433329582214355\n", "buy executed @ 5.3993330001831055\n", "sell executed @ 6.281332969665527\n", "sell executed @ 5.453332901000977\n", "buy executed @ 6.313333034515381\n", "sell executed @ 6.1666669845581055\n", "buy executed @ 6.074666976928711\n", "buy executed @ 5.900000095367432\n", "buy executed @ 5.757999897003174\n", "buy executed @ 5.6539998054504395\n", "buy executed @ 6.173333168029785\n", "sell executed @ 6.769999980926514\n", "sell executed @ 7.570000171661377\n", "sell executed @ 6.830667018890381\n", "buy executed @ 7.084000110626221\n", "sell executed @ 6.507999897003174\n", "buy executed @ 6.183332920074463\n", "buy executed @ 6.24399995803833\n", "sell executed @ 6.349999904632568\n", "sell executed @ 6.533332824707031\n", "sell executed @ 6.595333099365234\n", "sell executed @ 6.545332908630371\n", "buy executed @ 6.453332901000977\n", "buy executed @ 6.599999904632568\n", "sell executed @ 6.6666669845581055\n", "sell executed @ 6.906667232513428\n", "sell executed @ 6.783332824707031\n", "buy executed @ 6.803999900817871\n", "sell executed @ 6.9766669273376465\n", "sell executed @ 6.913332939147949\n", "buy executed @ 6.433332920074463\n", "buy executed @ 6.873332977294922\n", "sell executed @ 6.920000076293945\n", "sell executed @ 7.116666793823242\n", "sell executed @ 7.23799991607666\n", "sell executed @ 7.2906670570373535\n", "sell executed @ 7.883333206176758\n", "sell executed @ 7.866666793823242\n", "buy executed @ 7.888000011444092\n", "sell executed @ 8.091333389282227\n", "sell executed @ 8.309332847595215\n", "sell executed @ 8.212667465209961\n", "buy executed @ 8.325332641601562\n", "sell executed @ 8.366666793823242\n", "sell executed @ 8.868666648864746\n", "sell executed @ 8.41866683959961\n", "buy executed @ 7.101333141326904\n", "buy executed @ 8.064666748046875\n", "sell executed @ 7.900000095367432\n", "buy executed @ 7.992667198181152\n", "sell executed @ 8.266667366027832\n", "sell executed @ 8.29800033569336\n", "sell executed @ 8.026666641235352\n", "buy executed @ 8.542667388916016\n", "sell executed @ 8.621333122253418\n", "sell executed @ 8.986666679382324\n", "sell executed @ 8.838000297546387\n", "buy executed @ 9.0\n", "sell executed @ 8.97266674041748\n", "buy executed @ 9.333999633789062\n", "sell executed @ 9.649999618530273\n", "sell executed @ 9.459333419799805\n", "buy executed @ 10.289999961853027\n", "sell executed @ 10.15999984741211\n", "buy executed @ 9.961999893188477\n", "buy executed @ 9.966667175292969\n", "sell executed @ 9.514666557312012\n", "buy executed @ 9.095333099365234\n", "buy executed @ 9.442000389099121\n", "sell executed @ 9.562000274658203\n", "sell executed @ 9.90999984741211\n", "sell executed @ 10.0\n", "sell executed @ 9.947999954223633\n", "buy executed @ 10.466667175292969\n", "sell executed @ 11.010000228881836\n", "sell executed @ 10.819999694824219\n", "buy executed @ 11.27066707611084\n", "sell executed @ 10.947999954223633\n", "buy executed @ 11.091333389282227\n", "sell executed @ 11.5600004196167\n", "sell executed @ 11.317999839782715\n", "buy executed @ 11.34000015258789\n", "sell executed @ 11.23799991607666\n", "buy executed @ 10.874667167663574\n", "buy executed @ 10.763333320617676\n", "buy executed @ 11.093999862670898\n", "sell executed @ 10.933333396911621\n", "buy executed @ 10.851332664489746\n", "buy executed @ 11.199999809265137\n", "sell executed @ 11.005332946777344\n", "buy executed @ 11.137999534606934\n", "sell executed @ 11.386667251586914\n", "sell executed @ 11.926667213439941\n", "sell executed @ 12.298666954040527\n", "sell executed @ 11.942667007446289\n", "buy executed @ 12.237333297729492\n", "sell executed @ 12.446666717529297\n", "sell executed @ 12.501333236694336\n", "sell executed @ 12.600000381469727\n", "sell executed @ 12.93066692352295\n", "sell executed @ 12.572667121887207\n", "buy executed @ 11.670000076293945\n", "buy executed @ 11.760000228881836\n", "sell executed @ 12.163999557495117\n", "sell executed @ 12.293333053588867\n", "sell executed @ 11.648667335510254\n", "buy executed @ 11.53933334350586\n", "buy executed @ 11.516667366027832\n", "buy executed @ 11.666666984558105\n", "sell executed @ 12.35200023651123\n", "sell executed @ 12.326666831970215\n", "buy executed @ 12.236000061035156\n", "buy executed @ 12.276666641235352\n", "sell executed @ 12.218667030334473\n", "buy executed @ 11.366666793823242\n", "buy executed @ 11.260666847229004\n", "buy executed @ 11.0\n", "buy executed @ 11.61400032043457\n", "sell executed @ 11.345333099365234\n", "buy executed @ 10.850666999816895\n", "buy executed @ 10.975333213806152\n", "sell executed @ 10.378000259399414\n", "buy executed @ 10.866666793823242\n", "sell executed @ 11.0\n", "sell executed @ 12.0\n", "sell executed @ 10.320667266845703\n", "buy executed @ 9.612667083740234\n", "buy executed @ 9.09866714477539\n", "buy executed @ 9.399999618530273\n", "sell executed @ 9.645999908447266\n", "sell executed @ 9.38933277130127\n", "buy executed @ 9.261333465576172\n", "buy executed @ 9.123332977294922\n", "buy executed @ 9.017999649047852\n", "buy executed @ 7.961999893188477\n", "buy executed @ 8.405332565307617\n", "sell executed @ 8.192667007446289\n", "buy executed @ 8.10533332824707\n", "buy executed @ 8.300000190734863\n", "sell executed @ 7.958666801452637\n", "buy executed @ 8.087332725524902\n", "sell executed @ 8.65133285522461\n", "sell executed @ 8.423333168029785\n", "buy executed @ 8.845333099365234\n", "sell executed @ 9.621333122253418\n", "sell executed @ 9.34333324432373\n", "buy executed @ 9.434000015258789\n", "sell executed @ 9.133333206176758\n", "buy executed @ 9.33666706085205\n", "sell executed @ 9.458666801452637\n", "sell executed @ 9.313332557678223\n", "buy executed @ 9.869999885559082\n", "sell executed @ 9.898667335510254\n", "sell executed @ 9.838666915893555\n", "buy executed @ 10.149333000183105\n", "sell executed @ 9.793333053588867\n", "buy executed @ 9.438667297363281\n", "buy executed @ 9.65666675567627\n", "sell executed @ 10.0\n", "sell executed @ 10.336000442504883\n", "sell executed @ 10.353333473205566\n", "sell executed @ 10.074666976928711\n", "buy executed @ 10.154666900634766\n", "sell executed @ 9.986666679382324\n", "buy executed @ 10.0\n", "sell executed @ 10.0\n", "buy executed @ 9.923333168029785\n", "sell executed @ 10.166666984558105\n", "sell executed @ 9.897333145141602\n", "buy executed @ 9.718667030334473\n", "buy executed @ 9.366666793823242\n", "buy executed @ 11.229999542236328\n", "sell executed @ 10.833333015441895\n", "buy executed @ 11.345999717712402\n", "sell executed @ 11.416000366210938\n", "sell executed @ 11.854000091552734\n", "sell executed @ 11.815333366394043\n", "buy executed @ 11.856666564941406\n", "sell executed @ 11.677332878112793\n", "buy executed @ 11.433333396911621\n", "buy executed @ 11.686667442321777\n", "sell executed @ 11.866666793823242\n", "sell executed @ 11.923333168029785\n", "sell executed @ 12.192667007446289\n", "sell executed @ 12.046667098999023\n", "buy executed @ 11.886667251586914\n", "buy executed @ 11.75333309173584\n", "buy executed @ 12.067333221435547\n", "sell executed @ 12.62266731262207\n", "sell executed @ 13.264666557312012\n", "sell executed @ 13.052000045776367\n", "buy executed @ 12.88933277130127\n", "buy executed @ 13.206666946411133\n", "sell executed @ 13.682666778564453\n", "sell executed @ 13.579999923706055\n", "buy executed @ 14.333999633789062\n", "sell executed @ 14.109333038330078\n", "buy executed @ 13.917332649230957\n", "buy executed @ 15.333333015441895\n", "sell executed @ 17.238666534423828\n", "sell executed @ 17.416667938232422\n", "sell executed @ 16.643333435058594\n", "buy executed @ 15.817333221435547\n", "buy executed @ 17.23200035095215\n", "sell executed @ 17.114667892456055\n", "buy executed @ 16.94266700744629\n", "buy executed @ 16.862667083740234\n", "buy executed @ 16.18000030517578\n", "buy executed @ 15.766667366027832\n", "buy executed @ 15.433333396911621\n", "buy executed @ 16.252666473388672\n", "sell executed @ 15.685999870300293\n", "buy executed @ 15.66333293914795\n", "buy executed @ 15.796667098999023\n", "sell executed @ 16.092666625976562\n", "sell executed @ 15.744000434875488\n", "buy executed @ 15.73466682434082\n", "buy executed @ 15.316666603088379\n", "buy executed @ 14.942667007446289\n", "buy executed @ 14.796667098999023\n", "buy executed @ 14.157999992370605\n", "buy executed @ 14.186667442321777\n", "sell executed @ 14.433333396911621\n", "sell executed @ 13.934666633605957\n", "buy executed @ 14.666666984558105\n", "sell executed @ 15.353333473205566\n", "sell executed @ 15.067333221435547\n", "buy executed @ 13.720666885375977\n", "buy executed @ 14.00333309173584\n", "sell executed @ 14.450667381286621\n", "sell executed @ 14.454667091369629\n", "sell executed @ 13.37399959564209\n", "buy executed @ 13.84000015258789\n", "sell executed @ 13.272666931152344\n", "buy executed @ 13.133333206176758\n", "buy executed @ 13.307332992553711\n", "sell executed @ 13.138667106628418\n", "buy executed @ 13.757332801818848\n", "sell executed @ 14.42199993133545\n", "sell executed @ 14.053999900817871\n", "buy executed @ 13.466667175292969\n", "buy executed @ 13.333333015441895\n", "buy executed @ 13.21399974822998\n", "buy executed @ 13.573332786560059\n", "sell executed @ 13.805333137512207\n", "sell executed @ 13.90666675567627\n", "sell executed @ 13.965332984924316\n", "sell executed @ 14.4399995803833\n", "sell executed @ 13.97599983215332\n", "buy executed @ 12.133333206176758\n", "buy executed @ 11.990667343139648\n", "buy executed @ 12.258000373840332\n", "sell executed @ 12.250666618347168\n", "buy executed @ 12.596667289733887\n", "sell executed @ 12.665332794189453\n", "sell executed @ 12.596667289733887\n", "buy executed @ 12.714667320251465\n", "sell executed @ 13.129332542419434\n", "sell executed @ 13.078666687011719\n", "buy executed @ 13.356666564941406\n", "sell executed @ 13.635333061218262\n", "sell executed @ 13.90133285522461\n", "sell executed @ 14.001333236694336\n", "sell executed @ 14.038000106811523\n", "sell executed @ 14.020000457763672\n", "buy executed @ 13.821999549865723\n", "buy executed @ 13.565999984741211\n", "buy executed @ 13.623332977294922\n", "sell executed @ 13.631333351135254\n", "sell executed @ 13.983332633972168\n", "sell executed @ 13.863332748413086\n", "buy executed @ 13.628666877746582\n", "buy executed @ 13.433333396911621\n", "buy executed @ 13.673333168029785\n", "sell executed @ 13.652000427246094\n", "buy executed @ 13.784000396728516\n", "sell executed @ 14.940667152404785\n", "sell executed @ 15.433333396911621\n", "sell executed @ 15.2586669921875\n", "buy executed @ 15.23466682434082\n", "buy executed @ 15.300666809082031\n", "sell executed @ 15.9313325881958\n", "sell executed @ 15.536666870117188\n", "buy executed @ 15.811332702636719\n", "sell executed @ 15.645999908447266\n", "buy executed @ 15.970000267028809\n", "sell executed @ 16.163999557495117\n", "sell executed @ 16.04400062561035\n", "buy executed @ 15.419333457946777\n", "buy executed @ 15.166666984558105\n", "buy executed @ 14.576666831970215\n", "buy executed @ 14.751333236694336\n", "sell executed @ 14.478667259216309\n", "buy executed @ 14.707332611083984\n", "sell executed @ 14.666000366210938\n", "buy executed @ 15.11533260345459\n", "sell executed @ 14.788000106811523\n", "buy executed @ 14.410667419433594\n", "buy executed @ 14.396666526794434\n", "buy executed @ 14.483332633972168\n", "sell executed @ 14.812666893005371\n", "sell executed @ 14.667332649230957\n", "buy executed @ 14.883333206176758\n", "sell executed @ 14.847999572753906\n", "buy executed @ 14.949999809265137\n", "sell executed @ 15.107333183288574\n", "sell executed @ 14.79466724395752\n", "buy executed @ 15.284000396728516\n", "sell executed @ 15.072667121887207\n", "buy executed @ 15.625332832336426\n", "sell executed @ 15.83133316040039\n", "sell executed @ 15.926667213439941\n", "sell executed @ 16.674667358398438\n", "sell executed @ 16.743999481201172\n", "sell executed @ 17.031999588012695\n", "sell executed @ 17.205333709716797\n", "sell executed @ 17.46733283996582\n", "sell executed @ 17.499332427978516\n", "sell executed @ 17.43199920654297\n", "buy executed @ 17.549999237060547\n", "sell executed @ 17.257999420166016\n", "buy executed @ 16.97800064086914\n", "buy executed @ 17.101333618164062\n", "sell executed @ 16.96933364868164\n", "buy executed @ 17.21266746520996\n", "sell executed @ 17.665332794189453\n", "sell executed @ 17.566667556762695\n", "buy executed @ 17.459333419799805\n", "buy executed @ 17.913333892822266\n", "sell executed @ 18.366666793823242\n", "sell executed @ 19.17799949645996\n", "sell executed @ 18.93400001525879\n", "buy executed @ 18.836666107177734\n", "buy executed @ 18.507999420166016\n", "buy executed @ 18.865999221801758\n", "sell executed @ 18.633333206176758\n", "buy executed @ 18.69733238220215\n", "sell executed @ 18.700000762939453\n", "sell executed @ 18.291332244873047\n", "buy executed @ 17.010000228881836\n", "buy executed @ 17.493999481201172\n", "sell executed @ 17.55733299255371\n", "sell executed @ 17.19933319091797\n", "buy executed @ 17.0\n", "buy executed @ 16.347999572753906\n", "buy executed @ 16.7413330078125\n", "sell executed @ 16.834667205810547\n", "sell executed @ 16.549999237060547\n", "buy executed @ 16.266666412353516\n", "buy executed @ 16.461332321166992\n", "sell executed @ 16.14666748046875\n", "buy executed @ 16.68000030517578\n", "sell executed @ 16.87066650390625\n", "sell executed @ 17.275333404541016\n", "sell executed @ 17.235332489013672\n", "buy executed @ 17.34000015258789\n", "sell executed @ 17.483333587646484\n", "sell executed @ 16.30933380126953\n", "buy executed @ 15.904666900634766\n", "buy executed @ 15.216667175292969\n", "buy executed @ 14.666666984558105\n", "buy executed @ 14.64799976348877\n", "buy executed @ 15.558667182922363\n", "sell executed @ 15.114666938781738\n", "buy executed @ 15.618000030517578\n", "sell executed @ 15.545999526977539\n", "buy executed @ 15.644000053405762\n", "sell executed @ 15.751333236694336\n", "sell executed @ 15.616666793823242\n", "buy executed @ 15.30666732788086\n", "buy executed @ 16.075332641601562\n", "sell executed @ 15.87600040435791\n", "buy executed @ 16.167333602905273\n", "sell executed @ 16.200000762939453\n", "sell executed @ 16.03266716003418\n", "buy executed @ 16.066667556762695\n", "sell executed @ 15.63266658782959\n", "buy executed @ 16.145999908447266\n", "sell executed @ 15.940667152404785\n", "buy executed @ 16.170000076293945\n", "sell executed @ 16.648000717163086\n", "sell executed @ 16.70800018310547\n", "sell executed @ 16.666667938232422\n", "buy executed @ 17.166000366210938\n", "sell executed @ 17.05733299255371\n", "buy executed @ 16.707332611083984\n", "buy executed @ 16.530000686645508\n", "buy executed @ 16.81399917602539\n", "sell executed @ 16.34666633605957\n", "buy executed @ 16.489999771118164\n", "sell executed @ 16.555999755859375\n", "sell executed @ 16.356666564941406\n", "buy executed @ 16.077333450317383\n", "buy executed @ 15.637999534606934\n", "buy executed @ 15.083333015441895\n", "buy executed @ 15.239999771118164\n", "sell executed @ 15.244667053222656\n", "sell executed @ 14.769332885742188\n", "buy executed @ 13.956000328063965\n", "buy executed @ 14.275333404541016\n", "sell executed @ 14.035332679748535\n", "buy executed @ 13.654666900634766\n", "buy executed @ 13.952667236328125\n", "sell executed @ 13.392666816711426\n", "buy executed @ 12.870667457580566\n", "buy executed @ 14.158666610717773\n", "sell executed @ 14.679332733154297\n", "sell executed @ 14.666666984558105\n", "buy executed @ 14.920666694641113\n", "sell executed @ 14.65133285522461\n", "buy executed @ 14.767333030700684\n", "sell executed @ 15.126667022705078\n", "sell executed @ 14.932666778564453\n", "buy executed @ 14.87266731262207\n", "buy executed @ 14.857999801635742\n", "buy executed @ 14.303333282470703\n", "buy executed @ 14.003999710083008\n", "buy executed @ 14.223333358764648\n", "sell executed @ 14.187333106994629\n", "buy executed @ 13.928000450134277\n", "buy executed @ 13.536666870117188\n", "buy executed @ 13.554667472839355\n", "sell executed @ 12.388667106628418\n", "buy executed @ 12.965999603271484\n", "sell executed @ 12.713333129882812\n", "buy executed @ 12.924667358398438\n", "sell executed @ 12.636667251586914\n", "buy executed @ 13.133333206176758\n", "sell executed @ 13.352666854858398\n", "sell executed @ 13.45533275604248\n", "sell executed @ 13.628000259399414\n", "sell executed @ 13.740667343139648\n", "sell executed @ 13.404666900634766\n", "buy executed @ 13.597332954406738\n", "sell executed @ 13.597999572753906\n", "sell executed @ 14.214667320251465\n", "sell executed @ 14.552666664123535\n", "sell executed @ 14.658666610717773\n", "sell executed @ 14.800000190734863\n", "sell executed @ 14.358667373657227\n", "buy executed @ 14.50333309173584\n", "sell executed @ 14.147333145141602\n", "buy executed @ 12.904666900634766\n", "buy executed @ 13.526666641235352\n", "sell executed @ 13.713333129882812\n", "sell executed @ 13.611332893371582\n", "buy executed @ 13.666666984558105\n", "sell executed @ 14.052000045776367\n", "sell executed @ 14.37733268737793\n", "sell executed @ 13.81933307647705\n", "buy executed @ 13.662667274475098\n", "buy executed @ 13.600000381469727\n", "buy executed @ 13.793333053588867\n", "sell executed @ 13.513333320617676\n", "buy executed @ 13.120667457580566\n", "buy executed @ 13.283332824707031\n", "sell executed @ 13.523332595825195\n", "sell executed @ 13.280667304992676\n", "buy executed @ 12.959333419799805\n", "buy executed @ 12.564000129699707\n", "buy executed @ 12.743332862854004\n", "sell executed @ 12.916666984558105\n", "sell executed @ 12.596667289733887\n", "buy executed @ 12.800000190734863\n", "sell executed @ 13.028667449951172\n", "sell executed @ 12.997332572937012\n", "buy executed @ 13.466667175292969\n", "sell executed @ 13.16333293914795\n", "buy executed @ 13.233332633972168\n", "sell executed @ 13.438667297363281\n", "sell executed @ 13.218000411987305\n", "buy executed @ 12.928000450134277\n", "buy executed @ 12.604666709899902\n", "buy executed @ 12.390000343322754\n", "buy executed @ 12.902000427246094\n", "sell executed @ 12.579999923706055\n", "buy executed @ 12.682000160217285\n", "sell executed @ 13.199999809265137\n", "sell executed @ 13.500666618347168\n", "sell executed @ 13.880000114440918\n", "sell executed @ 13.895333290100098\n", "sell executed @ 13.989999771118164\n", "sell executed @ 14.029333114624023\n", "sell executed @ 13.904666900634766\n", "buy executed @ 13.830666542053223\n", "buy executed @ 13.846667289733887\n", "sell executed @ 13.666000366210938\n", "buy executed @ 13.785332679748535\n", "sell executed @ 13.720000267028809\n", "buy executed @ 14.166666984558105\n", "sell executed @ 14.5513334274292\n", "sell executed @ 14.699999809265137\n", "sell executed @ 14.837332725524902\n", "sell executed @ 15.649999618530273\n", "sell executed @ 15.33666706085205\n", "buy executed @ 15.359333038330078\n", "sell executed @ 15.329333305358887\n", "buy executed @ 15.211999893188477\n", "buy executed @ 15.850666999816895\n", "sell executed @ 15.606666564941406\n", "buy executed @ 14.733332633972168\n", "buy executed @ 15.732666969299316\n", "sell executed @ 15.752667427062988\n", "sell executed @ 16.007333755493164\n", "sell executed @ 16.507333755493164\n", "sell executed @ 16.321332931518555\n", "buy executed @ 16.261999130249023\n", "buy executed @ 16.46666717529297\n", "sell executed @ 16.562000274658203\n", "sell executed @ 16.475332260131836\n", "buy executed @ 16.20199966430664\n", "buy executed @ 16.358667373657227\n", "sell executed @ 16.511999130249023\n", "sell executed @ 16.567333221435547\n", "sell executed @ 16.468666076660156\n", "buy executed @ 16.733333587646484\n", "sell executed @ 16.76066780090332\n", "sell executed @ 16.594667434692383\n", "buy executed @ 16.546667098999023\n", "buy executed @ 16.5\n", "buy executed @ 16.399999618530273\n", "buy executed @ 16.72333335876465\n", "sell executed @ 17.02666664123535\n", "sell executed @ 16.793333053588867\n", "buy executed @ 16.884000778198242\n", "sell executed @ 16.680667877197266\n", "buy executed @ 16.64666748046875\n", "buy executed @ 16.67533302307129\n", "sell executed @ 16.81133270263672\n", "sell executed @ 17.46666717529297\n", "sell executed @ 17.49333381652832\n", "sell executed @ 17.476667404174805\n", "buy executed @ 17.35466766357422\n", "buy executed @ 17.798667907714844\n", "sell executed @ 17.76333236694336\n", "buy executed @ 17.926000595092773\n", "sell executed @ 17.463333129882812\n", "buy executed @ 17.65333366394043\n", "sell executed @ 18.073999404907227\n", "sell executed @ 18.68000030517578\n", "sell executed @ 18.591999053955078\n", "buy executed @ 18.333332061767578\n", "buy executed @ 17.288000106811523\n", "buy executed @ 17.27199935913086\n", "buy executed @ 17.481332778930664\n", "sell executed @ 17.483333587646484\n", "sell executed @ 17.47333335876465\n", "buy executed @ 17.78266716003418\n", "sell executed @ 17.614667892456055\n", "buy executed @ 18.166667938232422\n", "sell executed @ 18.333332061767578\n", "sell executed @ 18.003332138061523\n", "buy executed @ 17.417999267578125\n", "buy executed @ 17.976667404174805\n", "sell executed @ 17.825332641601562\n", "buy executed @ 17.495332717895508\n", "buy executed @ 17.049999237060547\n", "buy executed @ 17.618000030517578\n", "sell executed @ 17.512666702270508\n", "buy executed @ 17.84000015258789\n", "sell executed @ 17.752666473388672\n", "buy executed @ 17.333999633789062\n", "buy executed @ 17.57200050354004\n", "sell executed @ 16.63599967956543\n", "buy executed @ 16.238666534423828\n", "buy executed @ 15.876667022705078\n", "buy executed @ 15.8100004196167\n", "buy executed @ 15.666666984558105\n", "buy executed @ 15.990667343139648\n", "sell executed @ 16.482667922973633\n", "sell executed @ 17.03733253479004\n", "sell executed @ 17.025333404541016\n", "buy executed @ 17.35533332824707\n", "sell executed @ 16.804000854492188\n", "buy executed @ 15.733332633972168\n", "buy executed @ 13.519332885742188\n", "buy executed @ 15.368000030517578\n", "sell executed @ 15.195332527160645\n", "buy executed @ 15.399999618530273\n", "sell executed @ 16.124000549316406\n", "sell executed @ 16.374666213989258\n", "sell executed @ 16.022666931152344\n", "buy executed @ 16.35333251953125\n", "sell executed @ 16.804000854492188\n", "sell executed @ 16.05933380126953\n", "buy executed @ 16.336666107177734\n", "sell executed @ 16.803333282470703\n", "sell executed @ 16.48200035095215\n", "buy executed @ 16.50933265686035\n", "sell executed @ 16.739999771118164\n", "sell executed @ 16.850000381469727\n", "sell executed @ 16.869333267211914\n", "sell executed @ 17.597333908081055\n", "sell executed @ 17.19733238220215\n", "buy executed @ 17.59866714477539\n", "sell executed @ 17.268667221069336\n", "buy executed @ 17.463333129882812\n", "sell executed @ 17.302000045776367\n", "buy executed @ 17.77400016784668\n", "sell executed @ 17.156667709350586\n", "buy executed @ 16.69733238220215\n", "buy executed @ 16.799999237060547\n", "sell executed @ 16.500667572021484\n", "buy executed @ 15.706666946411133\n", "buy executed @ 16.589332580566406\n", "sell executed @ 16.0\n", "buy executed @ 15.775333404541016\n", "buy executed @ 15.338666915893555\n", "buy executed @ 14.728667259216309\n", "buy executed @ 14.866000175476074\n", "sell executed @ 14.218667030334473\n", "buy executed @ 14.711333274841309\n", "sell executed @ 14.428667068481445\n", "buy executed @ 14.869333267211914\n", "sell executed @ 15.100000381469727\n", "sell executed @ 15.1813325881958\n", "sell executed @ 14.13266658782959\n", "buy executed @ 14.104000091552734\n", "buy executed @ 14.333333015441895\n", "sell executed @ 14.092000007629395\n", "buy executed @ 14.322667121887207\n", "sell executed @ 14.087332725524902\n", "buy executed @ 14.116666793823242\n", "sell executed @ 14.026666641235352\n", "buy executed @ 13.928000450134277\n", "buy executed @ 14.256667137145996\n", "sell executed @ 15.133333206176758\n", "sell executed @ 15.371999740600586\n", "sell executed @ 15.380000114440918\n", "sell executed @ 15.53266716003418\n", "sell executed @ 14.898667335510254\n", "buy executed @ 14.517999649047852\n", "buy executed @ 14.523332595825195\n", "sell executed @ 14.196666717529297\n", "buy executed @ 13.739333152770996\n", "buy executed @ 14.346667289733887\n", "sell executed @ 14.300000190734863\n", "buy executed @ 14.702667236328125\n", "sell executed @ 14.899333000183105\n", "sell executed @ 14.489999771118164\n", "buy executed @ 14.357999801635742\n", "buy executed @ 14.755999565124512\n", "sell executed @ 15.404000282287598\n", "sell executed @ 15.452667236328125\n", "sell executed @ 15.404000282287598\n", "buy executed @ 15.800000190734863\n", "sell executed @ 15.6986665725708\n", "buy executed @ 15.497332572937012\n", "buy executed @ 15.180000305175781\n", "buy executed @ 15.168000221252441\n", "buy executed @ 15.113332748413086\n", "buy executed @ 14.980667114257812\n", "buy executed @ 15.015999794006348\n", "sell executed @ 14.500666618347168\n", "buy executed @ 14.788000106811523\n", "sell executed @ 14.80666732788086\n", "sell executed @ 15.595999717712402\n", "sell executed @ 15.526000022888184\n", "buy executed @ 15.446000099182129\n", "buy executed @ 15.666000366210938\n", "sell executed @ 15.478667259216309\n", "buy executed @ 15.370667457580566\n", "buy executed @ 15.432666778564453\n", "sell executed @ 15.337332725524902\n", "buy executed @ 15.773332595825195\n", "sell executed @ 15.900667190551758\n", "sell executed @ 15.381333351135254\n", "buy executed @ 15.090666770935059\n", "buy executed @ 14.666666984558105\n", "buy executed @ 14.279333114624023\n", "buy executed @ 14.52400016784668\n", "sell executed @ 14.267333030700684\n", "buy executed @ 14.106666564941406\n", "buy executed @ 14.133999824523926\n", "sell executed @ 13.480667114257812\n", "buy executed @ 13.264666557312012\n", "buy executed @ 13.913999557495117\n", "sell executed @ 13.293333053588867\n", "buy executed @ 13.436667442321777\n", "sell executed @ 13.653332710266113\n", "sell executed @ 13.337332725524902\n", "buy executed @ 13.113332748413086\n", "buy executed @ 12.825332641601562\n", "buy executed @ 12.719332695007324\n", "buy executed @ 12.66333293914795\n", "buy executed @ 12.583999633789062\n", "buy executed @ 12.82800006866455\n", "sell executed @ 12.239333152770996\n", "buy executed @ 11.380000114440918\n", "buy executed @ 11.420000076293945\n", "sell executed @ 10.473333358764648\n", "buy executed @ 9.48799991607666\n", "buy executed @ 10.033332824707031\n", "sell executed @ 10.133333206176758\n", "sell executed @ 10.333333015441895\n", "sell executed @ 10.579999923706055\n", "sell executed @ 10.600000381469727\n", "sell executed @ 11.494667053222656\n", "sell executed @ 10.910667419433594\n", "buy executed @ 11.341333389282227\n", "sell executed @ 11.744000434875488\n", "sell executed @ 11.516667366027832\n", "buy executed @ 11.90999984741211\n", "sell executed @ 12.579999923706055\n", "sell executed @ 12.826666831970215\n", "sell executed @ 12.949999809265137\n", "sell executed @ 12.248666763305664\n", "buy executed @ 12.552000045776367\n", "sell executed @ 13.199999809265137\n", "sell executed @ 13.178667068481445\n", "buy executed @ 13.566666603088379\n", "sell executed @ 13.63466739654541\n", "sell executed @ 14.0\n", "sell executed @ 13.862000465393066\n", "buy executed @ 14.176667213439941\n", "sell executed @ 14.284667015075684\n", "sell executed @ 14.533332824707031\n", "sell executed @ 14.764666557312012\n", "sell executed @ 15.273332595825195\n", "sell executed @ 15.689332962036133\n", "sell executed @ 15.814000129699707\n", "sell executed @ 15.4913330078125\n", "buy executed @ 14.385333061218262\n", "buy executed @ 15.440667152404785\n", "sell executed @ 15.326000213623047\n", "buy executed @ 15.672666549682617\n", "sell executed @ 15.28933334350586\n", "buy executed @ 16.32200050354004\n", "sell executed @ 16.607999801635742\n", "sell executed @ 16.03333282470703\n", "buy executed @ 16.931333541870117\n", "sell executed @ 17.76333236694336\n", "sell executed @ 17.366666793823242\n", "buy executed @ 16.733333587646484\n", "buy executed @ 16.633333206176758\n", "buy executed @ 16.567333221435547\n", "buy executed @ 16.866666793823242\n", "sell executed @ 16.753999710083008\n", "buy executed @ 16.815332412719727\n", "sell executed @ 16.874666213989258\n", "sell executed @ 16.417333602905273\n", "buy executed @ 16.599332809448242\n", "sell executed @ 16.592666625976562\n", "buy executed @ 16.867332458496094\n", "sell executed @ 16.803333282470703\n", "buy executed @ 16.850000381469727\n", "sell executed @ 16.656667709350586\n", "buy executed @ 16.542667388916016\n", "buy executed @ 16.100000381469727\n", "buy executed @ 15.824000358581543\n", "buy executed @ 15.352666854858398\n", "buy executed @ 15.230667114257812\n", "buy executed @ 14.057999610900879\n", "buy executed @ 14.381333351135254\n", "sell executed @ 13.83666706085205\n", "buy executed @ 13.839332580566406\n", "sell executed @ 14.095999717712402\n", "sell executed @ 13.85200023651123\n", "buy executed @ 13.876667022705078\n", "sell executed @ 13.936667442321777\n", "sell executed @ 13.94333267211914\n", "sell executed @ 14.2413330078125\n", "sell executed @ 14.465999603271484\n", "sell executed @ 14.657999992370605\n", "sell executed @ 14.4399995803833\n", "buy executed @ 14.52733325958252\n", "sell executed @ 14.699999809265137\n", "sell executed @ 14.999333381652832\n", "sell executed @ 14.869333267211914\n", "buy executed @ 14.76533317565918\n", "buy executed @ 14.63933277130127\n", "buy executed @ 14.666666984558105\n", "sell executed @ 14.533332824707031\n", "buy executed @ 14.815999984741211\n", "sell executed @ 15.58666706085205\n", "sell executed @ 15.665332794189453\n", "sell executed @ 15.159333229064941\n", "buy executed @ 14.633333206176758\n", "buy executed @ 14.592000007629395\n", "buy executed @ 14.463333129882812\n", "buy executed @ 14.494667053222656\n", "sell executed @ 14.52066707611084\n", "sell executed @ 14.633333206176758\n", "sell executed @ 14.711999893188477\n", "sell executed @ 13.29800033569336\n", "buy executed @ 13.045999526977539\n", "buy executed @ 12.670000076293945\n", "buy executed @ 12.723999977111816\n", "sell executed @ 13.459333419799805\n", "sell executed @ 13.675333023071289\n", "sell executed @ 14.197999954223633\n", "sell executed @ 13.742667198181152\n", "buy executed @ 13.982000350952148\n", "sell executed @ 14.0\n", "sell executed @ 14.206666946411133\n", "sell executed @ 14.520000457763672\n", "sell executed @ 14.663999557495117\n", "sell executed @ 14.9399995803833\n", "sell executed @ 15.033332824707031\n", "sell executed @ 14.874667167663574\n", "buy executed @ 14.834667205810547\n", "buy executed @ 14.642666816711426\n", "buy executed @ 15.0\n", "sell executed @ 15.097999572753906\n", "sell executed @ 15.066666603088379\n", "buy executed @ 14.799332618713379\n", "buy executed @ 14.817999839782715\n", "sell executed @ 15.179332733154297\n", "sell executed @ 15.28933334350586\n", "sell executed @ 15.196666717529297\n", "buy executed @ 15.380000114440918\n", "sell executed @ 15.699999809265137\n", "sell executed @ 15.291333198547363\n", "buy executed @ 15.157999992370605\n", "buy executed @ 15.045999526977539\n", "buy executed @ 15.333333015441895\n", "sell executed @ 15.199999809265137\n", "buy executed @ 15.121333122253418\n", "buy executed @ 15.215999603271484\n", "sell executed @ 15.07800006866455\n", "buy executed @ 15.02733325958252\n", "buy executed @ 15.067999839782715\n", "sell executed @ 15.03266716003418\n", "buy executed @ 14.95533275604248\n", "buy executed @ 14.921333312988281\n", "buy executed @ 14.902667045593262\n", "buy executed @ 14.944666862487793\n", "sell executed @ 14.954667091369629\n", "sell executed @ 15.136667251586914\n", "sell executed @ 14.87399959564209\n", "buy executed @ 14.809332847595215\n", "buy executed @ 14.676667213439941\n", "buy executed @ 14.407333374023438\n", "buy executed @ 14.028667449951172\n", "buy executed @ 13.934000015258789\n", "buy executed @ 13.488666534423828\n", "buy executed @ 13.267999649047852\n", "buy executed @ 13.699999809265137\n", "sell executed @ 13.303333282470703\n", "buy executed @ 13.272666931152344\n", "buy executed @ 13.0\n", "buy executed @ 13.137332916259766\n", "sell executed @ 13.050000190734863\n", "buy executed @ 13.099332809448242\n", "sell executed @ 13.361332893371582\n", "sell executed @ 13.800000190734863\n", "sell executed @ 13.789999961853027\n", "buy executed @ 13.758000373840332\n", "buy executed @ 13.760000228881836\n", "sell executed @ 13.732666969299316\n", "buy executed @ 13.766667366027832\n", "sell executed @ 13.976667404174805\n", "sell executed @ 13.833999633789062\n", "buy executed @ 13.706666946411133\n", "buy executed @ 13.480667114257812\n", "buy executed @ 14.153332710266113\n", "sell executed @ 14.206666946411133\n", "sell executed @ 14.149333000183105\n", "buy executed @ 13.497332572937012\n", "buy executed @ 13.399999618530273\n", "buy executed @ 13.423333168029785\n", "sell executed @ 13.456666946411133\n", "sell executed @ 13.396666526794434\n", "buy executed @ 13.366666793823242\n", "buy executed @ 13.37733268737793\n", "sell executed @ 13.136667251586914\n", "buy executed @ 13.065999984741211\n", "buy executed @ 13.315999984741211\n", "sell executed @ 13.474666595458984\n", "sell executed @ 13.239999771118164\n", "buy executed @ 13.399999618530273\n", "sell executed @ 13.526666641235352\n", "sell executed @ 13.399999618530273\n", "buy executed @ 14.089332580566406\n", "sell executed @ 13.600000381469727\n", "buy executed @ 13.499333381652832\n", "buy executed @ 13.202667236328125\n", "buy executed @ 12.670000076293945\n", "buy executed @ 12.600000381469727\n", "buy executed @ 12.600000381469727\n", "sell executed @ 12.919333457946777\n", "sell executed @ 12.458666801452637\n", "buy executed @ 12.736666679382324\n", "sell executed @ 12.28266716003418\n", "buy executed @ 12.533332824707031\n", "sell executed @ 12.185333251953125\n", "buy executed @ 12.176667213439941\n", "buy executed @ 12.232666969299316\n", "sell executed @ 12.710000038146973\n", "sell executed @ 12.336000442504883\n", "buy executed @ 12.38933277130127\n", "sell executed @ 12.707332611083984\n", "sell executed @ 12.909333229064941\n", "sell executed @ 13.031999588012695\n", "sell executed @ 13.037332534790039\n", "sell executed @ 12.733332633972168\n", "buy executed @ 12.550000190734863\n", "buy executed @ 12.192000389099121\n", "buy executed @ 12.167332649230957\n", "buy executed @ 12.368000030517578\n", "sell executed @ 12.40999984741211\n", "sell executed @ 12.803333282470703\n", "sell executed @ 12.724666595458984\n", "buy executed @ 12.853333473205566\n", "sell executed @ 12.878666877746582\n", "sell executed @ 13.249333381652832\n", "sell executed @ 13.227333068847656\n", "buy executed @ 13.20533275604248\n", "buy executed @ 13.499333381652832\n", "sell executed @ 13.536666870117188\n", "sell executed @ 13.896666526794434\n", "sell executed @ 13.881333351135254\n", "buy executed @ 13.866666793823242\n", "buy executed @ 14.325332641601562\n", "sell executed @ 14.768667221069336\n", "sell executed @ 14.570667266845703\n", "buy executed @ 14.420000076293945\n", "buy executed @ 14.324000358581543\n", "buy executed @ 14.316666603088379\n", "buy executed @ 15.094667434692383\n", "sell executed @ 15.128666877746582\n", "sell executed @ 15.264666557312012\n", "sell executed @ 15.466667175292969\n", "sell executed @ 15.271332740783691\n", "buy executed @ 15.27066707611084\n", "buy executed @ 15.333333015441895\n", "sell executed @ 15.779999732971191\n", "sell executed @ 15.776666641235352\n", "buy executed @ 16.483333587646484\n", "sell executed @ 16.36400032043457\n", "buy executed @ 16.389999389648438\n", "sell executed @ 16.666667938232422\n", "sell executed @ 17.15399932861328\n", "sell executed @ 16.952667236328125\n", "buy executed @ 16.7586669921875\n", "buy executed @ 16.8353328704834\n", "sell executed @ 16.615999221801758\n", "buy executed @ 16.8700008392334\n", "sell executed @ 16.555999755859375\n", "buy executed @ 16.79400062561035\n", "sell executed @ 16.733333587646484\n", "buy executed @ 17.21266746520996\n", "sell executed @ 17.156667709350586\n", "buy executed @ 17.75\n", "sell executed @ 17.986000061035156\n", "sell executed @ 18.049333572387695\n", "sell executed @ 18.601999282836914\n", "sell executed @ 18.666667938232422\n", "sell executed @ 18.50666618347168\n", "buy executed @ 17.719999313354492\n", "buy executed @ 18.363332748413086\n", "sell executed @ 18.687332153320312\n", "sell executed @ 17.600000381469727\n", "buy executed @ 16.8439998626709\n", "buy executed @ 16.544666290283203\n", "buy executed @ 16.279333114624023\n", "buy executed @ 16.94533348083496\n", "sell executed @ 16.6473331451416\n", "buy executed @ 16.715999603271484\n", "sell executed @ 16.527332305908203\n", "buy executed @ 16.794666290283203\n", "sell executed @ 16.46666717529297\n", "buy executed @ 16.5086669921875\n", "sell executed @ 16.413999557495117\n", "buy executed @ 16.321332931518555\n", "buy executed @ 16.407333374023438\n", "sell executed @ 17.133333206176758\n", "sell executed @ 17.49333381652832\n", "sell executed @ 17.600000381469727\n", "sell executed @ 17.373332977294922\n", "buy executed @ 17.52199935913086\n", "sell executed @ 16.770666122436523\n", "buy executed @ 17.025999069213867\n", "sell executed @ 17.046667098999023\n", "sell executed @ 17.373332977294922\n", "sell executed @ 18.468000411987305\n", "sell executed @ 18.555999755859375\n", "sell executed @ 18.535999298095703\n", "buy executed @ 18.582000732421875\n", "sell executed @ 19.126667022705078\n", "sell executed @ 19.792667388916016\n", "sell executed @ 20.13599967956543\n", "sell executed @ 19.79199981689453\n", "buy executed @ 19.833332061767578\n", "sell executed @ 20.610000610351562\n", "sell executed @ 20.892000198364258\n", "sell executed @ 20.422666549682617\n", "buy executed @ 19.780000686645508\n", "buy executed @ 20.18000030517578\n", "sell executed @ 19.979999542236328\n", "buy executed @ 20.163999557495117\n", "sell executed @ 20.43400001525879\n", "sell executed @ 20.133333206176758\n", "buy executed @ 20.614667892456055\n", "sell executed @ 20.53333282470703\n", "buy executed @ 20.82466697692871\n", "sell executed @ 20.779333114624023\n", "buy executed @ 20.655332565307617\n", "buy executed @ 20.992000579833984\n", "sell executed @ 21.600000381469727\n", "sell executed @ 21.17799949645996\n", "buy executed @ 20.496000289916992\n", "buy executed @ 19.866666793823242\n", "buy executed @ 20.726667404174805\n", "sell executed @ 20.625333786010742\n", "buy executed @ 21.437332153320312\n", "sell executed @ 21.559999465942383\n", "sell executed @ 21.698667526245117\n", "sell executed @ 21.225332260131836\n", "buy executed @ 21.172666549682617\n", "buy executed @ 20.959333419799805\n", "buy executed @ 20.46666717529297\n", "buy executed @ 21.03333282470703\n", "sell executed @ 20.85333251953125\n", "buy executed @ 20.69733238220215\n", "buy executed @ 20.43400001525879\n", "buy executed @ 20.73466682434082\n", "sell executed @ 21.152000427246094\n", "sell executed @ 21.733333587646484\n", "sell executed @ 22.512666702270508\n", "sell executed @ 22.933332443237305\n", "sell executed @ 22.65133285522461\n", "buy executed @ 22.566667556762695\n", "buy executed @ 22.979999542236328\n", "sell executed @ 23.756000518798828\n", "sell executed @ 24.25\n", "sell executed @ 24.961332321166992\n", "sell executed @ 23.865999221801758\n", "buy executed @ 24.507999420166016\n", "sell executed @ 25.4060001373291\n", "sell executed @ 24.833332061767578\n", "buy executed @ 25.198667526245117\n", "sell executed @ 25.0\n", "buy executed @ 25.1113338470459\n", "sell executed @ 24.956666946411133\n", "buy executed @ 25.19933319091797\n", "sell executed @ 25.496667861938477\n", "sell executed @ 25.779333114624023\n", "sell executed @ 25.093332290649414\n", "buy executed @ 24.44533348083496\n", "buy executed @ 24.707332611083984\n", "sell executed @ 24.247333526611328\n", "buy executed @ 24.682666778564453\n", "sell executed @ 23.14666748046875\n", "buy executed @ 21.150667190551758\n", "buy executed @ 20.899999618530273\n", "buy executed @ 20.860000610351562\n", "buy executed @ 21.066667556762695\n", "sell executed @ 22.02666664123535\n", "sell executed @ 22.007333755493164\n", "buy executed @ 21.54599952697754\n", "buy executed @ 21.702667236328125\n", "sell executed @ 21.166667938232422\n", "buy executed @ 21.881999969482422\n", "sell executed @ 21.793333053588867\n", "buy executed @ 21.964000701904297\n", "sell executed @ 22.016000747680664\n", "sell executed @ 23.0\n", "sell executed @ 22.69066619873047\n", "buy executed @ 23.066667556762695\n", "sell executed @ 22.459333419799805\n", "buy executed @ 22.366666793823242\n", "buy executed @ 21.53333282470703\n", "buy executed @ 21.262666702270508\n", "buy executed @ 23.02199935913086\n", "sell executed @ 23.133333206176758\n", "sell executed @ 23.823333740234375\n", "sell executed @ 23.8353328704834\n", "sell executed @ 24.066667556762695\n", "sell executed @ 24.106666564941406\n", "sell executed @ 23.79800033569336\n", "buy executed @ 24.308666229248047\n", "sell executed @ 24.34666633605957\n", "sell executed @ 24.200000762939453\n", "buy executed @ 24.08066749572754\n", "buy executed @ 23.527332305908203\n", "buy executed @ 23.05466651916504\n", "buy executed @ 22.742000579833984\n", "buy executed @ 22.599332809448242\n", "buy executed @ 23.501333236694336\n", "sell executed @ 23.615999221801758\n", "sell executed @ 23.152000427246094\n", "buy executed @ 22.631999969482422\n", "buy executed @ 23.31133270263672\n", "sell executed @ 23.56999969482422\n", "sell executed @ 23.7413330078125\n", "sell executed @ 23.586666107177734\n", "buy executed @ 23.299999237060547\n", "buy executed @ 23.065332412719727\n", "buy executed @ 23.266000747680664\n", "sell executed @ 23.42333221435547\n", "sell executed @ 24.299333572387695\n", "sell executed @ 24.254667282104492\n", "buy executed @ 24.288667678833008\n", "sell executed @ 24.96733283996582\n", "sell executed @ 25.350000381469727\n", "sell executed @ 25.333332061767578\n", "buy executed @ 24.866666793823242\n", "buy executed @ 24.99333381652832\n", "sell executed @ 24.432666778564453\n", "buy executed @ 23.543333053588867\n", "buy executed @ 23.39533233642578\n", "buy executed @ 23.32666778564453\n", "buy executed @ 22.658666610717773\n", "buy executed @ 22.790666580200195\n", "sell executed @ 22.834667205810547\n", "sell executed @ 22.393333435058594\n", "buy executed @ 23.416667938232422\n", "sell executed @ 23.733333587646484\n", "sell executed @ 23.540000915527344\n", "buy executed @ 23.309999465942383\n", "buy executed @ 23.1200008392334\n", "buy executed @ 23.592666625976562\n", "sell executed @ 23.530000686645508\n", "buy executed @ 23.798667907714844\n", "sell executed @ 23.583999633789062\n", "buy executed @ 23.393999099731445\n", "buy executed @ 23.731332778930664\n", "sell executed @ 23.70400047302246\n", "buy executed @ 23.512666702270508\n", "buy executed @ 23.325332641601562\n", "buy executed @ 22.586666107177734\n", "buy executed @ 22.446666717529297\n", "buy executed @ 21.851999282836914\n", "buy executed @ 21.316667556762695\n", "buy executed @ 21.278667449951172\n", "buy executed @ 21.34866714477539\n", "sell executed @ 22.149999618530273\n", "sell executed @ 20.0086669921875\n", "buy executed @ 19.96666717529297\n", "buy executed @ 20.46666717529297\n", "sell executed @ 20.06800079345703\n", "buy executed @ 20.366666793823242\n", "sell executed @ 20.166667938232422\n", "buy executed @ 20.166667938232422\n", "buy executed @ 21.0\n", "sell executed @ 20.400667190551758\n", "buy executed @ 20.932666778564453\n", "sell executed @ 21.711332321166992\n", "sell executed @ 20.91933250427246\n", "buy executed @ 20.724000930786133\n", "buy executed @ 21.118000030517578\n", "sell executed @ 20.91933250427246\n", "buy executed @ 20.883333206176758\n", "buy executed @ 21.090667724609375\n", "sell executed @ 21.15333366394043\n", "sell executed @ 20.570667266845703\n", "buy executed @ 20.362667083740234\n", "buy executed @ 20.433332443237305\n", "sell executed @ 20.133333206176758\n", "buy executed @ 20.00666618347168\n", "buy executed @ 20.799999237060547\n", "sell executed @ 20.97333335876465\n", "sell executed @ 20.975332260131836\n", "sell executed @ 22.030000686645508\n", "sell executed @ 22.728666305541992\n", "sell executed @ 22.733999252319336\n", "sell executed @ 22.80266761779785\n", "sell executed @ 22.99333381652832\n", "sell executed @ 22.68400001525879\n", "buy executed @ 22.179332733154297\n", "buy executed @ 21.972667694091797\n", "buy executed @ 21.96733283996582\n", "buy executed @ 21.588666915893555\n", "buy executed @ 21.066667556762695\n", "buy executed @ 20.78333282470703\n", "buy executed @ 21.07866668701172\n", "sell executed @ 20.799999237060547\n", "buy executed @ 21.399999618530273\n", "sell executed @ 20.857999801635742\n", "buy executed @ 21.107999801635742\n", "sell executed @ 21.066667556762695\n", "buy executed @ 22.3439998626709\n", "sell executed @ 22.14666748046875\n", "buy executed @ 22.349332809448242\n", "sell executed @ 22.575332641601562\n", "sell executed @ 22.502666473388672\n", "buy executed @ 22.697999954223633\n", "sell executed @ 23.044666290283203\n", "sell executed @ 23.0\n", "buy executed @ 23.293333053588867\n", "sell executed @ 24.0\n", "sell executed @ 23.6386661529541\n", "buy executed @ 23.218000411987305\n", "buy executed @ 22.766666412353516\n", "buy executed @ 22.656667709350586\n", "buy executed @ 23.00933265686035\n", "sell executed @ 23.167333602905273\n", "sell executed @ 23.399999618530273\n", "sell executed @ 23.229333877563477\n", "buy executed @ 22.531333923339844\n", "buy executed @ 21.680667877197266\n", "buy executed @ 22.599332809448242\n", "sell executed @ 22.887332916259766\n", "sell executed @ 21.32866668701172\n", "buy executed @ 21.075332641601562\n", "buy executed @ 21.001333236694336\n", "buy executed @ 21.389333724975586\n", "sell executed @ 21.633333206176758\n", "sell executed @ 22.166667938232422\n", "sell executed @ 22.29800033569336\n", "sell executed @ 22.402000427246094\n", "sell executed @ 22.368667602539062\n", "buy executed @ 23.18866729736328\n", "sell executed @ 23.566667556762695\n", "sell executed @ 23.75\n", "sell executed @ 23.504667282104492\n", "buy executed @ 23.000667572021484\n", "buy executed @ 21.798667907714844\n", "buy executed @ 22.159332275390625\n", "sell executed @ 22.25\n", "sell executed @ 21.695999145507812\n", "buy executed @ 22.19066619873047\n", "sell executed @ 21.606666564941406\n", "buy executed @ 21.907333374023438\n", "sell executed @ 21.907333374023438\n", "sell executed @ 21.958667755126953\n", "buy executed @ 21.528667449951172\n", "buy executed @ 21.100000381469727\n", "buy executed @ 20.9913330078125\n", "buy executed @ 20.683332443237305\n", "buy executed @ 20.926000595092773\n", "sell executed @ 20.75\n", "buy executed @ 20.48933219909668\n", "buy executed @ 20.266666412353516\n", "buy executed @ 17.6386661529541\n", "buy executed @ 17.099332809448242\n", "buy executed @ 17.083999633789062\n", "buy executed @ 17.988000869750977\n", "sell executed @ 16.851999282836914\n", "buy executed @ 19.28933334350586\n", "sell executed @ 20.066667556762695\n", "sell executed @ 20.024667739868164\n", "buy executed @ 19.931333541870117\n", "buy executed @ 20.049333572387695\n", "sell executed @ 20.154666900634766\n", "sell executed @ 20.239999771118164\n", "sell executed @ 19.933332443237305\n", "buy executed @ 19.257999420166016\n", "buy executed @ 19.405332565307617\n", "sell executed @ 19.405332565307617\n", "sell executed @ 19.41933250427246\n", "buy executed @ 19.0\n", "buy executed @ 18.899999618530273\n", "buy executed @ 18.583332061767578\n", "buy executed @ 19.024667739868164\n", "sell executed @ 19.573999404907227\n", "sell executed @ 19.567333221435547\n", "buy executed @ 19.904666900634766\n", "sell executed @ 18.586000442504883\n", "buy executed @ 18.866666793823242\n", "sell executed @ 19.833332061767578\n", "sell executed @ 20.053333282470703\n", "sell executed @ 20.027332305908203\n", "buy executed @ 20.5\n", "sell executed @ 20.51333236694336\n", "sell executed @ 20.221332550048828\n", "buy executed @ 19.000667572021484\n", "buy executed @ 18.922000885009766\n", "buy executed @ 19.059999465942383\n", "sell executed @ 18.976667404174805\n", "buy executed @ 18.755332946777344\n", "buy executed @ 19.18400001525879\n", "sell executed @ 18.517332077026367\n", "buy executed @ 18.559999465942383\n", "sell executed @ 18.5086669921875\n", "buy executed @ 18.567333221435547\n", "sell executed @ 18.88599967956543\n", "sell executed @ 19.1473331451416\n", "sell executed @ 19.05733299255371\n", "buy executed @ 19.62266731262207\n", "sell executed @ 19.84666633605957\n", "sell executed @ 20.03333282470703\n", "sell executed @ 21.07666778564453\n", "sell executed @ 21.266666412353516\n", "sell executed @ 21.500667572021484\n", "sell executed @ 22.979999542236328\n", "sell executed @ 23.11400032043457\n", "sell executed @ 23.17533302307129\n", "sell executed @ 23.589332580566406\n", "sell executed @ 23.69333267211914\n", "sell executed @ 24.3439998626709\n", "sell executed @ 23.869333267211914\n", "buy executed @ 24.133333206176758\n", "sell executed @ 23.43600082397461\n", "buy executed @ 22.007999420166016\n", "buy executed @ 22.40333366394043\n", "sell executed @ 23.0\n", "sell executed @ 23.243999481201172\n", "sell executed @ 23.55533218383789\n", "sell executed @ 24.004667282104492\n", "sell executed @ 22.116666793823242\n", "buy executed @ 20.917333602905273\n", "buy executed @ 20.329999923706055\n", "buy executed @ 20.799333572387695\n", "sell executed @ 21.637332916259766\n", "sell executed @ 21.053333282470703\n", "buy executed @ 21.428667068481445\n", "sell executed @ 21.038667678833008\n", "buy executed @ 20.78066635131836\n", "buy executed @ 20.58733367919922\n", "buy executed @ 21.666667938232422\n", "sell executed @ 21.088666915893555\n", "buy executed @ 21.415332794189453\n", "sell executed @ 20.12266731262207\n", "buy executed @ 20.294666290283203\n", "sell executed @ 19.78266716003418\n", "buy executed @ 20.323333740234375\n", "sell executed @ 20.483333587646484\n", "sell executed @ 19.726667404174805\n", "buy executed @ 19.483333587646484\n", "buy executed @ 19.865999221801758\n", "sell executed @ 21.895999908447266\n", "sell executed @ 23.187332153320312\n", "sell executed @ 23.03066635131836\n", "buy executed @ 22.922666549682617\n", "buy executed @ 24.606000900268555\n", "sell executed @ 24.3700008392334\n", "buy executed @ 23.600000381469727\n", "buy executed @ 24.075332641601562\n", "sell executed @ 23.89666748046875\n", "buy executed @ 22.79400062561035\n", "buy executed @ 22.660667419433594\n", "buy executed @ 21.566667556762695\n", "buy executed @ 19.446666717529297\n", "buy executed @ 20.707332611083984\n", "sell executed @ 21.391332626342773\n", "sell executed @ 21.275999069213867\n", "buy executed @ 21.3799991607666\n", "sell executed @ 21.200000762939453\n", "buy executed @ 21.227333068847656\n", "sell executed @ 20.684667587280273\n", "buy executed @ 20.150667190551758\n", "buy executed @ 20.133333206176758\n", "buy executed @ 19.79599952697754\n", "buy executed @ 19.003332138061523\n", "buy executed @ 18.98666763305664\n", "buy executed @ 17.34000015258789\n", "buy executed @ 18.21733283996582\n", "sell executed @ 18.631332397460938\n", "sell executed @ 18.762666702270508\n", "sell executed @ 19.201332092285156\n", "sell executed @ 19.250667572021484\n", "sell executed @ 19.336000442504883\n", "sell executed @ 19.779333114624023\n", "sell executed @ 18.700666427612305\n", "buy executed @ 20.237333297729492\n", "sell executed @ 19.84666633605957\n", "buy executed @ 19.898666381835938\n", "sell executed @ 20.0\n", "sell executed @ 20.12733268737793\n", "sell executed @ 20.860000610351562\n", "sell executed @ 18.017332077026367\n", "buy executed @ 20.384666442871094\n", "sell executed @ 20.93000030517578\n", "sell executed @ 20.222000122070312\n", "buy executed @ 19.59666633605957\n", "buy executed @ 18.309999465942383\n", "buy executed @ 17.634666442871094\n", "buy executed @ 17.016666412353516\n", "buy executed @ 17.640666961669922\n", "sell executed @ 17.16866683959961\n", "buy executed @ 17.399999618530273\n", "sell executed @ 17.270666122436523\n", "buy executed @ 17.713333129882812\n", "sell executed @ 18.82666778564453\n", "sell executed @ 17.952667236328125\n", "buy executed @ 17.826000213623047\n", "buy executed @ 17.3786678314209\n", "buy executed @ 17.591333389282227\n", "sell executed @ 20.06999969482422\n", "sell executed @ 21.148000717163086\n", "sell executed @ 20.549999237060547\n", "buy executed @ 22.49799919128418\n", "sell executed @ 21.892667770385742\n", "buy executed @ 22.16933250427246\n", "sell executed @ 22.55066680908203\n", "sell executed @ 22.916000366210938\n", "sell executed @ 22.700000762939453\n", "buy executed @ 22.60466766357422\n", "buy executed @ 22.889333724975586\n", "sell executed @ 23.233333587646484\n", "sell executed @ 23.266666412353516\n", "sell executed @ 23.224666595458984\n", "buy executed @ 22.21066665649414\n", "buy executed @ 22.84666633605957\n", "sell executed @ 22.82200050354004\n", "buy executed @ 23.012666702270508\n", "sell executed @ 23.756000518798828\n", "sell executed @ 22.78333282470703\n", "buy executed @ 23.46666717529297\n", "sell executed @ 22.290000915527344\n", "buy executed @ 21.666667938232422\n", "buy executed @ 22.670000076293945\n", "sell executed @ 23.06599998474121\n", "sell executed @ 23.133333206176758\n", "sell executed @ 22.788667678833008\n", "buy executed @ 24.0\n", "sell executed @ 23.73666763305664\n", "buy executed @ 23.733999252319336\n", "buy executed @ 24.600000381469727\n", "sell executed @ 24.0\n", "buy executed @ 24.660667419433594\n", "sell executed @ 24.628000259399414\n", "buy executed @ 24.676666259765625\n", "sell executed @ 25.0\n", "sell executed @ 24.133333206176758\n", "buy executed @ 23.369333267211914\n", "buy executed @ 22.50666618347168\n", "buy executed @ 21.803333282470703\n", "buy executed @ 21.15999984741211\n", "buy executed @ 20.899999618530273\n", "buy executed @ 20.0\n", "buy executed @ 21.32266616821289\n", "sell executed @ 21.540000915527344\n", "sell executed @ 22.519332885742188\n", "sell executed @ 20.406667709350586\n", "buy executed @ 20.46666717529297\n", "sell executed @ 20.399999618530273\n", "buy executed @ 21.447999954223633\n", "sell executed @ 22.797332763671875\n", "sell executed @ 22.366666793823242\n", "buy executed @ 22.293333053588867\n", "buy executed @ 22.805999755859375\n", "sell executed @ 22.825332641601562\n", "sell executed @ 22.333332061767578\n", "buy executed @ 22.985332489013672\n", "sell executed @ 23.08066749572754\n", "sell executed @ 21.53333282470703\n", "buy executed @ 20.321332931518555\n", "buy executed @ 19.5\n", "buy executed @ 18.868667602539062\n", "buy executed @ 19.625999450683594\n", "sell executed @ 19.527332305908203\n", "buy executed @ 19.684667587280273\n", "sell executed @ 20.030000686645508\n", "sell executed @ 20.066667556762695\n", "sell executed @ 20.3613338470459\n", "sell executed @ 20.865333557128906\n", "sell executed @ 20.832666397094727\n", "buy executed @ 21.305999755859375\n", "sell executed @ 20.886667251586914\n", "buy executed @ 20.455333709716797\n", "buy executed @ 20.773332595825195\n", "sell executed @ 21.079999923706055\n", "sell executed @ 20.823333740234375\n", "buy executed @ 20.225332260131836\n", "buy executed @ 20.299999237060547\n", "sell executed @ 20.437332153320312\n", "sell executed @ 20.29400062561035\n", "buy executed @ 20.12066650390625\n", "buy executed @ 19.632667541503906\n", "buy executed @ 19.860666275024414\n", "sell executed @ 19.481332778930664\n", "buy executed @ 20.118667602539062\n", "sell executed @ 21.261333465576172\n", "sell executed @ 20.46266746520996\n", "buy executed @ 19.874666213989258\n", "buy executed @ 18.799999237060547\n", "buy executed @ 18.43199920654297\n", "buy executed @ 18.589332580566406\n", "sell executed @ 18.46066665649414\n", "buy executed @ 18.90133285522461\n", "sell executed @ 19.099332809448242\n", "sell executed @ 18.926666259765625\n", "buy executed @ 19.496667861938477\n", "sell executed @ 18.900667190551758\n", "buy executed @ 18.399999618530273\n", "buy executed @ 17.833332061767578\n", "buy executed @ 17.979333877563477\n", "sell executed @ 18.17333221435547\n", "sell executed @ 18.172000885009766\n", "buy executed @ 17.31399917602539\n", "buy executed @ 17.62933349609375\n", "sell executed @ 17.916667938232422\n", "sell executed @ 18.477333068847656\n", "sell executed @ 18.579999923706055\n", "sell executed @ 18.841333389282227\n", "sell executed @ 19.219999313354492\n", "sell executed @ 19.154666900634766\n", "buy executed @ 17.459333419799805\n", "buy executed @ 17.99066734313965\n", "sell executed @ 18.512666702270508\n", "sell executed @ 18.110000610351562\n", "buy executed @ 18.44933319091797\n", "sell executed @ 17.886667251586914\n", "buy executed @ 18.014667510986328\n", "sell executed @ 17.908666610717773\n", "buy executed @ 17.71666717529297\n", "buy executed @ 18.316667556762695\n", "sell executed @ 18.082000732421875\n", "buy executed @ 17.933332443237305\n", "buy executed @ 17.343332290649414\n", "buy executed @ 17.59000015258789\n", "sell executed @ 17.0\n", "buy executed @ 16.433332443237305\n", "buy executed @ 15.723999977111816\n", "buy executed @ 16.137332916259766\n", "sell executed @ 15.923333168029785\n", "buy executed @ 16.368000030517578\n", "sell executed @ 16.257333755493164\n", "buy executed @ 16.667999267578125\n", "sell executed @ 17.1200008392334\n", "sell executed @ 16.46266746520996\n", "buy executed @ 16.133333206176758\n", "buy executed @ 15.983332633972168\n", "buy executed @ 15.46733283996582\n", "buy executed @ 15.286666870117188\n", "buy executed @ 15.288000106811523\n", "sell executed @ 15.299332618713379\n", "sell executed @ 14.797332763671875\n", "buy executed @ 13.520000457763672\n", "buy executed @ 13.184000015258789\n", "buy executed @ 13.273332595825195\n", "sell executed @ 12.956000328063965\n", "buy executed @ 13.321999549865723\n", "sell executed @ 12.74666690826416\n", "buy executed @ 12.473333358764648\n", "buy executed @ 12.583333015441895\n", "sell executed @ 12.34000015258789\n", "buy executed @ 12.36733341217041\n", "sell executed @ 12.073332786560059\n", "buy executed @ 13.245332717895508\n", "sell executed @ 13.629332542419434\n", "sell executed @ 13.666666984558105\n", "sell executed @ 14.016667366027832\n", "sell executed @ 14.609333038330078\n", "sell executed @ 14.863332748413086\n", "sell executed @ 14.025333404541016\n", "buy executed @ 14.083333015441895\n", "sell executed @ 14.36533260345459\n", "sell executed @ 15.248000144958496\n", "sell executed @ 15.007332801818848\n", "buy executed @ 14.866666793823242\n", "buy executed @ 14.414667129516602\n", "buy executed @ 14.88266658782959\n", "sell executed @ 14.959333419799805\n", "sell executed @ 14.687333106994629\n", "buy executed @ 14.630000114440918\n", "buy executed @ 14.732666969299316\n", "sell executed @ 15.347332954406738\n", "sell executed @ 15.259332656860352\n", "buy executed @ 15.959333419799805\n", "sell executed @ 15.637999534606934\n", "buy executed @ 15.416000366210938\n", "buy executed @ 15.264666557312012\n", "buy executed @ 15.609999656677246\n", "sell executed @ 15.87600040435791\n", "sell executed @ 15.983332633972168\n", "sell executed @ 16.53333282470703\n", "sell executed @ 16.6200008392334\n", "sell executed @ 17.044666290283203\n", "sell executed @ 17.003332138061523\n", "buy executed @ 17.04599952697754\n", "sell executed @ 17.25\n", "sell executed @ 17.11400032043457\n", "buy executed @ 17.277999877929688\n", "sell executed @ 15.566666603088379\n", "buy executed @ 15.128000259399414\n", "buy executed @ 15.13933277130127\n", "sell executed @ 15.526666641235352\n", "sell executed @ 16.200000762939453\n", "sell executed @ 16.176666259765625\n", "buy executed @ 15.423333168029785\n", "buy executed @ 15.30666732788086\n", "buy executed @ 15.458666801452637\n", "sell executed @ 15.100000381469727\n", "buy executed @ 15.630000114440918\n", "sell executed @ 15.736666679382324\n", "sell executed @ 15.53266716003418\n", "buy executed @ 15.253999710083008\n", "buy executed @ 15.413999557495117\n", "sell executed @ 14.723999977111816\n", "buy executed @ 14.444000244140625\n", "buy executed @ 14.947333335876465\n", "sell executed @ 15.174667358398438\n", "sell executed @ 14.800666809082031\n", "buy executed @ 14.853333473205566\n", "sell executed @ 14.664667129516602\n", "buy executed @ 14.239999771118164\n", "buy executed @ 14.38266658782959\n", "sell executed @ 14.246000289916992\n", "buy executed @ 14.600000381469727\n", "sell executed @ 15.276666641235352\n", "sell executed @ 14.938667297363281\n", "buy executed @ 15.12600040435791\n", "sell executed @ 14.833333015441895\n", "buy executed @ 15.146666526794434\n", "sell executed @ 15.333333015441895\n", "sell executed @ 15.386667251586914\n", "sell executed @ 15.825332641601562\n", "sell executed @ 16.51333236694336\n", "sell executed @ 16.464000701904297\n", "buy executed @ 16.399999618530273\n", "buy executed @ 16.1646671295166\n", "buy executed @ 16.333332061767578\n", "sell executed @ 16.399999618530273\n", "sell executed @ 16.432666778564453\n", "sell executed @ 16.0\n", "buy executed @ 16.101333618164062\n", "sell executed @ 14.970666885375977\n", "buy executed @ 15.37733268737793\n", "sell executed @ 16.14666748046875\n", "sell executed @ 16.200000762939453\n", "sell executed @ 16.100000381469727\n", "buy executed @ 16.21933364868164\n", "sell executed @ 15.457332611083984\n", "buy executed @ 15.440667152404785\n", "buy executed @ 15.319999694824219\n", "buy executed @ 15.724666595458984\n", "sell executed @ 16.08799934387207\n", "sell executed @ 16.351999282836914\n", "sell executed @ 16.476667404174805\n", "sell executed @ 16.52666664123535\n", "sell executed @ 17.18000030517578\n", "sell executed @ 17.159332275390625\n", "buy executed @ 17.5\n", "sell executed @ 17.3799991607666\n", "buy executed @ 17.222000122070312\n", "buy executed @ 16.954666137695312\n", "buy executed @ 16.96666717529297\n", "sell executed @ 19.891332626342773\n", "sell executed @ 19.847999572753906\n", "buy executed @ 21.836000442504883\n", "sell executed @ 21.332666397094727\n", "buy executed @ 20.866666793823242\n", "buy executed @ 20.873332977294922\n", "sell executed @ 21.08799934387207\n", "sell executed @ 20.98666763305664\n", "buy executed @ 21.308000564575195\n", "sell executed @ 21.200000762939453\n", "buy executed @ 21.94266700744629\n", "sell executed @ 22.299999237060547\n", "sell executed @ 22.93000030517578\n", "sell executed @ 23.126667022705078\n", "sell executed @ 23.666667938232422\n", "sell executed @ 23.073999404907227\n", "buy executed @ 23.375999450683594\n", "sell executed @ 23.527999877929688\n", "sell executed @ 23.450000762939453\n", "buy executed @ 24.0\n", "sell executed @ 23.634000778198242\n", "buy executed @ 22.67733383178711\n", "buy executed @ 22.954666137695312\n", "sell executed @ 22.351333618164062\n", "buy executed @ 22.07466697692871\n", "buy executed @ 22.073999404907227\n", "buy executed @ 21.959999084472656\n", "buy executed @ 22.174667358398438\n", "sell executed @ 22.516666412353516\n", "sell executed @ 22.18866729736328\n", "buy executed @ 22.333332061767578\n", "sell executed @ 22.439332962036133\n", "sell executed @ 22.663999557495117\n", "sell executed @ 23.458667755126953\n", "sell executed @ 23.661333084106445\n", "sell executed @ 24.06999969482422\n", "sell executed @ 24.170000076293945\n", "sell executed @ 25.266000747680664\n", "sell executed @ 25.375333786010742\n", "sell executed @ 26.488000869750977\n", "sell executed @ 27.3526668548584\n", "sell executed @ 27.45199966430664\n", "sell executed @ 27.890666961669922\n", "sell executed @ 28.527332305908203\n", "sell executed @ 29.0\n", "sell executed @ 28.586000442504883\n", "buy executed @ 27.0\n", "buy executed @ 28.299999237060547\n", "sell executed @ 29.366666793823242\n", "sell executed @ 29.364667892456055\n", "buy executed @ 30.760000228881836\n", "sell executed @ 31.579999923706055\n", "sell executed @ 33.13999938964844\n", "sell executed @ 32.11933135986328\n", "buy executed @ 32.900001525878906\n", "sell executed @ 36.284000396728516\n", "sell executed @ 35.31733322143555\n", "buy executed @ 32.91666793823242\n", "buy executed @ 33.840667724609375\n", "sell executed @ 35.349998474121094\n", "sell executed @ 38.125999450683594\n", "sell executed @ 37.616668701171875\n", "buy executed @ 38.04199981689453\n", "sell executed @ 36.132667541503906\n", "buy executed @ 37.89933395385742\n", "sell executed @ 38.37933349609375\n", "sell executed @ 42.16133117675781\n", "sell executed @ 42.66666793823242\n", "sell executed @ 44.91266632080078\n", "sell executed @ 58.86399841308594\n", "sell executed @ 54.88399887084961\n", "buy executed @ 46.66133117675781\n", "buy executed @ 48.70333480834961\n", "sell executed @ 53.33333206176758\n", "sell executed @ 51.25266647338867\n", "buy executed @ 51.858001708984375\n", "sell executed @ 49.45600128173828\n", "buy executed @ 52.4813346862793\n", "sell executed @ 56.106666564941406\n", "sell executed @ 61.56666564941406\n", "sell executed @ 60.79666519165039\n", "buy executed @ 60.46533203125\n", "buy executed @ 55.93333435058594\n", "buy executed @ 56.599998474121094\n", "sell executed @ 52.16666793823242\n", "buy executed @ 48.66666793823242\n", "buy executed @ 41.97999954223633\n", "buy executed @ 47.41733169555664\n", "sell executed @ 53.66666793823242\n", "sell executed @ 50.930667877197266\n", "buy executed @ 48.2513313293457\n", "buy executed @ 46.0\n", "buy executed @ 40.35933303833008\n", "buy executed @ 43.96200180053711\n", "sell executed @ 42.68000030517578\n", "buy executed @ 38.72600173950195\n", "buy executed @ 39.66666793823242\n", "sell executed @ 31.299999237060547\n", "buy executed @ 29.333999633789062\n", "buy executed @ 25.933332443237305\n", "buy executed @ 24.979999542236328\n", "buy executed @ 29.213333129882812\n", "sell executed @ 28.906667709350586\n", "buy executed @ 31.81999969482422\n", "sell executed @ 36.349998474121094\n", "sell executed @ 36.49266815185547\n", "sell executed @ 33.66666793823242\n", "buy executed @ 34.017333984375\n", "sell executed @ 33.41666793823242\n", "buy executed @ 33.599998474121094\n", "sell executed @ 32.068668365478516\n", "buy executed @ 33.96666717529297\n", "sell executed @ 34.08000183105469\n", "sell executed @ 36.33333206176758\n", "sell executed @ 36.9466667175293\n", "sell executed @ 37.4726676940918\n", "sell executed @ 39.34400177001953\n", "sell executed @ 46.597999572753906\n", "sell executed @ 49.46666717529297\n", "sell executed @ 47.79600143432617\n", "buy executed @ 51.48533248901367\n", "sell executed @ 48.8466682434082\n", "buy executed @ 48.67466735839844\n", "buy executed @ 46.93199920654297\n", "buy executed @ 48.50666809082031\n", "sell executed @ 47.387332916259766\n", "buy executed @ 49.17399978637695\n", "sell executed @ 53.042667388916016\n", "sell executed @ 52.678001403808594\n", "buy executed @ 57.01266860961914\n", "sell executed @ 50.33333206176758\n", "buy executed @ 46.733333587646484\n", "buy executed @ 52.65266799926758\n", "sell executed @ 51.766666412353516\n", "buy executed @ 51.81399917602539\n", "sell executed @ 52.917999267578125\n", "sell executed @ 52.70066833496094\n", "buy executed @ 55.133331298828125\n", "sell executed @ 54.72200012207031\n", "buy executed @ 52.0\n", "buy executed @ 52.689998626708984\n", "sell executed @ 55.185333251953125\n", "sell executed @ 54.34466552734375\n", "buy executed @ 54.70000076293945\n", "sell executed @ 54.400001525878906\n", "buy executed @ 54.81133270263672\n", "sell executed @ 55.633331298828125\n", "sell executed @ 54.7239990234375\n", "buy executed @ 54.23400115966797\n", "buy executed @ 53.91666793823242\n", "buy executed @ 57.20000076293945\n", "sell executed @ 59.64666748046875\n", "sell executed @ 59.20800018310547\n", "buy executed @ 59.32533264160156\n", "sell executed @ 58.522666931152344\n", "buy executed @ 61.266666412353516\n", "sell executed @ 62.66733169555664\n", "sell executed @ 66.12533569335938\n", "sell executed @ 66.01333618164062\n", "buy executed @ 65.33333587646484\n", "buy executed @ 61.18600082397461\n", "buy executed @ 67.4566650390625\n", "sell executed @ 65.84733581542969\n", "buy executed @ 66.86666870117188\n", "sell executed @ 67.51866912841797\n", "sell executed @ 66.663330078125\n", "buy executed @ 66.59200286865234\n", "buy executed @ 66.27400207519531\n", "buy executed @ 63.61800003051758\n", "buy executed @ 66.31866455078125\n", "sell executed @ 64.60066986083984\n", "buy executed @ 67.0999984741211\n", "sell executed @ 72.19999694824219\n", "sell executed @ 81.43199920654297\n", "sell executed @ 85.11266326904297\n", "sell executed @ 93.6673355102539\n", "sell executed @ 93.66666412353516\n", "buy executed @ 93.1326675415039\n", "buy executed @ 93.06666564941406\n", "buy executed @ 110.5999984741211\n", "sell executed @ 103.73332977294922\n", "buy executed @ 102.86666870117188\n", "buy executed @ 98.47733306884766\n", "buy executed @ 100.89666748046875\n", "sell executed @ 101.267333984375\n", "sell executed @ 109.32866668701172\n", "sell executed @ 106.5999984741211\n", "buy executed @ 111.93000030517578\n", "sell executed @ 94.40066528320312\n", "buy executed @ 95.66666412353516\n", "sell executed @ 100.26667022705078\n", "sell executed @ 100.06666564941406\n", "buy executed @ 99.19999694824219\n", "buy executed @ 101.0\n", "sell executed @ 96.61333465576172\n", "buy executed @ 99.6673355102539\n", "sell executed @ 99.53266906738281\n", "buy executed @ 99.38866424560547\n", "buy executed @ 99.96932983398438\n", "sell executed @ 96.53333282470703\n", "buy executed @ 93.06666564941406\n", "buy executed @ 98.0\n", "sell executed @ 107.4000015258789\n", "sell executed @ 110.99933624267578\n", "sell executed @ 111.80000305175781\n", "sell executed @ 126.59933471679688\n", "sell executed @ 124.33333587646484\n", "buy executed @ 124.04533386230469\n", "buy executed @ 136.3173370361328\n", "sell executed @ 141.7519989013672\n", "sell executed @ 131.65933227539062\n", "buy executed @ 137.3333282470703\n", "sell executed @ 145.36399841308594\n", "sell executed @ 153.00799560546875\n", "sell executed @ 148.20333862304688\n", "buy executed @ 167.3800048828125\n", "sell executed @ 159.663330078125\n", "buy executed @ 135.7433319091797\n", "buy executed @ 134.27000427246094\n", "buy executed @ 118.66666412353516\n", "buy executed @ 118.86666870117188\n", "sell executed @ 128.73666381835938\n", "sell executed @ 127.3133316040039\n", "buy executed @ 126.98332977294922\n", "buy executed @ 145.52000427246094\n", "sell executed @ 146.6233367919922\n", "sell executed @ 138.53334045410156\n", "buy executed @ 149.31333923339844\n", "sell executed @ 151.0433349609375\n", "sell executed @ 143.1999969482422\n", "buy executed @ 135.05332946777344\n", "buy executed @ 121.26667022705078\n", "buy executed @ 131.1566619873047\n", "sell executed @ 141.5399932861328\n", "sell executed @ 138.6666717529297\n", "buy executed @ 140.44000244140625\n", "sell executed @ 146.9199981689453\n", "sell executed @ 140.4633331298828\n", "buy executed @ 141.11666870117188\n", "sell executed @ 141.26333618164062\n", "sell executed @ 139.9566650390625\n", "buy executed @ 146.14666748046875\n", "sell executed @ 143.3766632080078\n", "buy executed @ 147.3333282470703\n", "sell executed @ 147.78334045410156\n", "sell executed @ 149.92666625976562\n", "sell executed @ 150.10333251953125\n", "sell executed @ 151.47999572753906\n", "sell executed @ 148.74667358398438\n", "buy executed @ 143.9166717529297\n", "buy executed @ 140.89999389648438\n", "buy executed @ 147.30667114257812\n", "sell executed @ 140.6133270263672\n", "buy executed @ 137.2100067138672\n", "buy executed @ 141.25332641601562\n", "sell executed @ 138.82666015625\n", "buy executed @ 136.65333557128906\n", "buy executed @ 135.63333129882812\n", "buy executed @ 131.3333282470703\n", "buy executed @ 136.57666015625\n", "sell executed @ 143.5399932861328\n", "sell executed @ 142.76666259765625\n", "buy executed @ 145.36666870117188\n", "sell executed @ 146.5\n", "sell executed @ 140.02999877929688\n", "buy executed @ 138.81666564941406\n", "buy executed @ 138.35000610351562\n", "buy executed @ 136.9499969482422\n", "buy executed @ 136.30999755859375\n", "buy executed @ 153.38999938964844\n", "sell executed @ 149.4499969482422\n", "buy executed @ 164.0\n", "sell executed @ 165.99667358398438\n", "sell executed @ 167.8333282470703\n", "sell executed @ 180.13333129882812\n", "sell executed @ 183.35333251953125\n", "sell executed @ 193.72000122070312\n", "sell executed @ 200.73666381835938\n", "sell executed @ 199.19667053222656\n", "buy executed @ 185.47999572753906\n", "buy executed @ 196.67333984375\n", "sell executed @ 197.00332641601562\n", "sell executed @ 201.63999938964844\n", "sell executed @ 208.50332641601562\n", "sell executed @ 217.89666748046875\n", "sell executed @ 191.4566650390625\n", "buy executed @ 205.00332641601562\n", "sell executed @ 206.3333282470703\n", "sell executed @ 214.42666625976562\n", "sell executed @ 209.41000366210938\n", "buy executed @ 209.39666748046875\n", "buy executed @ 222.96665954589844\n", "sell executed @ 222.0800018310547\n", "buy executed @ 216.0\n", "buy executed @ 210.73333740234375\n", "buy executed @ 214.3300018310547\n", "sell executed @ 224.836669921875\n", "sell executed @ 220.3333282470703\n", "buy executed @ 224.0\n", "sell executed @ 233.3300018310547\n", "sell executed @ 239.82000732421875\n", "sell executed @ 241.22000122070312\n", "sell executed @ 252.8300018310547\n", "sell executed @ 259.2099914550781\n", "sell executed @ 285.3333435058594\n", "sell executed @ 283.1333312988281\n", "buy executed @ 277.0\n", "buy executed @ 284.2533264160156\n", "sell executed @ 281.1300048828125\n", "buy executed @ 284.0\n", "sell executed @ 279.26666259765625\n", "buy executed @ 286.2466735839844\n", "sell executed @ 285.0\n", "buy executed @ 278.10333251953125\n", "buy executed @ 285.0\n", "sell executed @ 297.1266784667969\n", "sell executed @ 290.1166687011719\n", "buy executed @ 273.3333435058594\n", "buy executed @ 276.6666564941406\n", "sell executed @ 271.42999267578125\n", "buy executed @ 281.55999755859375\n", "sell executed @ 292.3399963378906\n", "sell executed @ 285.0\n", "buy executed @ 281.6666564941406\n", "buy executed @ 289.8900146484375\n", "sell executed @ 285.0400085449219\n", "buy executed @ 281.21331787109375\n", "buy executed @ 270.8133239746094\n", "buy executed @ 267.086669921875\n", "buy executed @ 272.6666564941406\n", "sell executed @ 259.6966552734375\n", "buy executed @ 260.29998779296875\n", "sell executed @ 265.0\n", "sell executed @ 254.2133331298828\n", "buy executed @ 220.7100067138672\n", "buy executed @ 237.28334045410156\n", "sell executed @ 242.0500030517578\n", "sell executed @ 233.3333282470703\n", "buy executed @ 230.0366668701172\n", "buy executed @ 239.42666625976562\n", "sell executed @ 229.3300018310547\n", "buy executed @ 218.60000610351562\n", "buy executed @ 208.68666076660156\n", "buy executed @ 200.18333435058594\n", "buy executed @ 202.72666931152344\n", "sell executed @ 233.43333435058594\n", "sell executed @ 233.13333129882812\n", "buy executed @ 223.3333282470703\n", "buy executed @ 231.3633270263672\n", "sell executed @ 234.4499969482422\n", "sell executed @ 218.9566650390625\n", "buy executed @ 228.09666442871094\n", "sell executed @ 215.53334045410156\n", "buy executed @ 228.19667053222656\n", "sell executed @ 225.2566680908203\n", "buy executed @ 222.6366729736328\n", "buy executed @ 204.3333282470703\n", "buy executed @ 213.9566650390625\n", "sell executed @ 205.2133331298828\n", "buy executed @ 200.5833282470703\n", "buy executed @ 215.5399932861328\n", "sell executed @ 229.4566650390625\n", "sell executed @ 235.90333557128906\n", "sell executed @ 230.10000610351562\n", "buy executed @ 229.0\n", "buy executed @ 225.7933349609375\n", "buy executed @ 225.92333984375\n", "sell executed @ 228.56666564941406\n", "sell executed @ 237.56666564941406\n", "sell executed @ 256.8999938964844\n", "sell executed @ 247.6999969482422\n", "buy executed @ 242.88333129882812\n", "buy executed @ 239.86666870117188\n", "buy executed @ 239.13999938964844\n", "buy executed @ 234.92333984375\n", "buy executed @ 247.1666717529297\n", "sell executed @ 239.93333435058594\n", "buy executed @ 247.0\n", "sell executed @ 239.32000732421875\n", "buy executed @ 232.1366729736328\n", "buy executed @ 233.1699981689453\n", "sell executed @ 222.52999877929688\n", "buy executed @ 234.60000610351562\n", "sell executed @ 226.31333923339844\n", "buy executed @ 227.02000427246094\n", "sell executed @ 226.9199981689453\n", "buy executed @ 221.93333435058594\n", "buy executed @ 221.63333129882812\n", "buy executed @ 199.74667358398438\n", "buy executed @ 200.8300018310547\n", "sell executed @ 200.51333618164062\n", "buy executed @ 194.47000122070312\n", "buy executed @ 191.85000610351562\n", "buy executed @ 189.3333282470703\n", "buy executed @ 184.18333435058594\n", "buy executed @ 191.6666717529297\n", "sell executed @ 198.70333862304688\n", "sell executed @ 193.86666870117188\n", "buy executed @ 202.43666076660156\n", "sell executed @ 202.52000427246094\n", "sell executed @ 206.74667358398438\n", "sell executed @ 209.5\n", "sell executed @ 209.26666259765625\n", "buy executed @ 206.7100067138672\n", "buy executed @ 200.60000610351562\n", "buy executed @ 193.23666381835938\n", "buy executed @ 197.27667236328125\n", "sell executed @ 207.6699981689453\n", "sell executed @ 200.72332763671875\n", "buy executed @ 201.2933349609375\n", "sell executed @ 203.41000366210938\n", "sell executed @ 204.07666015625\n", "sell executed @ 205.56333923339844\n", "sell executed @ 199.17999267578125\n", "buy executed @ 200.6300048828125\n", "sell executed @ 204.4566650390625\n", "sell executed @ 208.16000366210938\n", "sell executed @ 206.0833282470703\n", "buy executed @ 210.6666717529297\n", "sell executed @ 224.99667358398438\n", "sell executed @ 229.86000061035156\n", "sell executed @ 223.8800048828125\n", "buy executed @ 228.21665954589844\n", "sell executed @ 226.58999633789062\n", "buy executed @ 227.97332763671875\n", "sell executed @ 226.32666015625\n", "buy executed @ 227.23666381835938\n", "sell executed @ 221.42333984375\n", "buy executed @ 209.4566650390625\n", "buy executed @ 217.72666931152344\n", "sell executed @ 220.73333740234375\n", "sell executed @ 228.77333068847656\n", "sell executed @ 223.5833282470703\n", "buy executed @ 219.4633331298828\n", "buy executed @ 218.22666931152344\n", "buy executed @ 209.9633331298828\n", "buy executed @ 217.3300018310547\n", "sell executed @ 219.8699951171875\n", "sell executed @ 218.81333923339844\n", "buy executed @ 215.45333862304688\n", "buy executed @ 216.99000549316406\n", "sell executed @ 221.13333129882812\n", "sell executed @ 215.6666717529297\n", "buy executed @ 216.59666442871094\n", "sell executed @ 223.9199981689453\n", "sell executed @ 233.3333282470703\n", "sell executed @ 239.6666717529297\n", "sell executed @ 237.0\n", "buy executed @ 238.6666717529297\n", "sell executed @ 237.3000030517578\n", "buy executed @ 236.72332763671875\n", "buy executed @ 237.99667358398438\n", "sell executed @ 237.57000732421875\n", "buy executed @ 235.44667053222656\n", "buy executed @ 241.23666381835938\n", "sell executed @ 235.02333068847656\n", "buy executed @ 224.22000122070312\n", "buy executed @ 223.25\n", "buy executed @ 226.07000732421875\n", "sell executed @ 227.61666870117188\n", "sell executed @ 228.47999572753906\n", "sell executed @ 236.89332580566406\n", "sell executed @ 235.67666625976562\n", "buy executed @ 236.10333251953125\n", "sell executed @ 235.0\n", "buy executed @ 238.24000549316406\n", "sell executed @ 244.3333282470703\n", "sell executed @ 244.69332885742188\n", "sell executed @ 244.8333282470703\n", "sell executed @ 244.0833282470703\n", "buy executed @ 246.6666717529297\n", "sell executed @ 253.86000061035156\n", "sell executed @ 251.1366729736328\n", "buy executed @ 253.1999969482422\n", "sell executed @ 246.73666381835938\n", "buy executed @ 247.52333068847656\n", "sell executed @ 248.3333282470703\n", "sell executed @ 250.94332885742188\n", "sell executed @ 252.38333129882812\n", "sell executed @ 244.85333251953125\n", "buy executed @ 244.92999267578125\n", "sell executed @ 247.8433380126953\n", "sell executed @ 251.6666717529297\n", "sell executed @ 248.6300048828125\n", "buy executed @ 257.7066650390625\n", "sell executed @ 262.3999938964844\n", "sell executed @ 259.9333190917969\n", "buy executed @ 260.3333435058594\n", "sell executed @ 259.4666748046875\n", "buy executed @ 265.5\n", "sell executed @ 261.6000061035156\n", "buy executed @ 258.73333740234375\n", "buy executed @ 261.82000732421875\n", "sell executed @ 265.4033203125\n", "sell executed @ 262.54998779296875\n", "buy executed @ 266.9766540527344\n", "sell executed @ 270.15667724609375\n", "sell executed @ 271.8299865722656\n", "sell executed @ 274.5799865722656\n", "sell executed @ 283.92999267578125\n", "sell executed @ 292.510009765625\n", "sell executed @ 288.45001220703125\n", "buy executed @ 285.3333435058594\n", "buy executed @ 298.5\n", "sell executed @ 316.84332275390625\n", "sell executed @ 341.5633239746094\n", "sell executed @ 346.5533447265625\n", "sell executed @ 356.10333251953125\n", "sell executed @ 360.6199951171875\n", "sell executed @ 381.6666564941406\n", "sell executed @ 386.4533386230469\n", "sell executed @ 392.4433288574219\n", "sell executed @ 411.4700012207031\n", "sell executed @ 409.3333435058594\n", "buy executed @ 383.2633361816406\n", "buy executed @ 391.20001220703125\n", "sell executed @ 336.8033447265625\n", "buy executed @ 367.5899963378906\n", "sell executed @ 349.1666564941406\n", "buy executed @ 339.2099914550781\n", "buy executed @ 334.4366760253906\n", "buy executed @ 354.5033264160156\n", "sell executed @ 368.8500061035156\n", "sell executed @ 366.2900085449219\n", "buy executed @ 387.4433288574219\n", "sell executed @ 389.1700134277344\n", "sell executed @ 360.1300048828125\n", "buy executed @ 366.489990234375\n", "sell executed @ 366.9966735839844\n", "sell executed @ 381.4566650390625\n", "sell executed @ 386.8999938964844\n", "sell executed @ 366.35333251953125\n", "buy executed @ 361.5966796875\n", "buy executed @ 333.836669921875\n", "buy executed @ 348.0666809082031\n", "sell executed @ 350.9033203125\n", "sell executed @ 353.5466613769531\n", "sell executed @ 336.25\n", "buy executed @ 333.6966552734375\n", "buy executed @ 315.0\n", "buy executed @ 317.7366638183594\n", "sell executed @ 331.5\n", "sell executed @ 304.92333984375\n", "buy executed @ 303.5666809082031\n", "buy executed @ 305.6233215332031\n", "sell executed @ 321.88665771484375\n", "sell executed @ 335.6000061035156\n", "sell executed @ 357.8900146484375\n", "sell executed @ 369.8299865722656\n", "sell executed @ 366.21331787109375\n", "buy executed @ 353.77667236328125\n", "buy executed @ 357.8133239746094\n", "sell executed @ 382.5833435058594\n", "sell executed @ 396.51666259765625\n", "sell executed @ 382.2166748046875\n", "buy executed @ 359.0\n", "buy executed @ 360.1233215332031\n", "sell executed @ 333.3333435058594\n", "buy executed @ 351.22332763671875\n", "sell executed @ 359.6166687011719\n", "sell executed @ 369.69000244140625\n", "sell executed @ 339.9599914550781\n", "buy executed @ 342.2033386230469\n", "sell executed @ 347.2366638183594\n", "sell executed @ 336.57666015625\n", "buy executed @ 332.11334228515625\n", "buy executed @ 301.586669921875\n", "buy executed @ 304.73333740234375\n", "sell executed @ 317.4766540527344\n", "sell executed @ 311.1199951171875\n", "buy executed @ 277.1866760253906\n", "buy executed @ 290.9033203125\n", "sell executed @ 311.7366638183594\n", "sell executed @ 309.3933410644531\n", "buy executed @ 294.0\n", "buy executed @ 299.0733337402344\n", "sell executed @ 307.92999267578125\n", "sell executed @ 301.84332275390625\n", "buy executed @ 311.6666564941406\n", "sell executed @ 302.7900085449219\n", "buy executed @ 303.2099914550781\n", "sell executed @ 287.19000244140625\n", "buy executed @ 300.0\n", "sell executed @ 304.6833190917969\n", "sell executed @ 304.4200134277344\n", "buy executed @ 295.3333435058594\n", "buy executed @ 278.0433349609375\n", "buy executed @ 276.80999755859375\n", "buy executed @ 233.4633331298828\n", "buy executed @ 269.74334716796875\n", "sell executed @ 271.6700134277344\n", "sell executed @ 289.8933410644531\n", "sell executed @ 290.7099914550781\n", "sell executed @ 292.92333984375\n", "sell executed @ 283.0333251953125\n", "buy executed @ 285.4333190917969\n", "sell executed @ 265.1766662597656\n", "buy executed @ 279.82666015625\n", "sell executed @ 283.8166809082031\n", "sell executed @ 280.0666809082031\n", "buy executed @ 260.2033386230469\n", "buy executed @ 258.42333984375\n", "buy executed @ 269.6666564941406\n", "sell executed @ 276.9966735839844\n", "sell executed @ 291.4966735839844\n", "sell executed @ 304.99334716796875\n", "sell executed @ 310.0\n", "sell executed @ 326.64666748046875\n", "sell executed @ 336.57666015625\n", "sell executed @ 336.0\n", "buy executed @ 355.0333251953125\n", "sell executed @ 369.3299865722656\n", "sell executed @ 363.72332763671875\n", "buy executed @ 364.8566589355469\n", "sell executed @ 360.3833312988281\n", "buy executed @ 363.1266784667969\n", "sell executed @ 378.76666259765625\n", "sell executed @ 357.8233337402344\n", "buy executed @ 350.7966613769531\n", "buy executed @ 347.7366638183594\n", "buy executed @ 326.79998779296875\n", "buy executed @ 332.5466613769531\n", "sell executed @ 327.02667236328125\n", "buy executed @ 333.0966796875\n", "sell executed @ 329.6766662597656\n", "buy executed @ 335.0199890136719\n", "sell executed @ 343.3333435058594\n", "sell executed @ 358.24334716796875\n", "sell executed @ 338.3033447265625\n", "buy executed @ 326.3233337402344\n", "buy executed @ 331.80999755859375\n", "sell executed @ 299.52667236328125\n", "buy executed @ 299.99334716796875\n", "sell executed @ 300.75\n", "sell executed @ 286.92333984375\n", "buy executed @ 301.05999755859375\n", "sell executed @ 301.3133239746094\n", "sell executed @ 313.00665283203125\n", "sell executed @ 295.6666564941406\n", "buy executed @ 278.8166809082031\n", "buy executed @ 273.10333251953125\n", "buy executed @ 265.0\n", "buy executed @ 233.6666717529297\n", "buy executed @ 257.82666015625\n", "sell executed @ 255.72000122070312\n", "buy executed @ 249.1199951171875\n", "buy executed @ 248.17333984375\n", "buy executed @ 235.6666717529297\n", "buy executed @ 237.99667358398438\n", "sell executed @ 218.33999633789062\n", "buy executed @ 217.8433380126953\n", "buy executed @ 207.9499969482422\n", "buy executed @ 220.47332763671875\n", "sell executed @ 241.0833282470703\n", "sell executed @ 257.9466552734375\n", "sell executed @ 251.72000122070312\n", "buy executed @ 244.1566619873047\n", "buy executed @ 243.22666931152344\n", "buy executed @ 244.35333251953125\n", "sell executed @ 234.0\n", "buy executed @ 240.086669921875\n", "sell executed @ 249.33999633789062\n", "sell executed @ 235.1566619873047\n", "buy executed @ 223.1666717529297\n", "buy executed @ 218.2866668701172\n", "buy executed @ 220.9166717529297\n", "sell executed @ 222.73666381835938\n", "sell executed @ 213.43333435058594\n", "buy executed @ 224.60333251953125\n", "sell executed @ 234.50332641601562\n", "sell executed @ 237.9066619873047\n", "sell executed @ 237.47000122070312\n", "buy executed @ 249.36666870117188\n", "sell executed @ 244.48333740234375\n", "buy executed @ 230.5\n", "buy executed @ 224.50999450683594\n", "buy executed @ 227.0\n", "sell executed @ 223.0\n", "buy executed @ 230.77999877929688\n", "sell executed @ 233.9199981689453\n", "sell executed @ 242.3333282470703\n", "sell executed @ 252.10333251953125\n", "sell executed @ 236.84666442871094\n", "buy executed @ 225.5\n", "buy executed @ 234.89666748046875\n", "sell executed @ 240.0\n", "sell executed @ 244.93666076660156\n", "sell executed @ 245.0\n", "sell executed @ 246.78334045410156\n", "sell executed @ 255.10667419433594\n", "sell executed @ 276.2200012207031\n", "sell executed @ 272.2166748046875\n", "buy executed @ 266.5133361816406\n", "buy executed @ 263.80999755859375\n", "buy executed @ 280.0666809082031\n", "sell executed @ 280.70001220703125\n", "sell executed @ 301.27667236328125\n", "sell executed @ 294.0033264160156\n", "buy executed @ 305.0\n", "sell executed @ 311.0\n", "sell executed @ 302.6700134277344\n", "buy executed @ 295.0\n", "buy executed @ 290.2933349609375\n", "buy executed @ 297.0666809082031\n", "sell executed @ 296.5133361816406\n", "buy executed @ 289.4166564941406\n", "buy executed @ 301.78668212890625\n", "sell executed @ 311.6666564941406\n", "sell executed @ 303.39666748046875\n", "buy executed @ 306.0\n", "sell executed @ 299.0\n", "buy executed @ 291.913330078125\n", "buy executed @ 291.4533386230469\n", "buy executed @ 297.5633239746094\n", "sell executed @ 302.3599853515625\n", "sell executed @ 297.42999267578125\n", "buy executed @ 282.8299865722656\n", "buy executed @ 287.8699951171875\n", "sell executed @ 280.6199951171875\n", "buy executed @ 272.5799865722656\n", "buy executed @ 281.07000732421875\n", "sell executed @ 272.67999267578125\n", "buy executed @ 273.1000061035156\n", "sell executed @ 281.29998779296875\n", "sell executed @ 291.6700134277344\n", "sell executed @ 300.7200012207031\n", "sell executed @ 292.8999938964844\n", "buy executed @ 292.239990234375\n", "buy executed @ 301.8299865722656\n", "sell executed @ 299.6099853515625\n", "buy executed @ 300.0899963378906\n", "sell executed @ 306.9100036621094\n", "sell executed @ 308.2900085449219\n", "sell executed @ 299.8599853515625\n", "buy executed @ 283.0899963378906\n", "buy executed @ 271.8299865722656\n", "buy executed @ 283.8399963378906\n", "sell executed @ 283.0799865722656\n", "buy executed @ 282.760009765625\n", "buy executed @ 266.1499938964844\n", "buy executed @ 254.5\n", "buy executed @ 250.52000427246094\n", "buy executed @ 245.00999450683594\n", "buy executed @ 239.44000244140625\n", "buy executed @ 233.94000244140625\n", "buy executed @ 223.92999267578125\n", "buy executed @ 220.9499969482422\n", "buy executed @ 215.3300018310547\n", "buy executed @ 208.3000030517578\n", "buy executed @ 224.00999450683594\n", "sell executed @ 210.0399932861328\n", "buy executed @ 229.5\n", "sell executed @ 219.8000030517578\n", "buy executed @ 208.27999877929688\n", "buy executed @ 206.4199981689453\n", "buy executed @ 205.82000732421875\n", "buy executed @ 210.10000610351562\n", "sell executed @ 219.39999389648438\n", "sell executed @ 229.77000427246094\n", "sell executed @ 225.39999389648438\n", "buy executed @ 226.19000244140625\n", "sell executed @ 234.0500030517578\n", "sell executed @ 226.0399932861328\n", "buy executed @ 211.36000061035156\n", "buy executed @ 222.60000610351562\n", "sell executed @ 208.64999389648438\n", "buy executed @ 194.02000427246094\n", "buy executed @ 190.77999877929688\n", "buy executed @ 189.89999389648438\n", "buy executed @ 186.0\n", "buy executed @ 192.77000427246094\n", "sell executed @ 195.8800048828125\n", "sell executed @ 191.50999450683594\n", "buy executed @ 183.9600067138672\n", "buy executed @ 185.0500030517578\n", "sell executed @ 175.85000610351562\n", "buy executed @ 168.6300048828125\n", "buy executed @ 173.57000732421875\n", "sell executed @ 185.05999755859375\n", "sell executed @ 179.9600067138672\n", "buy executed @ 184.99000549316406\n", "sell executed @ 182.42999267578125\n", "buy executed @ 197.0800018310547\n", "sell executed @ 191.77999877929688\n", "buy executed @ 189.44000244140625\n", "buy executed @ 181.22000122070312\n", "buy executed @ 175.02999877929688\n", "buy executed @ 172.1999969482422\n", "buy executed @ 173.83999633789062\n", "sell executed @ 176.10000610351562\n", "sell executed @ 174.8699951171875\n", "buy executed @ 159.25\n", "buy executed @ 153.44000244140625\n", "buy executed @ 159.63999938964844\n", "sell executed @ 154.0\n", "buy executed @ 146.0500030517578\n", "buy executed @ 139.33999633789062\n", "buy executed @ 136.0\n", "buy executed @ 126.37000274658203\n", "buy executed @ 117.5\n", "buy executed @ 110.3499984741211\n", "buy executed @ 120.38999938964844\n", "sell executed @ 119.94999694824219\n", "buy executed @ 118.47000122070312\n", "buy executed @ 109.11000061035156\n", "buy executed @ 110.51000213623047\n", "sell executed @ 103.0\n", "buy executed @ 118.95999908447266\n", "sell executed @ 121.06999969482422\n", "sell executed @ 122.08999633789062\n", "sell executed @ 122.55999755859375\n", "sell executed @ 116.55000305175781\n", "buy executed @ 125.69999694824219\n", "sell executed @ 136.55999755859375\n", "sell executed @ 127.26000213623047\n", "buy executed @ 128.67999267578125\n", "sell executed @ 135.8699951171875\n", "sell executed @ 143.0\n", "sell executed @ 141.91000366210938\n", "buy executed @ 159.97000122070312\n", "sell executed @ 162.42999267578125\n", "sell executed @ 178.0500030517578\n", "sell executed @ 164.57000732421875\n", "buy executed @ 173.88999938964844\n", "sell executed @ 187.3300018310547\n", "sell executed @ 183.9499969482422\n", "buy executed @ 193.00999450683594\n", "sell executed @ 196.42999267578125\n", "sell executed @ 196.10000610351562\n", "buy executed @ 207.77999877929688\n", "sell executed @ 202.22999572753906\n", "buy executed @ 194.4199981689453\n", "buy executed @ 191.94000244140625\n", "buy executed @ 211.75999450683594\n", "sell executed @ 210.77999877929688\n", "buy executed @ 199.99000549316406\n", "buy executed @ 204.99000549316406\n", "sell executed @ 197.92999267578125\n", "buy executed @ 203.91000366210938\n", "sell executed @ 196.3300018310547\n", "buy executed @ 202.02999877929688\n", "sell executed @ 210.58999633789062\n", "sell executed @ 206.2100067138672\n", "buy executed @ 186.74000549316406\n", "buy executed @ 194.8000030517578\n", "sell executed @ 198.5399932861328\n", "sell executed @ 191.3800048828125\n", "buy executed @ 185.0399932861328\n", "buy executed @ 180.25\n", "buy executed @ 175.1300048828125\n", "buy executed @ 167.4600067138672\n", "buy executed @ 177.30999755859375\n", "sell executed @ 180.8000030517578\n", "sell executed @ 180.3699951171875\n", "buy executed @ 184.52000427246094\n", "sell executed @ 178.0800018310547\n", "buy executed @ 188.27999877929688\n", "sell executed @ 199.3000030517578\n", "sell executed @ 195.25999450683594\n", "buy executed @ 191.64999389648438\n", "buy executed @ 194.4199981689453\n", "sell executed @ 192.0\n", "buy executed @ 193.1300048828125\n", "sell executed @ 195.5800018310547\n", "sell executed @ 197.52999877929688\n", "sell executed @ 199.91000366210938\n", "sell executed @ 197.32000732421875\n", "buy executed @ 190.52000427246094\n", "buy executed @ 183.0800018310547\n", "buy executed @ 179.94000244140625\n", "buy executed @ 186.69000244140625\n", "sell executed @ 190.74000549316406\n", "sell executed @ 182.9600067138672\n", "buy executed @ 183.9499969482422\n", "sell executed @ 186.32000732421875\n", "sell executed @ 187.14999389648438\n", "sell executed @ 179.10000610351562\n", "buy executed @ 166.1699981689453\n", "buy executed @ 164.8000030517578\n", "buy executed @ 164.64999389648438\n", "buy executed @ 159.82000732421875\n", "buy executed @ 160.2899932861328\n", "sell executed @ 152.63999938964844\n", "buy executed @ 160.89999389648438\n", "sell executed @ 163.1699981689453\n", "sell executed @ 161.8800048828125\n", "buy executed @ 160.00999450683594\n", "buy executed @ 162.7100067138672\n", "sell executed @ 163.97000122070312\n", "sell executed @ 173.72000122070312\n", "sell executed @ 168.9499969482422\n", "buy executed @ 172.5500030517578\n", "sell executed @ 168.6999969482422\n", "buy executed @ 176.07000732421875\n", "sell executed @ 167.66000366210938\n", "buy executed @ 165.64999389648438\n", "buy executed @ 168.41000366210938\n", "sell executed @ 174.22000122070312\n", "sell executed @ 177.1699981689453\n", "sell executed @ 180.6999969482422\n", "sell executed @ 186.1999969482422\n", "sell executed @ 182.22999572753906\n", "buy executed @ 186.5399932861328\n", "sell executed @ 184.6199951171875\n", "buy executed @ 200.10000610351562\n", "sell executed @ 199.77999877929688\n", "buy executed @ 202.58999633789062\n", "sell executed @ 210.14999389648438\n", "sell executed @ 217.8000030517578\n", "sell executed @ 216.13999938964844\n", "buy executed @ 228.0\n", "sell executed @ 224.22000122070312\n", "buy executed @ 249.07000732421875\n", "sell executed @ 247.94000244140625\n", "buy executed @ 253.50999450683594\n", "sell executed @ 260.1700134277344\n", "sell executed @ 248.39999389648438\n", "buy executed @ 258.9200134277344\n", "sell executed @ 261.5\n", "sell executed @ 275.1300048828125\n", "sell executed @ 250.77000427246094\n", "buy executed @ 259.2900085449219\n", "sell executed @ 250.07000732421875\n", "buy executed @ 243.24000549316406\n", "buy executed @ 249.6999969482422\n", "sell executed @ 258.0299987792969\n", "sell executed @ 260.6000061035156\n", "sell executed @ 276.489990234375\n", "sell executed @ 278.82000732421875\n", "sell executed @ 278.0899963378906\n", "buy executed @ 278.42999267578125\n", "sell executed @ 276.4700012207031\n", "buy executed @ 268.6499938964844\n", "buy executed @ 276.3299865722656\n", "sell executed @ 274.5899963378906\n", "buy executed @ 277.010009765625\n", "sell executed @ 286.6300048828125\n", "sell executed @ 290.1499938964844\n", "sell executed @ 296.0400085449219\n", "sell executed @ 279.55999755859375\n", "buy executed @ 268.0\n", "buy executed @ 255.85000610351562\n", "buy executed @ 272.3800048828125\n", "sell executed @ 263.25\n", "buy executed @ 268.30999755859375\n", "sell executed @ 259.8599853515625\n", "buy executed @ 267.4800109863281\n", "sell executed @ 266.260009765625\n", "buy executed @ 255.57000732421875\n", "buy executed @ 252.0399932861328\n", "buy executed @ 260.9700012207031\n", "sell executed @ 251.4499969482422\n", "buy executed @ 247.4499969482422\n", "buy executed @ 250.8699951171875\n", "sell executed @ 245.39999389648438\n", "buy executed @ 241.77000427246094\n", "buy executed @ 235.6999969482422\n", "buy executed @ 238.72999572753906\n", "sell executed @ 228.02000427246094\n", "buy executed @ 226.05999755859375\n", "buy executed @ 214.1199951171875\n", "buy executed @ 221.5500030517578\n", "sell executed @ 240.25\n", "sell executed @ 229.33999633789062\n", "buy executed @ 238.66000366210938\n", "sell executed @ 231.30999755859375\n", "buy executed @ 242.5800018310547\n", "sell executed @ 238.5800018310547\n", "buy executed @ 254.1999969482422\n", "sell executed @ 255.97999572753906\n", "sell executed @ 257.260009765625\n", "sell executed @ 245.0\n", "buy executed @ 255.13999938964844\n", "sell executed @ 245.07000732421875\n", "buy executed @ 251.22000122070312\n", "sell executed @ 264.2699890136719\n", "sell executed @ 270.760009765625\n", "sell executed @ 270.07000732421875\n", "buy executed @ 271.32000732421875\n", "sell executed @ 277.54998779296875\n", "sell executed @ 271.1600036621094\n", "buy executed @ 264.3500061035156\n", "buy executed @ 267.0400085449219\n", "sell executed @ 257.8500061035156\n", "buy executed @ 257.3999938964844\n", "buy executed @ 243.3800048828125\n", "buy executed @ 242.97999572753906\n", "buy executed @ 244.25999450683594\n", "sell executed @ 240.02000427246094\n", "buy executed @ 250.0\n", "sell executed @ 244.80999755859375\n", "buy executed @ 248.61000061035156\n", "sell executed @ 248.13999938964844\n", "buy executed @ 260.0\n", "sell executed @ 253.97999572753906\n", "buy executed @ 255.30999755859375\n", "sell executed @ 257.75\n", "sell executed @ 266.20001220703125\n", "sell executed @ 262.9200134277344\n", "buy executed @ 258.8999938964844\n", "buy executed @ 250.0500030517578\n", "buy executed @ 250.10000610351562\n", "sell executed @ 252.6999969482422\n", "sell executed @ 225.9499969482422\n", "buy executed @ 217.00999450683594\n", "buy executed @ 210.0\n", "buy executed @ 216.5\n", "sell executed @ 215.8800048828125\n", "buy executed @ 211.32000732421875\n", "buy executed @ 210.60000610351562\n", "buy executed @ 209.27999877929688\n", "buy executed @ 196.1199951171875\n", "buy executed @ 204.0399932861328\n", "sell executed @ 212.97000122070312\n", "sell executed @ 221.14999389648438\n", "sell executed @ 223.97999572753906\n", "sell executed @ 219.97999572753906\n", "buy executed @ 223.14999389648438\n", "sell executed @ 219.75\n", "buy executed @ 210.02999877929688\n", "buy executed @ 215.60000610351562\n", "sell executed @ 235.02999877929688\n", "sell executed @ 239.2899932861328\n", "sell executed @ 239.49000549316406\n", "sell executed @ 232.0\n", "buy executed @ 234.0399932861328\n", "sell executed @ 235.0399932861328\n", "sell executed @ 242.0399932861328\n", "sell executed @ 233.75\n", "buy executed @ 236.88999938964844\n", "sell executed @ 236.67999267578125\n", "buy executed @ 249.2100067138672\n", "sell executed @ 245.13999938964844\n", "buy executed @ 233.13999938964844\n", "buy executed @ 235.75\n", "sell executed @ 233.8699951171875\n", "buy executed @ 242.9199981689453\n", "sell executed @ 241.5500030517578\n", "buy executed @ 240.27000427246094\n", "buy executed @ 242.74000549316406\n", "sell executed @ 238.5500030517578\n", "buy executed @ 234.19000244140625\n", "buy executed @ 241.22000122070312\n", "sell executed @ 251.2100067138672\n", "sell executed @ 253.77999877929688\n", "sell executed @ 253.47999572753906\n", "buy executed @ 256.4100036621094\n", "sell executed @ 251.89999389648438\n", "buy executed @ 256.760009765625\n", "sell executed @ 254.49000549316406\n", "buy executed @ 258.3500061035156\n", "sell executed @ 263.6600036621094\n", "sell executed @ 255.10000610351562\n", "buy executed @ 250.0800018310547\n", "buy executed @ 244.97999572753906\n", "buy executed @ 239.25\n", "buy executed @ 236.86000061035156\n", "buy executed @ 236.13999938964844\n", "buy executed @ 238.11000061035156\n", "sell executed @ 235.10000610351562\n", "buy executed @ 230.57000732421875\n", "buy executed @ 220.0800018310547\n", "buy executed @ 215.10000610351562\n", "buy executed @ 214.86000061035156\n", "buy executed @ 216.8800048828125\n", "sell executed @ 209.99000549316406\n", "buy executed @ 212.25999450683594\n", "sell executed @ 211.3000030517578\n", "buy executed @ 211.8800048828125\n", "sell executed @ 189.6999969482422\n", "buy executed @ 185.5\n", "buy executed @ 185.6300048828125\n", "sell executed @ 195.3300018310547\n", "sell executed @ 187.0\n", "buy executed @ 188.5\n", "sell executed @ 185.0399932861328\n", "buy executed @ 184.25999450683594\n", "buy executed @ 177.2100067138672\n", "buy executed @ 188.17999267578125\n", "sell executed @ 189.0\n", "sell executed @ 190.17999267578125\n", "sell executed @ 192.11000061035156\n", "sell executed @ 183.99000549316406\n", "buy executed @ 185.3000030517578\n", "sell executed @ 189.16000366210938\n", "sell executed @ 202.05999755859375\n", "sell executed @ 196.1300048828125\n", "buy executed @ 193.36000061035156\n", "buy executed @ 194.0\n", "sell executed @ 195.30999755859375\n", "sell executed @ 192.2899932861328\n", "buy executed @ 204.0399932861328\n", "sell executed @ 200.4199981689453\n", "buy executed @ 204.17999267578125\n", "sell executed @ 200.52000427246094\n", "buy executed @ 198.72999572753906\n", "buy executed @ 183.0500030517578\n", "buy executed @ 179.99000549316406\n", "buy executed @ 174.35000610351562\n", "buy executed @ 181.5\n", "sell executed @ 175.4499969482422\n", "buy executed @ 177.77000427246094\n", "sell executed @ 173.0500030517578\n", "buy executed @ 167.77000427246094\n", "buy executed @ 163.16000366210938\n", "buy executed @ 170.02000427246094\n", "sell executed @ 172.36000061035156\n", "sell executed @ 173.0\n", "sell executed @ 176.38999938964844\n", "sell executed @ 166.69000244140625\n", "buy executed @ 168.75999450683594\n", "sell executed @ 178.5800018310547\n", "sell executed @ 181.41000366210938\n", "sell executed @ 177.4499969482422\n", "buy executed @ 176.1699981689453\n", "buy executed @ 164.75\n", "buy executed @ 164.02000427246094\n", "buy executed @ 170.07000732421875\n", "sell executed @ 169.0800018310547\n", "buy executed @ 169.33999633789062\n", "sell executed @ 172.91000366210938\n", "sell executed @ 173.0399932861328\n", "sell executed @ 172.5500030517578\n", "buy executed @ 172.33999633789062\n", "buy executed @ 170.24000549316406\n", "buy executed @ 156.74000549316406\n", "buy executed @ 157.63999938964844\n", "sell executed @ 151.25\n", "buy executed @ 148.97000122070312\n", "buy executed @ 140.55999755859375\n", "buy executed @ 143.3300018310547\n", "sell executed @ 162.83999633789062\n", "sell executed @ 158.9600067138672\n", "buy executed @ 168.85000610351562\n", "sell executed @ 188.4199981689453\n", "sell executed @ 186.97999572753906\n", "buy executed @ 182.0\n", "buy executed @ 182.86000061035156\n", "sell executed @ 182.10000610351562\n", "buy executed @ 183.8000030517578\n", "sell executed @ 182.39999389648438\n", "buy executed @ 171.58999633789062\n", "buy executed @ 175.00999450683594\n", "sell executed @ 173.0500030517578\n", "buy executed @ 170.0\n", "buy executed @ 174.5\n", "sell executed @ 179.89999389648438\n", "sell executed @ 174.10000610351562\n", "buy executed @ 173.5500030517578\n", "buy executed @ 177.55999755859375\n", "sell executed @ 175.50999450683594\n", "buy executed @ 182.85000610351562\n", "sell executed @ 181.8000030517578\n", "buy executed @ 174.83999633789062\n", "buy executed @ 176.39999389648438\n", "sell executed @ 174.19000244140625\n", "buy executed @ 178.5800018310547\n", "sell executed @ 178.5\n", "buy executed @ 178.1300048828125\n", "buy executed @ 174.77999877929688\n", "buy executed @ 175.35000610351562\n", "sell executed @ 174.60000610351562\n", "buy executed @ 176.1300048828125\n", "sell executed @ 176.05999755859375\n", "buy executed @ 173.9199981689453\n", "buy executed @ 171.1199951171875\n", "buy executed @ 188.38999938964844\n", "sell executed @ 185.8000030517578\n", "buy executed @ 177.9199981689453\n", "buy executed @ 186.55999755859375\n", "sell executed @ 184.67999267578125\n", "buy executed @ 182.3000030517578\n", "buy executed @ 184.97000122070312\n", "sell executed @ 184.39999389648438\n", "buy executed @ 186.5399932861328\n", "sell executed @ 195.1699981689453\n", "sell executed @ 199.5500030517578\n", "sell executed @ 201.02000427246094\n", "sell executed @ 218.88999938964844\n", "sell executed @ 234.55999755859375\n", "sell executed @ 249.80999755859375\n", "sell executed @ 247.7100067138672\n", "buy executed @ 251.0\n", "sell executed @ 262.79998779296875\n", "sell executed @ 263.29998779296875\n", "sell executed @ 235.8000030517578\n", "buy executed @ 255.97000122070312\n", "sell executed @ 255.30999755859375\n", "buy executed @ 252.72999572753906\n", "buy executed @ 251.08999633789062\n", "buy executed @ 247.7899932861328\n", "buy executed @ 244.2100067138672\n", "buy executed @ 253.60000610351562\n", "sell executed @ 225.4199981689453\n", "buy executed @ 216.8000030517578\n", "buy executed @ 221.19000244140625\n", "sell executed @ 224.89999389648438\n", "sell executed @ 232.25\n", "sell executed @ 227.89999389648438\n", "buy executed @ 227.69000244140625\n", "buy executed @ 214.8800048828125\n", "buy executed @ 185.22000122070312\n", "buy executed @ 200.75\n", "sell executed @ 200.77000427246094\n", "sell executed @ 195.6999969482422\n", "buy executed @ 197.0500030517578\n", "sell executed @ 199.02000427246094\n", "sell executed @ 198.47000122070312\n", "buy executed @ 207.38999938964844\n", "sell executed @ 205.02000427246094\n", "buy executed @ 211.14999389648438\n", "sell executed @ 217.07000732421875\n", "sell executed @ 224.8800048828125\n", "sell executed @ 222.6699981689453\n", "buy executed @ 223.82000732421875\n", "sell executed @ 214.4600067138672\n", "buy executed @ 218.75\n", "sell executed @ 213.25\n", "buy executed @ 209.72000122070312\n", "buy executed @ 209.8000030517578\n", "sell executed @ 208.6300048828125\n", "buy executed @ 215.25999450683594\n", "sell executed @ 210.58999633789062\n", "buy executed @ 223.49000549316406\n", "sell executed @ 232.60000610351562\n", "sell executed @ 216.1999969482422\n", "buy executed @ 220.07000732421875\n", "sell executed @ 224.5500030517578\n", "sell executed @ 224.66000366210938\n", "sell executed @ 228.0\n", "sell executed @ 229.3000030517578\n", "sell executed @ 229.4499969482422\n", "sell executed @ 230.08999633789062\n", "sell executed @ 234.0\n", "sell executed @ 241.52000427246094\n", "sell executed @ 242.61000061035156\n", "sell executed @ 254.0800018310547\n", "sell executed @ 252.5399932861328\n", "buy executed @ 260.6000061035156\n", "sell executed @ 257.3800048828125\n", "buy executed @ 259.0400085449219\n", "sell executed @ 262.6700134277344\n", "sell executed @ 247.5500030517578\n", "buy executed @ 244.47999572753906\n", "buy executed @ 246.69000244140625\n", "sell executed @ 249.0\n", "sell executed @ 243.55999755859375\n", "buy executed @ 243.82000732421875\n", "sell executed @ 241.80999755859375\n", "buy executed @ 220.1300048828125\n", "buy executed @ 220.1300048828125\n", "buy executed @ 221.39999389648438\n", "sell executed @ 221.58999633789062\n", "sell executed @ 220.7100067138672\n", "buy executed @ 218.89999389648438\n", "buy executed @ 217.30999755859375\n", "buy executed @ 217.1300048828125\n", "buy executed @ 244.67999267578125\n", "sell executed @ 256.010009765625\n", "sell executed @ 270.0\n", "sell executed @ 264.510009765625\n", "buy executed @ 258.0400085449219\n", "buy executed @ 257.989990234375\n", "buy executed @ 252.0399932861328\n", "buy executed @ 244.55999755859375\n", "buy executed @ 247.33999633789062\n", "sell executed @ 284.6700134277344\n", "sell executed @ 288.8900146484375\n", "sell executed @ 299.1400146484375\n", "sell executed @ 346.29998779296875\n", "sell executed @ 342.739990234375\n", "buy executed @ 335.8500061035156\n", "buy executed @ 327.69000244140625\n", "buy executed @ 310.57000732421875\n", "buy executed @ 340.7300109863281\n", "sell executed @ 335.760009765625\n", "buy executed @ 345.0\n", "sell executed @ 343.80999755859375\n", "buy executed @ 341.0899963378906\n", "buy executed @ 360.1400146484375\n", "sell executed @ 341.0\n", "buy executed @ 341.79998779296875\n", "sell executed @ 336.0799865722656\n", "buy executed @ 352.3800048828125\n", "sell executed @ 351.79998779296875\n", "buy executed @ 353.0\n", "sell executed @ 359.8699951171875\n", "sell executed @ 377.4200134277344\n", "sell executed @ 397.6099853515625\n", "sell executed @ 392.67999267578125\n", "buy executed @ 409.70001220703125\n", "sell executed @ 424.8399963378906\n", "sell executed @ 420.0\n", "buy executed @ 441.0899963378906\n", "sell executed @ 475.8999938964844\n", "sell executed @ 466.5\n", "buy executed @ 451.8800048828125\n", "buy executed @ 425.510009765625\n", "buy executed @ 431.0\n", "sell executed @ 435.8999938964844\n", "sell executed @ 465.1600036621094\n", "sell executed @ 449.5199890136719\n", "buy executed @ 419.3999938964844\n", "buy executed @ 423.7900085449219\n", "sell executed @ 390.1000061035156\n", "buy executed @ 381.4800109863281\n", "buy executed @ 423.20001220703125\n", "sell executed @ 405.8299865722656\n", "buy executed @ 392.95001220703125\n", "buy executed @ 391.3999938964844\n", "buy executed @ 383.2099914550781\n", "buy executed @ 414.3399963378906\n", "sell executed @ 409.8999938964844\n", "buy executed @ 423.489990234375\n", "sell executed @ 421.5\n", "buy executed @ 432.6400146484375\n", "sell executed @ 416.80999755859375\n", "buy executed @ 416.05999755859375\n", "buy executed @ 414.45001220703125\n", "buy executed @ 394.79998779296875\n", "buy executed @ 396.9100036621094\n", "sell executed @ 395.2099914550781\n", "buy executed @ 410.7799987792969\n", "sell executed @ 401.5299987792969\n", "buy executed @ 386.67999267578125\n", "buy executed @ 382.6300048828125\n", "buy executed @ 387.510009765625\n", "sell executed @ 373.0299987792969\n", "buy executed @ 370.19000244140625\n", "buy executed @ 356.2099914550781\n", "buy executed @ 345.79998779296875\n", "buy executed @ 329.94000244140625\n", "buy executed @ 345.0\n", "sell executed @ 360.6199951171875\n", "sell executed @ 355.010009765625\n", "buy executed @ 354.0\n", "buy executed @ 361.510009765625\n", "sell executed @ 353.44000244140625\n", "buy executed @ 338.1400146484375\n", "buy executed @ 327.0199890136719\n", "buy executed @ 303.7099914550781\n", "buy executed @ 291.1600036621094\n", "buy executed @ 279.5\n", "buy executed @ 300.3399963378906\n", "sell executed @ 270.92999267578125\n", "buy executed @ 272.9200134277344\n", "sell executed @ 272.05999755859375\n", "buy executed @ 259.32000732421875\n", "buy executed @ 252.5399932861328\n", "buy executed @ 225.30999755859375\n", "buy executed @ 247.22000122070312\n", "sell executed @ 248.1300048828125\n", "sell executed @ 247.30999755859375\n", "buy executed @ 245.05999755859375\n", "buy executed @ 228.16000366210938\n", "buy executed @ 231.61000061035156\n", "sell executed @ 233.35000610351562\n", "sell executed @ 234.99000549316406\n", "sell executed @ 258.0799865722656\n", "sell executed @ 283.6000061035156\n", "sell executed @ 282.6600036621094\n", "buy executed @ 272.4800109863281\n", "buy executed @ 275.5799865722656\n", "sell executed @ 249.30999755859375\n", "buy executed @ 263.79998779296875\n", "sell executed @ 254.60000610351562\n", "buy executed @ 265.2900085449219\n", "sell executed @ 255.3800048828125\n", "buy executed @ 223.77999877929688\n", "buy executed @ 245.0\n", "sell executed @ 224.69000244140625\n", "buy executed @ 260.0\n", "sell executed @ 251.83999633789062\n", "buy executed @ 258.3599853515625\n", "sell executed @ 249.91000366210938\n", "buy executed @ 247.61000061035156\n", "buy executed @ 243.47000122070312\n", "buy executed @ 230.25999450683594\n", "buy executed @ 230.9600067138672\n", "sell executed @ 254.86000061035156\n", "sell executed @ 250.5\n", "buy executed @ 261.69000244140625\n", "sell executed @ 288.9800109863281\n", "sell executed @ 285.5\n", "buy executed @ 279.8999938964844\n", "buy executed @ 280.010009765625\n", "sell executed @ 284.8999938964844\n", "sell executed @ 284.57000732421875\n", "buy executed @ 273.1099853515625\n", "buy executed @ 276.8800048828125\n", "sell executed @ 279.6300048828125\n", "sell executed @ 290.2099914550781\n", "sell executed @ 321.989990234375\n", "sell executed @ 320.0\n", "buy executed @ 342.5\n", "sell executed @ 340.3399963378906\n", "buy executed @ 346.239990234375\n", "sell executed @ 336.29998779296875\n", "buy executed @ 347.8699951171875\n", "sell executed @ 344.42999267578125\n", "buy executed @ 331.8999938964844\n", "buy executed @ 337.9200134277344\n", "sell executed @ 347.3500061035156\n", "sell executed @ 364.8399963378906\n", "sell executed @ 365.2900085449219\n", "sell executed @ 355.5199890136719\n", "buy executed @ 343.5\n", "buy executed @ 346.6000061035156\n", "sell executed @ 345.1000061035156\n", "buy executed @ 322.489990234375\n", "buy executed @ 298.8299865722656\n", "buy executed @ 285.9599914550781\n", "buy executed @ 314.94000244140625\n", "sell executed @ 334.3999938964844\n", "sell executed @ 323.0799865722656\n", "buy executed @ 313.9700012207031\n", "buy executed @ 331.2900085449219\n", "sell executed @ 326.0899963378906\n", "buy executed @ 317.30999755859375\n", "buy executed @ 327.95001220703125\n", "sell executed @ 327.5400085449219\n", "buy executed @ 356.1700134277344\n", "sell executed @ 342.70001220703125\n", "buy executed @ 324.6099853515625\n", "buy executed @ 324.510009765625\n"]}, {"data": {"text/plain": ["[<__main__.momentum at 0x220566aed90>]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro.run()"]}, {"cell_type": "code", "execution_count": 9, "id": "f29a32dd-9314-4ea3-b2ce-add507d2de04", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n"]}], "source": ["eur_usd_hourly = yf.download(tickers='EURUSD=X', start='2024-01-01', interval='1h', group_by='column', auto_adjust=False)"]}, {"cell_type": "code", "execution_count": 10, "id": "4e01aa98-8c9d-43c7-a57f-ca4f5d1f33c0", "metadata": {}, "outputs": [], "source": ["if isinstance(eur_usd_hourly.columns, pd.MultiIndex):\n", "    eur_usd_hourly.columns = [col[0] for col in eur_usd_hourly.columns.values]"]}, {"cell_type": "code", "execution_count": 11, "id": "3dfa4373-c782-4ed7-95e5-c8e9d185d179", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 9227 entries, 2024-01-01 18:00:00+00:00 to 2025-06-27 21:00:00+00:00\n", "Data columns (total 6 columns):\n", " #   Column     Non-Null Count  Dtype  \n", "---  ------     --------------  -----  \n", " 0   Adj Close  9227 non-null   float64\n", " 1   Close      9227 non-null   float64\n", " 2   High       9227 non-null   float64\n", " 3   Low        9227 non-null   float64\n", " 4   Open       9227 non-null   float64\n", " 5   Volume     9227 non-null   int64  \n", "dtypes: float64(5), int64(1)\n", "memory usage: 504.6 KB\n"]}], "source": ["eur_usd_hourly.info()"]}, {"cell_type": "code", "execution_count": 12, "id": "fd6ea759-316c-4927-b054-e324e6fc27f9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-01 18:00:00+00:00</th>\n", "      <td>1.105583</td>\n", "      <td>1.105583</td>\n", "      <td>1.105583</td>\n", "      <td>1.105583</td>\n", "      <td>1.105583</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-01 19:00:00+00:00</th>\n", "      <td>1.105339</td>\n", "      <td>1.105339</td>\n", "      <td>1.105583</td>\n", "      <td>1.105339</td>\n", "      <td>1.105583</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-01 20:00:00+00:00</th>\n", "      <td>1.104240</td>\n", "      <td>1.104240</td>\n", "      <td>1.105339</td>\n", "      <td>1.103875</td>\n", "      <td>1.105339</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-01 21:00:00+00:00</th>\n", "      <td>1.105217</td>\n", "      <td>1.105217</td>\n", "      <td>1.105217</td>\n", "      <td>1.104240</td>\n", "      <td>1.104240</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-01 22:00:00+00:00</th>\n", "      <td>1.104728</td>\n", "      <td>1.104728</td>\n", "      <td>1.105094</td>\n", "      <td>1.104606</td>\n", "      <td>1.104850</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 17:00:00+00:00</th>\n", "      <td>1.170549</td>\n", "      <td>1.170549</td>\n", "      <td>1.172608</td>\n", "      <td>1.169864</td>\n", "      <td>1.172333</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 18:00:00+00:00</th>\n", "      <td>1.170275</td>\n", "      <td>1.170275</td>\n", "      <td>1.170686</td>\n", "      <td>1.169180</td>\n", "      <td>1.170686</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 19:00:00+00:00</th>\n", "      <td>1.170960</td>\n", "      <td>1.170960</td>\n", "      <td>1.171234</td>\n", "      <td>1.170275</td>\n", "      <td>1.170275</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 20:00:00+00:00</th>\n", "      <td>1.172196</td>\n", "      <td>1.172196</td>\n", "      <td>1.172745</td>\n", "      <td>1.171097</td>\n", "      <td>1.171097</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 21:00:00+00:00</th>\n", "      <td>1.171921</td>\n", "      <td>1.171921</td>\n", "      <td>1.171921</td>\n", "      <td>1.171921</td>\n", "      <td>1.171921</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9227 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                           <PERSON><PERSON> <PERSON>     Close      High       Low      Open  \\\n", "Datetime                                                                       \n", "2024-01-01 18:00:00+00:00   1.105583  1.105583  1.105583  1.105583  1.105583   \n", "2024-01-01 19:00:00+00:00   1.105339  1.105339  1.105583  1.105339  1.105583   \n", "2024-01-01 20:00:00+00:00   1.104240  1.104240  1.105339  1.103875  1.105339   \n", "2024-01-01 21:00:00+00:00   1.105217  1.105217  1.105217  1.104240  1.104240   \n", "2024-01-01 22:00:00+00:00   1.104728  1.104728  1.105094  1.104606  1.104850   \n", "...                              ...       ...       ...       ...       ...   \n", "2025-06-27 17:00:00+00:00   1.170549  1.170549  1.172608  1.169864  1.172333   \n", "2025-06-27 18:00:00+00:00   1.170275  1.170275  1.170686  1.169180  1.170686   \n", "2025-06-27 19:00:00+00:00   1.170960  1.170960  1.171234  1.170275  1.170275   \n", "2025-06-27 20:00:00+00:00   1.172196  1.172196  1.172745  1.171097  1.171097   \n", "2025-06-27 21:00:00+00:00   1.171921  1.171921  1.171921  1.171921  1.171921   \n", "\n", "                           Volume  \n", "Datetime                           \n", "2024-01-01 18:00:00+00:00       0  \n", "2024-01-01 19:00:00+00:00       0  \n", "2024-01-01 20:00:00+00:00       0  \n", "2024-01-01 21:00:00+00:00       0  \n", "2024-01-01 22:00:00+00:00       0  \n", "...                           ...  \n", "2025-06-27 17:00:00+00:00       0  \n", "2025-06-27 18:00:00+00:00       0  \n", "2025-06-27 19:00:00+00:00       0  \n", "2025-06-27 20:00:00+00:00       0  \n", "2025-06-27 21:00:00+00:00       0  \n", "\n", "[9227 rows x 6 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["eur_usd_hourly"]}, {"cell_type": "code", "execution_count": 13, "id": "175159ad-5f22-410e-b3df-5750cbf274d1", "metadata": {}, "outputs": [], "source": ["df = eur_usd_hourly[['Adj Close']]\n", "df.columns = ['price']"]}, {"cell_type": "code", "execution_count": 14, "id": "74319490-a54e-4c3d-ad2a-71ebceb52f75", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27812\\1029451938.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df.loc[:, 'return'] = np.log(df.price / df.price.shift(1))\n"]}], "source": ["df.loc[:, 'return'] = np.log(df.price / df.price.shift(1))"]}, {"cell_type": "code", "execution_count": 15, "id": "61b7faa9-7b05-477d-9ee1-a985e8f080ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "      <th>return</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-01 18:00:00+00:00</th>\n", "      <td>1.105583</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-01 19:00:00+00:00</th>\n", "      <td>1.105339</td>\n", "      <td>-0.000221</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-01 20:00:00+00:00</th>\n", "      <td>1.104240</td>\n", "      <td>-0.000994</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-01 21:00:00+00:00</th>\n", "      <td>1.105217</td>\n", "      <td>0.000884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-01 22:00:00+00:00</th>\n", "      <td>1.104728</td>\n", "      <td>-0.000442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 17:00:00+00:00</th>\n", "      <td>1.170549</td>\n", "      <td>-0.001640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 18:00:00+00:00</th>\n", "      <td>1.170275</td>\n", "      <td>-0.000234</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 19:00:00+00:00</th>\n", "      <td>1.170960</td>\n", "      <td>0.000585</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 20:00:00+00:00</th>\n", "      <td>1.172196</td>\n", "      <td>0.001054</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-27 21:00:00+00:00</th>\n", "      <td>1.171921</td>\n", "      <td>-0.000234</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9227 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                              price    return\n", "Datetime                                     \n", "2024-01-01 18:00:00+00:00  1.105583       NaN\n", "2024-01-01 19:00:00+00:00  1.105339 -0.000221\n", "2024-01-01 20:00:00+00:00  1.104240 -0.000994\n", "2024-01-01 21:00:00+00:00  1.105217  0.000884\n", "2024-01-01 22:00:00+00:00  1.104728 -0.000442\n", "...                             ...       ...\n", "2025-06-27 17:00:00+00:00  1.170549 -0.001640\n", "2025-06-27 18:00:00+00:00  1.170275 -0.000234\n", "2025-06-27 19:00:00+00:00  1.170960  0.000585\n", "2025-06-27 20:00:00+00:00  1.172196  0.001054\n", "2025-06-27 21:00:00+00:00  1.171921 -0.000234\n", "\n", "[9227 rows x 2 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 16, "id": "74fa05f3-7319-4c58-b765-5db67715f66e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='Datetime'>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x550 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df['return'].groupby(df.index.hour).mean().plot(kind='bar')"]}, {"cell_type": "code", "execution_count": 17, "id": "7df31894-da15-44c0-9c59-387a037ae1d4", "metadata": {}, "outputs": [], "source": ["class HourlyBias(bt.Strategy):\n", "    def __init__(self):\n", "        self.dataclose = self.datas[0].close\n", "    def next(self):\n", "#        print(f'time=  {self.datas[0].datetime.datetime().hour}')\n", "        if self.datas[0].datetime.datetime().hour == 11:\n", "            self.order = self.sell()\n", "            print(f'@time {self.datas[0].datetime.datetime().hour} selling @{self.dataclose[0]}')\n", "        if self.datas[0].datetime.datetime().hour == 13:\n", "            self.order = self.buy()\n", "            print(f'@time {self.datas[0].datetime.datetime().hour} buying @{self.dataclose[0]}')"]}, {"cell_type": "code", "execution_count": 19, "id": "226595fb-0ed4-4108-95f3-921dfd091604", "metadata": {}, "outputs": [], "source": ["data = bt.feeds.PandasData(dataname = df,\n", "                           open = 0,\n", "                           high = 0,\n", "                           low = 0,\n", "                           close = 0,\n", "                           volume =-1,\n", "                           openinterest = -1)"]}, {"cell_type": "code", "execution_count": 20, "id": "a9f130f7-52ff-4012-b4dc-baf93b362009", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro = bt.<PERSON><PERSON><PERSON>()\n", "cerebro.adddata(data)\n", "cerebro.addstrategy(HourlyBias)"]}, {"cell_type": "code", "execution_count": 21, "id": "3e209c40-06c5-4875-8764-8768974a9819", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["@time 11 selling @1.0972130298614502\n", "@time 13 buying @1.0957703590393066\n", "@time 11 selling @1.0922993421554565\n", "@time 13 buying @1.0924185514450073\n", "@time 11 selling @1.095170259475708\n", "@time 13 buying @1.09505033493042\n", "@time 11 selling @1.0917030572891235\n", "@time 13 buying @1.091941475868225\n", "@time 11 selling @1.0940918922424316\n", "@time 13 buying @1.0954102277755737\n", "@time 11 selling @1.0934937000274658\n", "@time 13 buying @1.0945708751678467\n", "@time 11 selling @1.0948106050491333\n", "@time 13 buying @1.0944511890411377\n", "@time 11 selling @1.0981770753860474\n", "@time 13 buying @1.0944511890411377\n", "@time 11 selling @1.0954102277755737\n", "@time 13 buying @1.0958904027938843\n", "@time 11 selling @1.0949305295944214\n", "@time 13 buying @1.095170259475708\n", "@time 11 selling @1.0894432067871094\n", "@time 13 buying @1.0875475406646729\n", "@time 11 selling @1.0882576704025269\n", "@time 13 buying @1.0866022109985352\n", "@time 11 selling @1.0892059803009033\n", "@time 13 buying @1.0854227542877197\n", "@time 11 selling @1.0890873670578003\n", "@time 13 buying @1.088139295578003\n", "@time 11 selling @1.0893245935440063\n", "@time 13 buying @1.089680790901184\n", "@time 11 selling @1.08695650100708\n", "@time 13 buying @1.0877841711044312\n", "@time 11 selling @1.089561939239502\n", "@time 13 buying @1.0928961038589478\n", "@time 11 selling @1.0897995233535767\n", "@time 13 buying @1.088139295578003\n", "@time 11 selling @1.0876659154891968\n", "@time 13 buying @1.0874292850494385\n", "@time 11 selling @1.0824854373931885\n", "@time 13 buying @1.08236825466156\n", "@time 11 selling @1.084481120109558\n", "@time 13 buying @1.0838934183120728\n", "@time 11 selling @1.0842459201812744\n", "@time 13 buying @1.0862481594085693\n", "@time 11 selling @1.08119797706604\n", "@time 13 buying @1.0817828178405762\n", "@time 11 selling @1.0886130332946777\n", "@time 13 buying @1.08119797706604\n", "@time 11 selling @1.0757315158843994\n", "@time 13 buying @1.0739984512329102\n", "@time 11 selling @1.0742292404174805\n", "@time 13 buying @1.073306918144226\n", "@time 11 selling @1.077470064163208\n", "@time 13 buying @1.0773539543151855\n", "@time 11 selling @1.0765421390533447\n", "@time 13 buying @1.074806571006775\n", "@time 11 selling @1.077470064163208\n", "@time 13 buying @1.0786322355270386\n", "@time 11 selling @1.0773539543151855\n", "@time 13 buying @1.076658010482788\n", "@time 11 selling @1.0781670808792114\n", "@time 13 buying @1.0710078477859497\n", "@time 11 selling @1.0711225271224976\n", "@time 13 buying @1.0711225271224976\n", "@time 11 selling @1.0738831758499146\n", "@time 13 buying @1.0771219730377197\n", "@time 11 selling @1.0781670808792114\n", "@time 13 buying @1.0750375986099243\n", "@time 11 selling @1.0780508518218994\n", "@time 13 buying @1.0772379636764526\n", "@time 11 selling @1.0801469087600708\n", "@time 13 buying @1.082602620124817\n", "@time 11 selling @1.0801469087600708\n", "@time 13 buying @1.0813149213790894\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.082602620124817\n", "@time 11 selling @1.0829542875289917\n", "@time 13 buying @1.0835410356521606\n", "@time 11 selling @1.0854227542877197\n", "@time 13 buying @1.086130142211914\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0850694179534912\n", "@time 11 selling @1.0820168256759644\n", "@time 13 buying @1.0830715894699097\n", "@time 11 selling @1.0837758779525757\n", "@time 13 buying @1.0842459201812744\n", "@time 11 selling @1.0821340084075928\n", "@time 13 buying @1.081548810005188\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0851871967315674\n", "@time 11 selling @1.0851871967315674\n", "@time 13 buying @1.0849517583847046\n", "@time 11 selling @1.087902545928955\n", "@time 13 buying @1.0887316465377808\n", "@time 11 selling @1.089680790901184\n", "@time 13 buying @1.0875475406646729\n", "@time 11 selling @1.0936132669448853\n", "@time 13 buying @1.0963709354400635\n", "@time 11 selling @1.0945708751678467\n", "@time 13 buying @1.0921800136566162\n", "@time 11 selling @1.0937328338623047\n", "@time 13 buying @1.0914647579193115\n", "@time 11 selling @1.0945708751678467\n", "@time 13 buying @1.0939722061157227\n", "@time 11 selling @1.0943313837051392\n", "@time 13 buying @1.0915838479995728\n", "@time 11 selling @1.089680790901184\n", "@time 13 buying @1.0889687538146973\n", "@time 11 selling @1.0906314849853516\n", "@time 13 buying @1.0892059803009033\n", "@time 11 selling @1.0853049755096436\n", "@time 13 buying @1.0856584310531616\n", "@time 11 selling @1.0845986604690552\n", "@time 13 buying @1.0848339796066284\n", "@time 11 selling @1.091226577758789\n", "@time 13 buying @1.0888501405715942\n", "@time 11 selling @1.0817828178405762\n", "@time 13 buying @1.0830715894699097\n", "@time 11 selling @1.0830715894699097\n", "@time 13 buying @1.0838934183120728\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0856584310531616\n", "@time 11 selling @1.0822510719299316\n", "@time 13 buying @1.0821340084075928\n", "@time 11 selling @1.0786322355270386\n", "@time 13 buying @1.0817828178405762\n", "@time 11 selling @1.079097867012024\n", "@time 13 buying @1.080263614654541\n", "@time 11 selling @1.0786322355270386\n", "@time 13 buying @1.077702283859253\n", "@time 11 selling @1.0756157636642456\n", "@time 13 buying @1.076774001121521\n", "@time 11 selling @1.0787487030029297\n", "@time 13 buying @1.079214334487915\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.087311029434204\n", "@time 11 selling @1.0838934183120728\n", "@time 13 buying @1.0803803205490112\n", "@time 11 selling @1.0827198028564453\n", "@time 13 buying @1.0854227542877197\n", "@time 11 selling @1.0864840745925903\n", "@time 13 buying @1.088020920753479\n", "@time 11 selling @1.085894227027893\n", "@time 13 buying @1.0757315158843994\n", "@time 11 selling @1.0731916427612305\n", "@time 13 buying @1.073767900466919\n", "@time 11 selling @1.065303087234497\n", "@time 13 buying @1.0645092725753784\n", "@time 11 selling @1.065757155418396\n", "@time 13 buying @1.0639430284500122\n", "@time 11 selling @1.0646226406097412\n", "@time 13 buying @1.063490390777588\n", "@time 11 selling @1.063829779624939\n", "@time 13 buying @1.064736008644104\n", "@time 11 selling @1.0680338144302368\n", "@time 13 buying @1.0654165744781494\n", "@time 11 selling @1.0654165744781494\n", "@time 13 buying @1.066894292831421\n", "@time 11 selling @1.0637166500091553\n", "@time 13 buying @1.0636035203933716\n", "@time 11 selling @1.0676915645599365\n", "@time 13 buying @1.0704345703125\n", "@time 11 selling @1.0691757202148438\n", "@time 13 buying @1.06963312625885\n", "@time 11 selling @1.0721560716629028\n", "@time 13 buying @1.0706638097763062\n", "@time 11 selling @1.071926236152649\n", "@time 13 buying @1.0710078477859497\n", "@time 11 selling @1.071811318397522\n", "@time 13 buying @1.0702054500579834\n", "@time 11 selling @1.0722709894180298\n", "@time 13 buying @1.0705492496490479\n", "@time 11 selling @1.067463755607605\n", "@time 13 buying @1.0686043500900269\n", "@time 11 selling @1.0705492496490479\n", "@time 13 buying @1.0690613985061646\n", "@time 11 selling @1.0749220848083496\n", "@time 13 buying @1.0781670808792114\n", "@time 11 selling @1.077470064163208\n", "@time 13 buying @1.0789813995361328\n", "@time 11 selling @1.0770059823989868\n", "@time 13 buying @1.078399658203125\n", "@time 11 selling @1.0749220848083496\n", "@time 13 buying @1.0752688646316528\n", "@time 11 selling @1.0730764865875244\n", "@time 13 buying @1.076774001121521\n", "@time 11 selling @1.078283429145813\n", "@time 13 buying @1.079097867012024\n", "@time 11 selling @1.079214334487915\n", "@time 13 buying @1.0807305574417114\n", "@time 11 selling @1.079563856124878\n", "@time 13 buying @1.0821340084075928\n", "@time 11 selling @1.0830715894699097\n", "@time 13 buying @1.0838934183120728\n", "@time 11 selling @1.08695650100708\n", "@time 13 buying @1.085894227027893\n", "@time 11 selling @1.0847163200378418\n", "@time 13 buying @1.085894227027893\n", "@time 11 selling @1.0862481594085693\n", "@time 13 buying @1.08695650100708\n", "@time 11 selling @1.0871928930282593\n", "@time 13 buying @1.0853049755096436\n", "@time 11 selling @1.0830715894699097\n", "@time 13 buying @1.0838934183120728\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.0837758779525757\n", "@time 11 selling @1.0845986604690552\n", "@time 13 buying @1.0845986604690552\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0857763290405273\n", "@time 11 selling @1.0887316465377808\n", "@time 13 buying @1.0868383646011353\n", "@time 11 selling @1.0853049755096436\n", "@time 13 buying @1.0841283798217773\n", "@time 11 selling @1.0821340084075928\n", "@time 13 buying @1.0833063125610352\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.087074637413025\n", "@time 11 selling @1.0845986604690552\n", "@time 13 buying @1.0854227542877197\n", "@time 11 selling @1.0864840745925903\n", "@time 13 buying @1.08695650100708\n", "@time 11 selling @1.087074637413025\n", "@time 13 buying @1.0888501405715942\n", "@time 11 selling @1.087074637413025\n", "@time 13 buying @1.0887316465377808\n", "@time 11 selling @1.0901559591293335\n", "@time 13 buying @1.0822510719299316\n", "@time 11 selling @1.0757315158843994\n", "@time 13 buying @1.0743446350097656\n", "@time 11 selling @1.0730764865875244\n", "@time 13 buying @1.0726161003112793\n", "@time 11 selling @1.076774001121521\n", "@time 13 buying @1.0840108394622803\n", "@time 11 selling @1.0786322355270386\n", "@time 13 buying @1.079097867012024\n", "@time 11 selling @1.0697474479675293\n", "@time 13 buying @1.0672358274459839\n", "@time 11 selling @1.071237325668335\n", "@time 13 buying @1.071811318397522\n", "@time 11 selling @1.0720411539077759\n", "@time 13 buying @1.0749220848083496\n", "@time 11 selling @1.0752688646316528\n", "@time 13 buying @1.0746910572052002\n", "@time 11 selling @1.071811318397522\n", "@time 13 buying @1.0726161003112793\n", "@time 11 selling @1.069290041923523\n", "@time 13 buying @1.0687185525894165\n", "@time 11 selling @1.073767900466919\n", "@time 13 buying @1.0745755434036255\n", "@time 11 selling @1.071237325668335\n", "@time 13 buying @1.0704345703125\n", "@time 11 selling @1.0697474479675293\n", "@time 13 buying @1.0678056478500366\n", "@time 11 selling @1.06963312625885\n", "@time 13 buying @1.0725010633468628\n", "@time 11 selling @1.0699764490127563\n", "@time 13 buying @1.0702054500579834\n", "@time 11 selling @1.0752688646316528\n", "@time 13 buying @1.0757315158843994\n", "@time 11 selling @1.0726161003112793\n", "@time 13 buying @1.0745755434036255\n", "@time 11 selling @1.0760787725448608\n", "@time 13 buying @1.0793308019638062\n", "@time 11 selling @1.0800302028656006\n", "@time 13 buying @1.0804970264434814\n", "@time 11 selling @1.0822510719299316\n", "@time 13 buying @1.0821340084075928\n", "@time 11 selling @1.0836584568023682\n", "@time 13 buying @1.0843634605407715\n", "@time 11 selling @1.0816657543182373\n", "@time 13 buying @1.0824854373931885\n", "@time 11 selling @1.0829542875289917\n", "@time 13 buying @1.0827198028564453\n", "@time 11 selling @1.085894227027893\n", "@time 13 buying @1.0890873670578003\n", "@time 11 selling @1.0889687538146973\n", "@time 13 buying @1.0897995233535767\n", "@time 11 selling @1.090869426727295\n", "@time 13 buying @1.0918222665786743\n", "@time 11 selling @1.0905125141143799\n", "@time 13 buying @1.0886130332946777\n", "@time 11 selling @1.0938525199890137\n", "@time 13 buying @1.0940918922424316\n", "@time 11 selling @1.0930156707763672\n", "@time 13 buying @1.0914647579193115\n", "@time 11 selling @1.089561939239502\n", "@time 13 buying @1.0884946584701538\n", "@time 11 selling @1.0887316465377808\n", "@time 13 buying @1.0883761644363403\n", "@time 11 selling @1.0860122442245483\n", "@time 13 buying @1.0851871967315674\n", "@time 11 selling @1.0849517583847046\n", "@time 13 buying @1.0857763290405273\n", "@time 11 selling @1.0848339796066284\n", "@time 13 buying @1.0842459201812744\n", "@time 11 selling @1.0863661766052246\n", "@time 13 buying @1.0868383646011353\n", "@time 11 selling @1.0821340084075928\n", "@time 13 buying @1.0814317464828491\n", "@time 11 selling @1.0828369855880737\n", "@time 13 buying @1.0814317464828491\n", "@time 11 selling @1.0831888914108276\n", "@time 13 buying @1.0845986604690552\n", "@time 11 selling @1.0794472694396973\n", "@time 13 buying @1.0807305574417114\n", "@time 11 selling @1.0834236145019531\n", "@time 13 buying @1.090869426727295\n", "@time 11 selling @1.096611499786377\n", "@time 13 buying @1.0981770753860474\n", "@time 11 selling @1.091941475868225\n", "@time 13 buying @1.0921800136566162\n", "@time 11 selling @1.0926573276519775\n", "@time 13 buying @1.0920606851577759\n", "@time 11 selling @1.0933741331100464\n", "@time 13 buying @1.0894432067871094\n", "@time 11 selling @1.091941475868225\n", "@time 13 buying @1.0925379991531372\n", "@time 11 selling @1.0928961038589478\n", "@time 13 buying @1.0928961038589478\n", "@time 11 selling @1.0933741331100464\n", "@time 13 buying @1.0962508916854858\n", "@time 11 selling @1.1025358438491821\n", "@time 13 buying @1.1041183471679688\n", "@time 11 selling @1.1015641689300537\n", "@time 13 buying @1.0969723463058472\n", "@time 11 selling @1.0995051860809326\n", "@time 13 buying @1.100473165512085\n", "@time 11 selling @1.1046061515808105\n", "@time 13 buying @1.1047282218933105\n", "@time 11 selling @1.1085245609283447\n", "@time 13 buying @1.1103708744049072\n", "@time 11 selling @1.1118524074554443\n", "@time 13 buying @1.1119760274887085\n", "@time 11 selling @1.1140819787979126\n", "@time 13 buying @1.1124708652496338\n", "@time 11 selling @1.1123470067977905\n", "@time 13 buying @1.1117286682128906\n", "@time 11 selling @1.1168192625045776\n", "@time 13 buying @1.1163206100463867\n", "@time 11 selling @1.1163206100463867\n", "@time 13 buying @1.117193579673767\n", "@time 11 selling @1.1122233867645264\n", "@time 13 buying @1.1113580465316772\n", "@time 11 selling @1.1098779439926147\n", "@time 13 buying @1.1085245609283447\n", "@time 11 selling @1.1085245609283447\n", "@time 13 buying @1.1075423955917358\n", "@time 11 selling @1.1063170433044434\n", "@time 13 buying @1.107051968574524\n", "@time 11 selling @1.1039965152740479\n", "@time 13 buying @1.1065618991851807\n", "@time 11 selling @1.1053388118743896\n", "@time 13 buying @1.1060723066329956\n", "@time 11 selling @1.1097548007965088\n", "@time 13 buying @1.110987663269043\n", "@time 11 selling @1.1111111640930176\n", "@time 13 buying @1.1086474657058716\n", "@time 11 selling @1.1047282218933105\n", "@time 13 buying @1.1049723625183105\n", "@time 11 selling @1.103265643119812\n", "@time 13 buying @1.102292776107788\n", "@time 11 selling @1.1047282218933105\n", "@time 13 buying @1.1015641689300537\n", "@time 11 selling @1.1016855239868164\n", "@time 13 buying @1.1039965152740479\n", "@time 11 selling @1.1087703704833984\n", "@time 13 buying @1.1097548007965088\n", "@time 11 selling @1.1127183437347412\n", "@time 13 buying @1.113089919090271\n", "@time 11 selling @1.1140819787979126\n", "@time 13 buying @1.111605167388916\n", "@time 11 selling @1.1125946044921875\n", "@time 13 buying @1.1138337850570679\n", "@time 11 selling @1.116071343421936\n", "@time 13 buying @1.1124708652496338\n", "@time 11 selling @1.1164453029632568\n", "@time 13 buying @1.1158223152160645\n", "@time 11 selling @1.1122233867645264\n", "@time 13 buying @1.1132138967514038\n", "@time 11 selling @1.1132138967514038\n", "@time 13 buying @1.1142061948776245\n", "@time 11 selling @1.1199462413787842\n", "@time 13 buying @1.1199462413787842\n", "@time 11 selling @1.1158223152160645\n", "@time 13 buying @1.1143302917480469\n", "@time 11 selling @1.1164453029632568\n", "@time 13 buying @1.1188185214996338\n", "@time 11 selling @1.1200717687606812\n", "@time 13 buying @1.1173185110092163\n", "@time 11 selling @1.1087703704833984\n", "@time 13 buying @1.1075423955917358\n", "@time 11 selling @1.1079105138778687\n", "@time 13 buying @1.1054610013961792\n", "@time 11 selling @1.1046061515808105\n", "@time 13 buying @1.1041183471679688\n", "@time 11 selling @1.1035091876983643\n", "@time 13 buying @1.0976948738098145\n", "@time 11 selling @1.098056435585022\n", "@time 13 buying @1.0982975959777832\n", "@time 11 selling @1.0990219116210938\n", "@time 13 buying @1.0981770753860474\n", "@time 11 selling @1.0957703590393066\n", "@time 13 buying @1.095650315284729\n", "@time 11 selling @1.0934937000274658\n", "@time 13 buying @1.0939722061157227\n", "@time 11 selling @1.0934937000274658\n", "@time 13 buying @1.0942115783691406\n", "@time 11 selling @1.0917030572891235\n", "@time 13 buying @1.0925379991531372\n", "@time 11 selling @1.090274691581726\n", "@time 13 buying @1.091226577758789\n", "@time 11 selling @1.0897995233535767\n", "@time 13 buying @1.0892059803009033\n", "@time 11 selling @1.0866022109985352\n", "@time 13 buying @1.0835410356521606\n", "@time 11 selling @1.0850694179534912\n", "@time 13 buying @1.085894227027893\n", "@time 11 selling @1.085894227027893\n", "@time 13 buying @1.0850694179534912\n", "@time 11 selling @1.0818997621536255\n", "@time 13 buying @1.080964207649231\n", "@time 11 selling @1.076658010482788\n", "@time 13 buying @1.077818512916565\n", "@time 11 selling @1.0797970294952393\n", "@time 13 buying @1.080263614654541\n", "@time 11 selling @1.0824854373931885\n", "@time 13 buying @1.0835410356521606\n", "@time 11 selling @1.0822510719299316\n", "@time 13 buying @1.08236825466156\n", "@time 11 selling @1.080263614654541\n", "@time 13 buying @1.079097867012024\n", "@time 11 selling @1.0827198028564453\n", "@time 13 buying @1.0845986604690552\n", "@time 11 selling @1.087074637413025\n", "@time 13 buying @1.0862481594085693\n", "@time 11 selling @1.087074637413025\n", "@time 13 buying @1.0871928930282593\n", "@time 11 selling @1.0917030572891235\n", "@time 13 buying @1.090869426727295\n", "@time 11 selling @1.090274691581726\n", "@time 13 buying @1.089561939239502\n", "@time 11 selling @1.0710078477859497\n", "@time 13 buying @1.0713521242141724\n", "@time 11 selling @1.0761945247650146\n", "@time 13 buying @1.079563856124878\n", "@time 11 selling @1.078515887260437\n", "@time 13 buying @1.0757315158843994\n", "@time 11 selling @1.066439151763916\n", "@time 13 buying @1.0650761127471924\n", "@time 11 selling @1.0622477531433105\n", "@time 13 buying @1.0624734163284302\n", "@time 11 selling @1.064056158065796\n", "@time 13 buying @1.063829779624939\n", "@time 11 selling @1.0532968044281006\n", "@time 13 buying @1.0531858205795288\n", "@time 11 selling @1.0579770803451538\n", "@time 13 buying @1.0561892986297607\n", "@time 11 selling @1.0545185804367065\n", "@time 13 buying @1.0569707155227661\n", "@time 11 selling @1.0564124584197998\n", "@time 13 buying @1.0578652620315552\n", "@time 11 selling @1.0558546781539917\n", "@time 13 buying @1.0558546781539917\n", "@time 11 selling @1.052188515663147\n", "@time 13 buying @1.0537407398223877\n", "@time 11 selling @1.0416667461395264\n", "@time 13 buying @1.0431879758834839\n", "@time 11 selling @1.048218011856079\n", "@time 13 buying @1.0530749559402466\n", "@time 11 selling @1.0515247583389282\n", "@time 13 buying @1.0504201650619507\n", "@time 11 selling @1.0528532266616821\n", "@time 13 buying @1.057305932044983\n", "@time 11 selling @1.05507493019104\n", "@time 13 buying @1.053629755973816\n", "@time 11 selling @1.0563008785247803\n", "@time 13 buying @1.0563008785247803\n", "@time 11 selling @1.052520751953125\n", "@time 13 buying @1.0507513284683228\n", "@time 11 selling @1.0526316165924072\n", "@time 13 buying @1.052520751953125\n", "@time 11 selling @1.0508617162704468\n", "@time 13 buying @1.048657774925232\n", "@time 11 selling @1.053851842880249\n", "@time 13 buying @1.056747317314148\n", "@time 11 selling @1.0590976476669312\n", "@time 13 buying @1.060895323753357\n", "@time 11 selling @1.0571942329406738\n", "@time 13 buying @1.0579770803451538\n", "@time 11 selling @1.0534077882766724\n", "@time 13 buying @1.0522992610931396\n", "@time 11 selling @1.0506409406661987\n", "@time 13 buying @1.0527424812316895\n", "@time 11 selling @1.0505305528640747\n", "@time 13 buying @1.0485477447509766\n", "@time 11 selling @1.0492078065872192\n", "@time 13 buying @1.0515247583389282\n", "@time 11 selling @1.0493179559707642\n", "@time 13 buying @1.0507513284683228\n", "@time 11 selling @1.049428105354309\n", "@time 13 buying @1.0506409406661987\n", "@time 11 selling @1.0497585535049438\n", "@time 13 buying @1.049428105354309\n", "@time 11 selling @1.0405826568603516\n", "@time 13 buying @1.0402579307556152\n", "@time 11 selling @1.0395010709762573\n", "@time 13 buying @1.0411244630813599\n", "@time 11 selling @1.0401498079299927\n", "@time 13 buying @1.0403661727905273\n", "@time 11 selling @1.039717197418213\n", "@time 13 buying @1.0405826568603516\n", "@time 11 selling @1.0403661727905273\n", "@time 13 buying @1.0405826568603516\n", "@time 11 selling @1.0441683530807495\n", "@time 13 buying @1.0440592765808105\n", "@time 11 selling @1.0450413227081299\n", "@time 13 buying @1.0393929481506348\n", "@time 11 selling @1.0412328243255615\n", "@time 13 buying @1.0396090745925903\n", "@time 11 selling @1.0333781242370605\n", "@time 13 buying @1.0317788124084473\n", "@time 11 selling @1.0300782918930054\n", "@time 13 buying @1.0295480489730835\n", "@time 11 selling @1.0423181056976318\n", "@time 13 buying @1.0418837070465088\n", "@time 11 selling @1.0416667461395264\n", "@time 13 buying @1.039717197418213\n", "@time 11 selling @1.0297601222991943\n", "@time 13 buying @1.0306090116500854\n", "@time 11 selling @1.0302904844284058\n", "@time 13 buying @1.0308215618133545\n", "@time 11 selling @1.0305029153823853\n", "@time 13 buying @1.02574622631073\n", "@time 11 selling @1.0198878049850464\n", "@time 13 buying @1.0211374759674072\n", "@time 11 selling @1.0258514881134033\n", "@time 13 buying @1.0255358219146729\n", "@time 11 selling @1.0303966999053955\n", "@time 13 buying @1.0347682237625122\n", "@time 11 selling @1.0293360948562622\n", "@time 13 buying @1.0282776355743408\n", "@time 11 selling @1.0307153463363647\n", "@time 13 buying @1.0291242599487305\n", "@time 11 selling @1.0328444242477417\n", "@time 13 buying @1.0400415658950806\n", "@time 11 selling @1.0356254577636719\n", "@time 13 buying @1.037882685661316\n", "@time 11 selling @1.0446046590805054\n", "@time 13 buying @1.0437324047088623\n", "@time 11 selling @1.040474534034729\n", "@time 13 buying @1.0413411855697632\n", "@time 11 selling @1.0487676858901978\n", "@time 13 buying @1.047120451927185\n", "@time 11 selling @1.0515247583389282\n", "@time 13 buying @1.0524100065231323\n", "@time 11 selling @1.0427528619766235\n", "@time 13 buying @1.0427528619766235\n", "@time 11 selling @1.040474534034729\n", "@time 13 buying @1.0390689373016357\n", "@time 11 selling @1.0395010709762573\n", "@time 13 buying @1.0459157228469849\n", "@time 11 selling @1.0377750396728516\n", "@time 13 buying @1.0385295152664185\n", "@time 11 selling @1.025430679321289\n", "@time 13 buying @1.0265886783599854\n", "@time 11 selling @1.0325244665145874\n", "@time 13 buying @1.0341261625289917\n", "@time 11 selling @1.041775107383728\n", "@time 13 buying @1.0411244630813599\n", "@time 11 selling @1.0366991758346558\n", "@time 13 buying @1.0362694263458252\n", "@time 11 selling @1.0385295152664185\n", "@time 13 buying @1.0370217561721802\n", "@time 11 selling @1.032417893409729\n", "@time 13 buying @1.0323113203048706\n", "@time 11 selling @1.032417893409729\n", "@time 13 buying @1.032417893409729\n", "@time 11 selling @1.0377750396728516\n", "@time 13 buying @1.033698558807373\n", "@time 11 selling @1.0405826568603516\n", "@time 13 buying @1.0396090745925903\n", "@time 11 selling @1.0470107793807983\n", "@time 13 buying @1.0490977764129639\n", "@time 11 selling @1.0485477447509766\n", "@time 13 buying @1.0476689338684082\n", "@time 11 selling @1.0465724468231201\n", "@time 13 buying @1.0447137355804443\n", "@time 11 selling @1.0427528619766235\n", "@time 13 buying @1.043623447418213\n", "@time 11 selling @1.0440592765808105\n", "@time 13 buying @1.0450413227081299\n", "@time 11 selling @1.0472300052642822\n", "@time 13 buying @1.0477787256240845\n", "@time 11 selling @1.0472300052642822\n", "@time 13 buying @1.0470107793807983\n", "@time 11 selling @1.0492078065872192\n", "@time 13 buying @1.0503098964691162\n", "@time 11 selling @1.049428105354309\n", "@time 13 buying @1.048657774925232\n", "@time 11 selling @1.047998309135437\n", "@time 13 buying @1.0426441431045532\n", "@time 11 selling @1.0400415658950806\n", "@time 13 buying @1.0410159826278687\n", "@time 11 selling @1.045478343963623\n", "@time 13 buying @1.0475592613220215\n", "@time 11 selling @1.0547411441802979\n", "@time 13 buying @1.054185152053833\n", "@time 11 selling @1.0680338144302368\n", "@time 13 buying @1.0720411539077759\n", "@time 11 selling @1.080263614654541\n", "@time 13 buying @1.0828369855880737\n", "@time 11 selling @1.0851871967315674\n", "@time 13 buying @1.0849517583847046\n", "@time 11 selling @1.0855406522750854\n", "@time 13 buying @1.0840108394622803\n", "@time 11 selling @1.090274691581726\n", "@time 13 buying @1.0921800136566162\n", "@time 11 selling @1.090274691581726\n", "@time 13 buying @1.0882576704025269\n", "@time 11 selling @1.0850694179534912\n", "@time 13 buying @1.0853049755096436\n", "@time 11 selling @1.0897995233535767\n", "@time 13 buying @1.0876659154891968\n", "@time 11 selling @1.0906314849853516\n", "@time 13 buying @1.090869426727295\n", "@time 11 selling @1.091941475868225\n", "@time 13 buying @1.0914647579193115\n", "@time 11 selling @1.0911074876785278\n", "@time 13 buying @1.0897995233535767\n", "@time 11 selling @1.0853049755096436\n", "@time 13 buying @1.0828369855880737\n", "@time 11 selling @1.0854227542877197\n", "@time 13 buying @1.0834236145019531\n", "@time 11 selling @1.0837758779525757\n", "@time 13 buying @1.07991361618042\n", "@time 11 selling @1.0820168256759644\n", "@time 13 buying @1.0818997621536255\n", "@time 11 selling @1.079563856124878\n", "@time 13 buying @1.07758629322052\n", "@time 11 selling @1.0788650512695312\n", "@time 13 buying @1.078399658203125\n", "@time 11 selling @1.07758629322052\n", "@time 13 buying @1.0814317464828491\n", "@time 11 selling @1.08236825466156\n", "@time 13 buying @1.08119797706604\n", "@time 11 selling @1.078399658203125\n", "@time 13 buying @1.079097867012024\n", "@time 11 selling @1.0804970264434814\n", "@time 13 buying @1.0831888914108276\n", "@time 11 selling @1.109139323234558\n", "@time 13 buying @1.1088933944702148\n", "@time 11 selling @1.1060723066329956\n", "@time 13 buying @1.1014429330825806\n", "@time 11 selling @1.0970927476882935\n", "@time 13 buying @1.0942115783691406\n", "@time 11 selling @1.0937328338623047\n", "@time 13 buying @1.0939722061157227\n", "@time 11 selling @1.1036310195922852\n", "@time 13 buying @1.1047282218933105\n", "@time 11 selling @1.1075423955917358\n", "@time 13 buying @1.113089919090271\n", "@time 11 selling @1.1328877210617065\n", "@time 13 buying @1.1364928483963013\n", "@time 11 selling @1.1385631561279297\n", "@time 13 buying @1.133144497871399\n", "@time 11 selling @1.1328877210617065\n", "@time 13 buying @1.131861925125122\n", "@time 11 selling @1.1364928483963013\n", "@time 13 buying @1.1368803977966309\n", "@time 11 selling @1.1361054182052612\n", "@time 13 buying @1.1363636255264282\n", "@time 11 selling @1.1384334564208984\n", "@time 13 buying @1.1383039951324463\n", "@time 11 selling @1.1547343730926514\n", "@time 13 buying @1.1524720191955566\n", "@time 11 selling @1.1488970518112183\n", "@time 13 buying @1.1479737758636475\n", "@time 11 selling @1.141291856765747\n", "@time 13 buying @1.134429931640625\n", "@time 11 selling @1.1375269889831543\n", "@time 13 buying @1.138044834136963\n", "@time 11 selling @1.1353315114974976\n", "@time 13 buying @1.1357183456420898\n", "@time 11 selling @1.1354604959487915\n", "@time 13 buying @1.1368803977966309\n", "@time 11 selling @1.1376564502716064\n", "@time 13 buying @1.1401208639144897\n", "@time 11 selling @1.1377859115600586\n", "@time 13 buying @1.1371389627456665\n", "@time 11 selling @1.132502794265747\n", "@time 13 buying @1.132502794265747\n", "@time 11 selling @1.1349449157714844\n", "@time 13 buying @1.1376564502716064\n", "@time 11 selling @1.1345586776733398\n", "@time 13 buying @1.135589361190796\n", "@time 11 selling @1.1328877210617065\n", "@time 13 buying @1.1354604959487915\n", "@time 11 selling @1.1361054182052612\n", "@time 13 buying @1.1371389627456665\n", "@time 11 selling @1.1299434900283813\n", "@time 13 buying @1.1309658288955688\n", "@time 11 selling @1.1257457733154297\n", "@time 13 buying @1.1262530088424683\n", "@time 11 selling @1.1112345457077026\n", "@time 13 buying @1.1123470067977905\n", "@time 11 selling @1.110987663269043\n", "@time 13 buying @1.1138337850570679\n", "@time 11 selling @1.121956706047058\n", "@time 13 buying @1.1225864887237549\n", "@time 11 selling @1.1184431314468384\n", "@time 13 buying @1.1203227043151855\n", "@time 11 selling @1.1196954250335693\n", "@time 13 buying @1.1204482316970825\n", "@time 11 selling @1.1275228261947632\n", "@time 13 buying @1.1253657341003418\n", "@time 11 selling @1.124985933303833\n", "@time 13 buying @1.1248594522476196\n", "@time 11 selling @1.1326310634613037\n", "@time 13 buying @1.1341726779937744\n", "@time 11 selling @1.1312217712402344\n", "@time 13 buying @1.1287955045700073\n", "@time 11 selling @1.1330161094665527\n", "@time 13 buying @1.1319899559020996\n", "@time 11 selling @1.1379153728485107\n", "@time 13 buying @1.1384334564208984\n", "@time 11 selling @1.1354604959487915\n", "@time 13 buying @1.1361054182052612\n", "@time 11 selling @1.1321182250976562\n", "@time 13 buying @1.1309658288955688\n", "@time 11 selling @1.1273956298828125\n", "@time 13 buying @1.1354604959487915\n", "@time 11 selling @1.1341726779937744\n", "@time 13 buying @1.1322463750839233\n", "@time 11 selling @1.1416828632354736\n", "@time 13 buying @1.1423349380493164\n", "@time 11 selling @1.139211654663086\n", "@time 13 buying @1.1389521360397339\n", "@time 11 selling @1.1383039951324463\n", "@time 13 buying @1.1402509212493896\n", "@time 11 selling @1.1428571939468384\n", "@time 13 buying @1.148369312286377\n", "@time 11 selling @1.1420739889144897\n", "@time 13 buying @1.1397310495376587\n", "@time 11 selling @1.1420739889144897\n", "@time 13 buying @1.1402509212493896\n", "@time 11 selling @1.1431183815002441\n", "@time 13 buying @1.144688606262207\n", "@time 11 selling @1.1437721252441406\n", "@time 13 buying @1.147446870803833\n", "@time 11 selling @1.1592857837677002\n", "@time 13 buying @1.1588828563690186\n", "@time 11 selling @1.151410460472107\n", "@time 13 buying @1.1528706550598145\n", "@time 11 selling @1.1575413942337036\n", "@time 13 buying @1.1607661247253418\n", "@time 11 selling @1.1564704179763794\n", "@time 13 buying @1.156336784362793\n", "@time 11 selling @1.151012897491455\n", "@time 13 buying @1.1492931842803955\n", "@time 11 selling @1.148369312286377\n", "@time 13 buying @1.1475785970687866\n", "@time 11 selling @1.1526048183441162\n", "@time 13 buying @1.1522064208984375\n", "@time 11 selling @1.1477103233337402\n", "@time 13 buying @1.1492931842803955\n", "@time 11 selling @1.161170482635498\n", "@time 13 buying @1.1594202518463135\n", "@time 11 selling @1.1603620052337646\n", "@time 13 buying @1.160496711730957\n", "@time 11 selling @1.169864296913147\n", "@time 13 buying @1.172195553779602\n", "@time 11 selling @1.171783447265625\n", "@time 13 buying @1.1732958555221558\n"]}, {"data": {"text/plain": ["[<__main__.<PERSON><PERSON><PERSON><PERSON> at 0x2201dc9e010>]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro.run()"]}, {"cell_type": "code", "execution_count": 22, "id": "af62ce74-18f8-4afa-bccb-ed5ea41afde8", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[<Figure size 800x550 with 3 Axes>]]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import matplotlib.pyplot as plt\n", "plt.show()\n", "\n", "%matplotlib inline\n", "cerebro.plot(iplot=False, volume=False)"]}, {"cell_type": "code", "execution_count": null, "id": "38deaa99-3d32-480c-b9c5-205d5212dab8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3108c0b7-d262-480e-9bc3-41526065160e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}