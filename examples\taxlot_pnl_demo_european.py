#!/usr/bin/env python3
"""
European Tax Lot Aware PnL Demonstration

This demo showcases the tax lot tracking functionality adapted for European markets.
Key differences from US version:
- No wash sale rules (European markets don't have this restriction)
- Focus on capital gains tax optimization
- European market scenarios and regulations
- Multi-currency trading common in European markets
- Different settlement periods (T+2 standard)

European Tax Considerations:
- Capital gains tax rates vary by country
- No wash sale restrictions - can immediately rebuy after loss sales
- Different holding periods for long-term vs short-term gains
- Cross-border trading within EU
- Currency hedging considerations
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from backtrader.position import Position
from datetime import datetime, timedelta


def demo_basic_pnl_comparison():
    """Basic comparison between average price and tax lot PnL methods"""
    
    print("\n" + "=" * 70)
    print("DEMO 1: Basic Tax Lot Aware PnL Comparison (European Markets)")
    print("=" * 70)
    
    position = Position()
    
    # Build a position with different entry points (common European scenario)
    trades = [
        (100, 50.0, "Initial purchase at peak"),
        (200, 30.0, "Double down after 40% drop"),
        (100, 45.0, "Buy more on recovery"),
        (100, 25.0, "Buy the dip again"),
    ]
    
    print("\nBuilding Position:")
    print("-" * 50)
    for i, (qty, price, desc) in enumerate(trades, 1):
        position.update(qty, price, datetime.now())
        print(f"{i}. {desc}: {qty} shares @ €{price:.2f}")
    
    print(f"\nFinal Position: {position.size} shares @ €{position.price:.2f} average")
    print(f"Tax Lots: {len(position.taxlots)}")
    
    print(f"\nTax Lot Details:")
    print("-" * 50)
    for i, lot in enumerate(position.taxlots, 1):
        print(f"Lot {i}: {lot.remaining_qty} shares @ €{lot.price:.2f}")
    
    print(f"\nPnL Comparison for Different Sales:")
    print("=" * 70)
    
    # Test different sale scenarios
    scenarios = [
        (200, 40.0, "Sell 200 shares at €40"),
        (300, 35.0, "Sell 300 shares at €35"),
        (100, 60.0, "Sell 100 shares at €60"),
    ]
    
    for sale_qty, sale_price, desc in scenarios:
        # Calculate both methods
        avg_pnl = (sale_price - position.price) * sale_qty
        taxlot_pnl = position.get_taxlot_pnl(sale_qty, sale_price)
        difference = avg_pnl - taxlot_pnl
        
        print(f"\nScenario: {desc}")
        print("-" * 50)
        print(f"Average Price PnL: €{avg_pnl:,.2f}")
        print(f"Tax Lot PnL (FIFO): €{taxlot_pnl:,.2f}")
        print(f"Difference: €{difference:,.2f}")
        
        # Show which lots would be affected (FIFO)
        print("FIFO Lots Affected:")
        remaining = sale_qty
        for i, lot in enumerate(position.taxlots):
            if remaining <= 0:
                break
            qty_sold = min(lot.remaining_qty, remaining)
            lot_pnl = (sale_price - lot.price) * qty_sold
            remaining -= qty_sold
            print(f"  - {qty_sold} shares @ €{lot.price:.2f} = €{lot_pnl:.2f}")


def demo_european_tax_optimization():
    """European-specific tax optimization scenarios without wash sale rules"""
    
    print("\n" + "=" * 70)
    print("DEMO 2: European Tax Optimization (No Wash Sale Rules)")
    print("=" * 70)
    
    position = Position()
    
    # Build position with various entry points
    trades = [
        (100, 100.0, datetime(2022, 1, 15), "Long-term holding"),
        (100, 120.0, datetime(2023, 3, 20), "Recent purchase"),
        (100, 80.0, datetime(2023, 6, 10), "Dip buying"),
        (100, 110.0, datetime(2023, 9, 5), "Averaging up"),
    ]
    
    for qty, price, date, desc in trades:
        position.update(qty, price, date)
    
    print(f"Current position: {position.size} shares @ €{position.price:.2f}")
    current_price = 90.0
    print(f"Current market price: €{current_price:.2f}")
    
    print(f"\nTax lot analysis:")
    current_date = datetime(2023, 12, 1)
    
    for i, lot in enumerate(position.taxlots, 1):
        days_held = (current_date - lot.datetime).days
        unrealized_pnl = (current_price - lot.price) * lot.remaining_qty
        holding_status = "Long-term" if days_held > 365 else "Short-term"
        
        print(f"Lot {i}: {lot.remaining_qty} @ €{lot.price:.2f}, "
              f"Unrealized P&L: €{unrealized_pnl:.2f}, "
              f"{holding_status} ({days_held} days)")
    
    print(f"\nEuropean Tax Optimization Strategies:")
    print("-" * 60)
    
    # Strategy 1: Harvest losses immediately (no wash sale rule)
    loss_lots = [lot for lot in position.taxlots if lot.price > current_price]
    total_loss_harvest = sum((current_price - lot.price) * lot.remaining_qty for lot in loss_lots)
    loss_shares = sum(lot.remaining_qty for lot in loss_lots)
    
    print(f"1. Immediate Loss Harvesting:")
    print(f"   Sell {loss_shares} shares at loss: €{total_loss_harvest:.2f}")
    print(f"   Can immediately rebuy (no wash sale rule in Europe)")
    
    # Strategy 2: Selective lot liquidation
    print(f"\n2. Selective Lot Liquidation:")
    for i, lot in enumerate(position.taxlots, 1):
        lot_pnl = (current_price - lot.price) * lot.remaining_qty
        days_held = (current_date - lot.datetime).days
        tax_treatment = "Favorable long-term" if days_held > 365 else "Short-term"
        print(f"   Lot {i}: €{lot_pnl:.2f} PnL, {tax_treatment}")
    
    # Strategy 3: Cross-border tax considerations
    print(f"\n3. Cross-Border Considerations:")
    print(f"   • No withholding tax within EU for most securities")
    print(f"   • Capital gains tax varies by country of residence")
    print(f"   • Can optimize timing across different tax years")
    print(f"   • No restriction on immediate repurchase after losses")


def demo_european_market_scenarios():
    """European market specific scenarios"""
    
    print("\n" + "=" * 70)
    print("DEMO 3: European Market Scenarios")
    print("=" * 70)
    
    print("\n1. Multi-Exchange Trading (London, Frankfurt, Amsterdam):")
    print("-" * 60)
    
    # Simulate trading same stock on different European exchanges
    positions = {
        'LSE': Position(),    # London Stock Exchange (GBP)
        'XETRA': Position(),  # Frankfurt (EUR)
        'AEX': Position(),    # Amsterdam (EUR)
    }
    
    # Exchange rates to EUR
    fx_rates = {
        'LSE': 1.1547,     # GBP/EUR
        'XETRA': 1.0,      # EUR/EUR
        'AEX': 1.0,        # EUR/EUR
    }
    
    # Trades on different exchanges
    trades = [
        ('LSE', 100, 43.50, "London: Royal Dutch Shell"),
        ('XETRA', 100, 50.25, "Frankfurt: Same stock in EUR"),
        ('AEX', 100, 50.30, "Amsterdam: Home exchange"),
    ]
    
    total_eur_value = 0
    for exchange, qty, price, desc in trades:
        positions[exchange].update(qty, price, datetime.now())
        eur_equivalent = price * fx_rates[exchange]
        total_eur_value += qty * eur_equivalent
        print(f"{desc}: {qty} @ {price:.2f} (€{eur_equivalent:.2f} EUR equiv)")
    
    print(f"Total EUR equivalent value: €{total_eur_value:.2f}")
    
    # Calculate PnL in different currencies
    print("\nPnL calculations across exchanges:")
    sale_prices = {'LSE': 45.50, 'XETRA': 52.75, 'AEX': 52.80}  # 5% gains
    
    for exchange in positions:
        pnl = positions[exchange].get_taxlot_pnl(50, sale_prices[exchange])
        eur_pnl = pnl * fx_rates[exchange]
        currency = 'GBP' if exchange == 'LSE' else 'EUR'
        print(f"{exchange}: {pnl:.2f} {currency} (€{eur_pnl:.2f} EUR)")
    
    print("\n2. Brexit Impact Scenario:")
    print("-" * 60)
    position = Position()
    
    # Pre-Brexit and post-Brexit positions
    brexit_trades = [
        (1000, 45.0, datetime(2019, 1, 1), "Pre-Brexit position"),
        (500, 35.0, datetime(2020, 2, 1), "Brexit uncertainty dip"),
        (300, 50.0, datetime(2021, 6, 1), "Post-Brexit recovery"),
    ]
    
    for qty, price, date, desc in brexit_trades:
        position.update(qty, price, date)
        print(f"{desc}: {qty} shares @ €{price:.2f}")
    
    # Show impact of regulatory changes
    current_price = 48.0
    brexit_pnl = position.get_taxlot_pnl(800, current_price)
    print(f"Brexit period PnL (selling 800 shares): €{brexit_pnl:.2f}")
    
    print("\n3. European Central Bank Policy Impact:")
    print("-" * 60)
    position = Position()
    
    # ECB policy-driven trades
    ecb_trades = [
        (200, 60.0, "Pre-QE position"),
        (300, 45.0, "QE announcement dip"),
        (400, 70.0, "Monetary expansion rally"),
        (100, 55.0, "Rate normalization concern"),
    ]
    
    for qty, price, desc in ecb_trades:
        position.update(qty, price, datetime.now())
        print(f"{desc}: {qty} shares @ €{price:.2f}")
    
    print(f"ECB policy impact position: {position.size} shares @ €{position.price:.2f}")


def demo_european_regulatory_scenarios():
    """European regulatory scenarios (MiFID II, etc.) - No wash sale rules"""
    
    print("\n" + "=" * 70)
    print("DEMO 4: European Regulatory Scenarios (MiFID II)")
    print("=" * 70)
    
    print("\n1. MiFID II Best Execution Requirements:")
    print("-" * 60)
    position = Position()
    
    # Simulate trades across different venues for best execution
    venues = [
        ("Primary Exchange", 100, 50.00, "Main market"),
        ("Dark Pool", 200, 49.98, "Better price in dark pool"),
        ("Systematic Internaliser", 150, 50.01, "SI execution"),
        ("Multilateral Trading Facility", 100, 49.99, "MTF execution"),
    ]
    
    total_saved = 0
    for venue, qty, price, desc in venues:
        position.update(qty, price, datetime.now())
        reference_price = 50.00
        saved = (reference_price - price) * qty
        total_saved += saved
        print(f"{venue}: {qty} @ €{price:.2f} ({desc}) - Saved: €{saved:.2f}")
    
    print(f"Total execution cost savings: €{total_saved:.2f}")
    print(f"Final position: {position.size} shares @ €{position.price:.4f}")
    
    print("\n2. Immediate Loss Harvesting (No Wash Sale Restrictions):")
    print("-" * 60)
    position = Position()
    
    # Build and liquidate position multiple times (legal in Europe)
    cycles = [
        ("Cycle 1", [(100, 45.0), (-100, 40.0)], "Loss: €500"),
        ("Cycle 2", [(100, 40.0), (-100, 35.0)], "Loss: €500"),  # Immediate rebuy OK
        ("Cycle 3", [(100, 35.0), (-100, 30.0)], "Loss: €500"),  # Immediate rebuy OK
        ("Cycle 4", [(200, 30.0), (-200, 40.0)], "Gain: €2000"), # Recovery
    ]
    
    total_pnl = 0
    for cycle_name, trades, expected in cycles:
        cycle_pnl = 0
        for qty, price in trades:
            if qty > 0:
                position.update(qty, price, datetime.now())
                print(f"  BUY {qty} @ €{price:.2f}")
            else:
                sale_qty = abs(qty)
                pnl = position.get_taxlot_pnl(sale_qty, price)
                position.update(qty, price, datetime.now())
                cycle_pnl += pnl
                print(f"  SELL {sale_qty} @ €{price:.2f}, PnL: €{pnl:.2f}")
        
        total_pnl += cycle_pnl
        print(f"{cycle_name}: {expected} (Actual: €{cycle_pnl:.2f})")
        print("  ✓ Immediate rebuy allowed (no wash sale rule)")
    
    print(f"Total PnL across cycles: €{total_pnl:.2f}")
    print("✓ European advantage: No wash sale restrictions")


def demo_european_currency_scenarios():
    """European multi-currency scenarios"""
    
    print("\n" + "=" * 70)
    print("DEMO 5: European Multi-Currency Scenarios")
    print("=" * 70)
    
    print("\n1. Cross-Currency Arbitrage:")
    print("-" * 60)
    
    # Same stock traded in different currencies
    london_pos = Position()  # GBP
    frankfurt_pos = Position()  # EUR
    
    # Arbitrage opportunity
    gbp_price = 45.50  # Price in London
    eur_price = 52.00  # Price in Frankfurt
    gbp_eur_rate = 1.1547  # GBP/EUR rate
    
    theoretical_eur_price = gbp_price * gbp_eur_rate
    arbitrage_opportunity = eur_price - theoretical_eur_price
    
    print(f"London price: £{gbp_price:.2f}")
    print(f"Frankfurt price: €{eur_price:.2f}")
    print(f"Theoretical EUR price: €{theoretical_eur_price:.2f}")
    print(f"Arbitrage opportunity: €{arbitrage_opportunity:.2f} per share")
    
    if arbitrage_opportunity > 0.05:  # Threshold for trading costs
        # Execute arbitrage
        london_pos.update(-1000, gbp_price, datetime.now())  # Sell in London
        frankfurt_pos.update(1000, eur_price, datetime.now())  # Buy in Frankfurt
        
        arbitrage_profit = arbitrage_opportunity * 1000
        print(f"Arbitrage executed: €{arbitrage_profit:.2f} profit potential")
    else:
        print("Arbitrage opportunity too small after transaction costs")
    
    print("\n2. Multi-Currency Portfolio:")
    print("-" * 60)
    
    # Portfolio across different European currencies
    portfolio = {
        'EUR': Position(),
        'GBP': Position(),
        'CHF': Position(),  # Swiss Franc
    }
    
    # Exchange rates to EUR
    fx_rates = {
        'EUR': 1.0,
        'GBP': 1.1547,
        'CHF': 1.0789,
    }
    
    # Trades in different currencies
    trades = [
        ('EUR', 1000, 50.0, "German DAX stock"),
        ('GBP', 800, 43.5, "UK FTSE stock"),
        ('CHF', 500, 46.3, "Swiss SMI stock"),
    ]
    
    total_eur_value = 0
    for currency, qty, price, desc in trades:
        portfolio[currency].update(qty, price, datetime.now())
        eur_value = qty * price * fx_rates[currency]
        total_eur_value += eur_value
        print(f"{desc}: {qty} @ {price:.1f} {currency} (€{eur_value:,.2f})")
    
    print(f"Total portfolio value: €{total_eur_value:,.2f}")
    
    # Show PnL in each currency
    print("\nPnL at 5% gain across all positions:")
    for currency in portfolio:
        original_price = 50.0 if currency == 'EUR' else (43.5 if currency == 'GBP' else 46.3)
        new_price = original_price * 1.05
        qty = portfolio[currency].size
        pnl = portfolio[currency].get_taxlot_pnl(qty, new_price)
        eur_pnl = pnl * fx_rates[currency]
        print(f"{currency}: {pnl:.2f} {currency} (€{eur_pnl:.2f} EUR)")


def demo_performance_stress_test():
    """Test performance with European institutional scale"""
    
    print("\n" + "=" * 70)
    print("DEMO 6: Performance Stress Test (European Institutional Scale)")
    print("=" * 70)
    
    import time
    
    position = Position()
    
    # Create tax lots for European institutional portfolio
    print("Creating 25,000 tax lots for European institutional portfolio...")
    start_time = time.time()
    
    for i in range(25000):
        if i % 5000 == 0:
            print(f"  Progress: {i:,} lots created...")
        
        qty = 100 + (i % 500)  # Varying quantities
        price = 50.0 + (i % 1000) * 0.01  # Varying prices in EUR
        dt = datetime(2023, 1, 1) + timedelta(hours=i//10)
        position.update(qty, price, dt)
    
    creation_time = time.time() - start_time
    print(f"Created {len(position.taxlots):,} tax lots in {creation_time:.3f} seconds")
    print(f"Total position: {position.size:,} shares @ €{position.price:.2f}")
    
    # Test PnL calculation performance
    print("\nTesting European-scale PnL calculation performance...")
    sale_qty = position.size // 4  # Sell 25%
    sale_price = 75.0
    
    start_time = time.time()
    for _ in range(50):  # 50 PnL calculations
        pnl = position.get_taxlot_pnl(sale_qty, sale_price)
    calc_time = time.time() - start_time
    
    print(f"50 PnL calculations in {calc_time:.3f} seconds")
    print(f"Average per calculation: {calc_time/50*1000:.2f} ms")
    print(f"Final PnL result: €{pnl:,.2f}")
    print("Performance suitable for European institutional trading")


if __name__ == '__main__':
    demo_basic_pnl_comparison()
    demo_european_tax_optimization()
    demo_european_market_scenarios()
    demo_european_regulatory_scenarios()
    demo_european_currency_scenarios()
    demo_performance_stress_test()
    
    print("\n" + "=" * 70)
    print("EUROPEAN MARKET DEMOS COMPLETE")
    print("=" * 70)
    print("\nKey European Market Features:")
    print("• No wash sale rules - immediate loss harvesting allowed")
    print("• T+2 settlement standard across EU")
    print("• Multi-currency trading (EUR, GBP, CHF)")
    print("• Cross-border execution harmonized")
    print("• MiFID II best execution compliance")
    print("• Brexit and ECB policy considerations")
    print("• Institutional-scale performance")
    print("\nEuropean Tax Lot Benefits:")
    print("• Precise cost basis tracking across currencies")
    print("• Optimal tax loss harvesting (no restrictions)")
    print("• Cross-exchange arbitrage opportunities")
    print("• Multi-currency portfolio management")
    print("• Regulatory compliance reporting")
    print("• Performance optimized for institutional scale")
    print("• Immediate rebuy capability after losses") 