#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
###############################################################################
#
# Demo: Tax Lot Aware PnL Calculations for Brokers
#
# This example demonstrates how brokers can use tax lot aware PnL calculations
# instead of the traditional average price method.
#
###############################################################################
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import backtrader
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import backtrader as bt
from backtrader.position import Position
from backtrader.broker import TaxAwareBrokerMixin
from backtrader.brokers.bbroker import BackBroker


class TaxAwareBackBroker(TaxAwareBrokerMixin, BackBroker):
    """
    Enhanced BackBroker with tax lot aware PnL calculations
    
    This broker can calculate PnL using either:
    1. Traditional average price method (default)
    2. Tax lot aware FIFO method (when enabled)
    """
    
    def __init__(self, use_taxlot_pnl=True):
        super(TaxAwareBackBroker, self).__init__()
        self.use_taxlot_pnl = use_taxlot_pnl
        
    def calculate_pnl(self, position, closed_qty, sale_price):
        """
        Calculate PnL using either average price or tax lot method
        
        Args:
            position: Position object
            closed_qty: Quantity being closed (positive number)
            sale_price: Sale price
            
        Returns:
            float: Calculated PnL
        """
        if self.use_taxlot_pnl and self.is_taxlot_enabled(position):
            # Use tax lot aware PnL calculation
            return self.get_taxlot_pnl(position, closed_qty, sale_price)
        else:
            # Use traditional average price calculation
            return closed_qty * (sale_price - position.price)


def demo_basic_pnl_comparison():
    """Demonstrate the difference between average price and tax lot PnL"""
    
    print("=" * 70)
    print("DEMO 1: Basic Tax Lot Aware PnL Comparison")
    print("=" * 70)
    
    # Create position and simulate trading
    position = Position()
    
    # Simulate a series of purchases at different prices
    trades = [
        (100, 50.0, "Initial purchase at peak"),
        (200, 30.0, "Double down after 40% drop"),
        (100, 45.0, "Buy more on recovery"),
        (100, 25.0, "Buy the dip again"),
    ]
    
    print("\nBuilding Position:")
    print("-" * 50)
    
    for i, (qty, price, description) in enumerate(trades):
        position.update(qty, price, datetime(2023, 1, i+1))
        print(f"{i+1}. {description}: {qty} shares @ ${price:.2f}")
    
    total_shares = position.size
    avg_price = position.price
    
    print(f"\nFinal Position: {total_shares} shares @ ${avg_price:.2f} average")
    print(f"Tax Lots: {len(position.taxlots)}")
    
    # Show tax lot details
    print("\nTax Lot Details:")
    print("-" * 50)
    for i, lot in enumerate(position.taxlots):
        print(f"Lot {i+1}: {lot.remaining_qty} shares @ ${lot.price:.2f}")
    
    # Test different sale scenarios
    sale_scenarios = [
        (200, 40.0, "Sell 200 shares at $40"),
        (300, 35.0, "Sell 300 shares at $35"),
        (100, 60.0, "Sell 100 shares at $60"),
    ]
    
    print("\nPnL Comparison for Different Sales:")
    print("=" * 70)
    
    for sale_qty, sale_price, description in sale_scenarios:
        print(f"\nScenario: {description}")
        print("-" * 50)
        
        # Calculate PnL using both methods
        avg_pnl = sale_qty * (sale_price - avg_price)
        taxlot_pnl = position.get_taxlot_pnl(sale_qty, sale_price)
        
        print(f"Average Price PnL: ${avg_pnl:,.2f}")
        print(f"Tax Lot PnL (FIFO): ${taxlot_pnl:,.2f}")
        print(f"Difference: ${taxlot_pnl - avg_pnl:,.2f}")
        
        # Show which lots would be affected
        print("FIFO Lots Affected:")
        remaining = min(sale_qty, position.size)
        for lot in position.taxlots:
            if remaining <= 0:
                break
            
            if lot.remaining_qty <= remaining:
                qty_from_lot = lot.remaining_qty
                remaining -= qty_from_lot
                print(f"  - {qty_from_lot} shares @ ${lot.price:.2f} = ${qty_from_lot * (sale_price - lot.price):.2f}")
            else:
                qty_from_lot = remaining
                remaining = 0
                print(f"  - {qty_from_lot} shares @ ${lot.price:.2f} = ${qty_from_lot * (sale_price - lot.price):.2f}")


def demo_edge_cases():
    """Test various edge cases and error conditions"""
    
    print("\n" + "=" * 70)
    print("DEMO 2: Edge Cases and Error Handling")
    print("=" * 70)
    
    print("\n1. Empty Position Edge Case:")
    print("-" * 40)
    empty_position = Position()
    pnl = empty_position.get_taxlot_pnl(100, 50.0)
    print(f"PnL from empty position: ${pnl:.2f} (should be 0)")
    
    print("\n2. Zero Sale Quantity:")
    print("-" * 40)
    position = Position()
    position.update(100, 40.0, datetime.now())
    pnl = position.get_taxlot_pnl(0, 50.0)
    print(f"PnL from zero sale: ${pnl:.2f} (should be 0)")
    
    print("\n3. Selling More Than Position Size:")
    print("-" * 40)
    position = Position()
    position.update(100, 40.0, datetime.now())
    pnl_100 = position.get_taxlot_pnl(100, 50.0)  # Exact position size
    pnl_200 = position.get_taxlot_pnl(200, 50.0)  # More than position size
    print(f"PnL selling 100 shares (exact): ${pnl_100:.2f}")
    print(f"PnL selling 200 shares (excess): ${pnl_200:.2f}")
    print(f"Should be equal: {pnl_100 == pnl_200}")
    
    print("\n4. Fractional Shares:")
    print("-" * 40)
    position = Position()
    position.update(100.5, 40.33, datetime.now())
    position.update(75.25, 45.67, datetime.now())
    pnl = position.get_taxlot_pnl(150.33, 50.0)
    print(f"Position: {position.size} shares @ ${position.price:.2f}")
    print(f"PnL selling 150.33 shares: ${pnl:.2f}")
    
    print("\n5. Extreme Price Values:")
    print("-" * 40)
    position = Position()
    position.update(100, 0.01, datetime.now())  # Penny stock
    position.update(10, 1000.0, datetime.now())  # High-priced stock
    pnl_low = position.get_taxlot_pnl(50, 0.02)  # Sell penny stock portion
    pnl_high = position.get_taxlot_pnl(60, 1100.0)  # Sell both
    print(f"PnL selling 50 shares @ $0.02: ${pnl_low:.2f}")
    print(f"PnL selling 60 shares @ $1100: ${pnl_high:.2f}")


def demo_position_reversal():
    """Test position reversal scenarios (long to short, short to long)"""
    
    print("\n" + "=" * 70)
    print("DEMO 3: Position Reversal Scenarios")
    print("=" * 70)
    
    print("\n1. Long to Short Reversal:")
    print("-" * 40)
    position = Position()
    
    # Build long position
    position.update(100, 50.0, datetime(2023, 1, 1))
    position.update(100, 40.0, datetime(2023, 1, 2))
    print(f"Long position: {position.size} shares @ ${position.price:.2f}")
    print(f"Tax lots: {len(position.taxlots)}")
    
    # Calculate PnL before reversal
    pnl_partial = position.get_taxlot_pnl(100, 45.0)
    print(f"PnL selling 100 shares @ $45: ${pnl_partial:.2f}")
    
    # Sell entire position and go short
    position.update(-250, 45.0, datetime(2023, 1, 3))  # Sell 250 (200 long + 50 short)
    print(f"After reversal: {position.size} shares (short)")
    print(f"Tax lots remaining: {len(position.taxlots)}")
    
    # Try to calculate PnL on short position (should return 0)
    pnl_short = position.get_taxlot_pnl(25, 50.0)
    print(f"PnL from short position: ${pnl_short:.2f} (should be 0)")
    
    print("\n2. Short to Long Reversal:")
    print("-" * 40)
    position = Position()
    
    # Start with short position (simulate)
    position.size = -100
    position.price = 60.0
    position.taxlots = []  # Short positions don't have tax lots
    print(f"Short position: {position.size} shares @ ${position.price:.2f}")
    
    # Cover short and go long
    position.update(150, 55.0, datetime(2023, 1, 4))  # Cover 100 short + 50 long
    print(f"After reversal: {position.size} shares @ ${position.price:.2f}")
    print(f"Tax lots: {len(position.taxlots)}")
    
    # Calculate PnL on new long position
    pnl_new_long = position.get_taxlot_pnl(25, 60.0)
    print(f"PnL selling 25 shares @ $60: ${pnl_new_long:.2f}")


def demo_complex_trading_scenarios():
    """Test complex real-world trading scenarios"""
    
    print("\n" + "=" * 70)
    print("DEMO 4: Complex Trading Scenarios")
    print("=" * 70)
    
    print("\n1. Day Trading with Multiple Lots:")
    print("-" * 40)
    position = Position()
    
    # Simulate day trading activity
    trades = [
        (1000, 100.0, "Morning buy"),
        (-300, 102.0, "Quick profit taking"),
        (500, 99.0, "Buy the dip"),
        (-200, 103.0, "Partial exit"),
        (200, 98.0, "Final addition"),
    ]
    
    for i, (qty, price, desc) in enumerate(trades):
        dt = datetime(2023, 1, 1, 9 + i)  # Hourly trades
        if qty > 0:
            position.update(qty, price, dt)
            print(f"{desc}: BUY {qty} @ ${price:.2f}")
        else:
            # For sales, show PnL before executing
            sale_qty = abs(qty)
            pnl = position.get_taxlot_pnl(sale_qty, price)
            position.update(qty, price, dt)
            print(f"{desc}: SELL {sale_qty} @ ${price:.2f}, PnL: ${pnl:.2f}")
    
    print(f"\nFinal position: {position.size} shares @ ${position.price:.2f}")
    print(f"Remaining tax lots: {len(position.taxlots)}")
    
    print("\n2. Dollar Cost Averaging with Volatility:")
    print("-" * 40)
    position = Position()
    
    # Simulate systematic investing during volatile period
    monthly_buys = [
        (100, 80.0, "Jan: Market high"),
        (100, 60.0, "Feb: 25% drop"),
        (100, 40.0, "Mar: Further decline"),
        (100, 45.0, "Apr: Small recovery"),
        (100, 35.0, "May: New low"),
        (100, 50.0, "Jun: Recovery begins"),
        (100, 70.0, "Jul: Strong rally"),
        (100, 65.0, "Aug: Pullback"),
    ]
    
    for i, (qty, price, desc) in enumerate(monthly_buys):
        dt = datetime(2023, i+1, 15)  # 15th of each month
        position.update(qty, price, dt)
        print(f"{desc}: {qty} shares @ ${price:.2f}")
    
    print(f"\nTotal invested: {position.size} shares @ ${position.price:.2f} average")
    
    # Test different exit strategies
    current_price = 60.0
    print(f"\nExit strategies @ ${current_price:.2f}:")
    
    exit_scenarios = [
        (200, "Sell oldest lots (FIFO)"),
        (400, "Sell half position"),
        (800, "Sell entire position"),
    ]
    
    for sale_qty, desc in exit_scenarios:
        pnl = position.get_taxlot_pnl(sale_qty, current_price)
        avg_pnl = sale_qty * (current_price - position.price)
        print(f"{desc}: Tax lot PnL ${pnl:.2f} vs Avg PnL ${avg_pnl:.2f} (diff: ${pnl-avg_pnl:.2f})")


def demo_tax_optimization():
    """Demonstrate tax optimization scenarios"""
    
    print("\n" + "=" * 70)
    print("DEMO 5: Tax Optimization Scenarios")
    print("=" * 70)
    
    print("\n1. Tax Loss Harvesting Opportunity:")
    print("-" * 40)
    position = Position()
    
    # Build position with mixed gains/losses
    position.update(100, 100.0, datetime(2022, 1, 1))   # Old lot (potential long-term)
    position.update(100, 120.0, datetime(2023, 6, 1))   # Recent lot at higher price
    position.update(100, 80.0, datetime(2023, 8, 1))    # Recent lot at lower price
    position.update(100, 110.0, datetime(2023, 10, 1))  # Most recent lot
    
    current_price = 90.0
    print(f"Current position: {position.size} shares @ ${position.price:.2f}")
    print(f"Current market price: ${current_price:.2f}")
    
    print("\nTax lot analysis:")
    for i, lot in enumerate(position.taxlots):
        unrealized_pnl = (current_price - lot.price) * lot.remaining_qty
        days_held = (datetime.now() - lot.datetime).days
        term = "Long-term" if days_held > 365 else "Short-term"
        print(f"Lot {i+1}: {lot.remaining_qty} @ ${lot.price:.2f}, "
              f"Unrealized P&L: ${unrealized_pnl:.2f}, {term} ({days_held} days)")
    
    # Show selective selling for tax optimization
    print(f"\nTax optimization strategies:")
    
    # Harvest losses (sell high-cost lots first conceptually, but FIFO rules apply)
    loss_harvest_qty = 200
    loss_pnl = position.get_taxlot_pnl(loss_harvest_qty, current_price)
    print(f"Sell {loss_harvest_qty} shares (FIFO): ${loss_pnl:.2f} PnL")
    
    # Show what lots would be sold
    print("FIFO lots to be sold:")
    remaining = loss_harvest_qty
    for lot in position.taxlots:
        if remaining <= 0:
            break
        qty_from_lot = min(lot.remaining_qty, remaining)
        lot_pnl = (current_price - lot.price) * qty_from_lot
        remaining -= qty_from_lot
        print(f"  - {qty_from_lot} shares @ ${lot.price:.2f} = ${lot_pnl:.2f}")
    
    print("\n2. Wash Sale Consideration:")
    print("-" * 40)
    print("Note: This demo shows PnL calculation only.")
    print("Actual wash sale rule implementation would be in Phase 3.")
    
    # Simulate selling at a loss and buying back
    sale_date = datetime(2023, 12, 1)
    rebuy_date = datetime(2023, 12, 15)  # 14 days later (potential wash sale)
    
    print(f"Sell date: {sale_date.strftime('%Y-%m-%d')}")
    print(f"Rebuy date: {rebuy_date.strftime('%Y-%m-%d')}")
    print(f"Days between: {(rebuy_date - sale_date).days} (< 30 days = potential wash sale)")


def demo_broker_integration():
    """Demonstrate broker integration with tax lot PnL"""
    
    print("\n" + "=" * 70)
    print("DEMO 6: Broker Integration Comparison")
    print("=" * 70)
    
    # Create brokers with different PnL methods
    standard_broker = BackBroker()
    taxlot_broker = TaxAwareBackBroker(use_taxlot_pnl=True)
    
    # Create identical positions
    position1 = Position()
    position2 = Position()
    
    # Build positions with volatile trading
    trades = [
        (100, 40.0, "Initial position"),
        (100, 60.0, "Buy high"),
        (100, 30.0, "Buy low"),
        (50, 55.0, "Small addition"),
    ]
    
    print("Building identical positions in both brokers:")
    for qty, price, desc in trades:
        dt = datetime.now()
        position1.update(qty, price, dt)
        position2.update(qty, price, dt)
        print(f"{desc}: {qty} shares @ ${price:.2f}")
    
    print(f"\nFinal position: {position1.size} shares @ ${position1.price:.2f} average")
    
    # Test different sale scenarios
    sale_scenarios = [
        (150, 50.0, "Moderate sale"),
        (200, 35.0, "Large sale at loss"),
        (100, 70.0, "Small sale at gain"),
    ]
    
    print("\nBroker PnL Comparison:")
    print("=" * 50)
    
    for sale_qty, sale_price, desc in sale_scenarios:
        print(f"\nScenario: {desc} - {sale_qty} shares @ ${sale_price:.2f}")
        print("-" * 50)
        
        # Standard broker PnL
        standard_pnl = sale_qty * (sale_price - position1.price)
        print(f"Standard Broker PnL: ${standard_pnl:.2f}")
        
        # Tax lot aware broker PnL
        taxlot_pnl = taxlot_broker.calculate_pnl(position2, sale_qty, sale_price)
        print(f"Tax Lot Broker PnL: ${taxlot_pnl:.2f}")
        
        difference = taxlot_pnl - standard_pnl
        print(f"Difference: ${difference:.2f}")
        
        # Show percentage difference
        if standard_pnl != 0:
            pct_diff = (difference / abs(standard_pnl)) * 100
            print(f"Percentage difference: {pct_diff:.1f}%")
    
    # Show unrealized PnL comparison
    print(f"\nUnrealized PnL Comparison:")
    print("-" * 30)
    current_price = 55.0
    
    # Tax lot method
    realized, unrealized = taxlot_broker.get_realized_unrealized_pnl(position2, current_price)
    print(f"Tax Lot Method @ ${current_price:.2f}: ${unrealized:.2f}")
    
    # Standard method
    standard_unrealized = position2.size * (current_price - position2.price)
    print(f"Standard Method @ ${current_price:.2f}: ${standard_unrealized:.2f}")
    
    unrealized_diff = unrealized - standard_unrealized
    print(f"Unrealized PnL Difference: ${unrealized_diff:.2f}")


def demo_performance_stress_test():
    """Test performance with large numbers of tax lots"""
    
    print("\n" + "=" * 70)
    print("DEMO 7: Performance Stress Test")
    print("=" * 70)
    
    import time
    
    position = Position()
    
    # Create many small tax lots
    print("Creating 1000 small tax lots...")
    start_time = time.time()
    
    for i in range(1000):
        qty = 10 + (i % 50)  # Varying quantities
        price = 50.0 + (i % 100) * 0.1  # Varying prices
        dt = datetime(2023, 1, 1) + timedelta(hours=i)
        position.update(qty, price, dt)
    
    creation_time = time.time() - start_time
    print(f"Created {len(position.taxlots)} tax lots in {creation_time:.3f} seconds")
    print(f"Total position: {position.size} shares @ ${position.price:.2f}")
    
    # Test PnL calculation performance
    print("\nTesting PnL calculation performance...")
    sale_qty = position.size // 2  # Sell half
    sale_price = 75.0
    
    start_time = time.time()
    for _ in range(100):  # 100 PnL calculations
        pnl = position.get_taxlot_pnl(sale_qty, sale_price)
    calc_time = time.time() - start_time
    
    print(f"100 PnL calculations in {calc_time:.3f} seconds")
    print(f"Average per calculation: {calc_time/100*1000:.2f} ms")
    print(f"Final PnL result: ${pnl:.2f}")


def demo_extreme_precision_edge_cases():
    """Test extreme precision and rounding edge cases"""
    
    print("\n" + "=" * 70)
    print("DEMO 8: Extreme Precision & Rounding Edge Cases")
    print("=" * 70)
    
    print("\n1. Micro-Fractional Shares (Cryptocurrency-like precision):")
    print("-" * 60)
    position = Position()
    
    # Simulate cryptocurrency or fractional share trading
    micro_trades = [
        (0.000001, 50000.*********, "Micro BTC position"),
        (0.000007, 50001.*********, "Another micro buy"),
        (0.000003, 49999.*********, "Micro buy on dip"),
    ]
    
    for qty, price, desc in micro_trades:
        position.update(qty, price, datetime.now())
        print(f"{desc}: {qty:.9f} units @ ${price:.9f}")
    
    print(f"Total position: {position.size:.9f} units @ ${position.price:.9f}")
    
    # Test PnL with extreme precision
    sale_qty = 0.000005
    sale_price = 50500.*********
    pnl = position.get_taxlot_pnl(sale_qty, sale_price)
    print(f"PnL selling {sale_qty:.9f} @ ${sale_price:.9f}: ${pnl:.9f}")
    
    print("\n2. High-Precision Decimal Places:")
    print("-" * 60)
    position = Position()
    
    # Test with many decimal places
    position.update(100.12345678, 123.87654321, datetime.now())
    position.update(200.98765432, 456.12345678, datetime.now())
    
    pnl = position.get_taxlot_pnl(150.55555555, 789.99999999)
    print(f"High precision PnL: ${pnl:.8f}")
    
    print("\n3. Rounding Error Accumulation Test:")
    print("-" * 60)
    position = Position()
    
    # Many small transactions that could accumulate rounding errors
    for i in range(100):
        qty = 1.0 / 3.0  # 0.333333...
        price = 1.0 / 7.0  # 0.142857...
        position.update(qty, price, datetime.now())
    
    total_qty = sum(1.0/3.0 for _ in range(100))
    print(f"Expected total: {total_qty:.10f}")
    print(f"Actual total: {position.size:.10f}")
    print(f"Difference: {abs(total_qty - position.size):.10f}")
    
    print("\n4. Currency Conversion Precision:")
    print("-" * 60)
    position = Position()
    
    # Simulate forex-like precision
    eur_to_usd = 1.0847392
    position.update(1000, 100 * eur_to_usd, datetime.now())  # EUR position converted to USD
    position.update(500, 101 * eur_to_usd, datetime.now())
    
    pnl = position.get_taxlot_pnl(750, 102 * eur_to_usd)
    print(f"Multi-currency PnL: ${pnl:.6f}")


def demo_market_crisis_scenarios():
    """Test extreme market conditions and crisis scenarios"""
    
    print("\n" + "=" * 70)
    print("DEMO 9: Market Crisis & Extreme Volatility Scenarios")
    print("=" * 70)
    
    print("\n1. Stock Goes to Zero (Bankruptcy Scenario):")
    print("-" * 60)
    position = Position()
    
    # Build position before bankruptcy
    position.update(1000, 50.0, datetime(2023, 1, 1))
    position.update(500, 30.0, datetime(2023, 2, 1))
    position.update(200, 10.0, datetime(2023, 3, 1))  # Trying to catch falling knife
    
    print(f"Pre-bankruptcy position: {position.size} shares @ ${position.price:.2f}")
    
    # Stock goes to zero
    bankruptcy_pnl = position.get_taxlot_pnl(position.size, 0.0)
    print(f"Total loss if stock goes to $0: ${bankruptcy_pnl:.2f}")
    
    # Partial sale before complete collapse
    partial_pnl = position.get_taxlot_pnl(800, 0.01)  # Sell at 1 cent
    print(f"PnL selling 800 shares @ $0.01: ${partial_pnl:.2f}")
    
    print("\n2. Negative Prices (Oil Futures 2020 Scenario):")
    print("-" * 60)
    position = Position()
    
    # Simulate oil futures going negative
    position.update(100, 20.0, datetime(2020, 4, 1))   # Before crash
    position.update(50, 10.0, datetime(2020, 4, 15))   # During decline
    position.update(25, 1.0, datetime(2020, 4, 20))    # Near zero
    
    # Oil goes negative
    negative_price = -37.63  # Actual WTI price on April 20, 2020
    negative_pnl = position.get_taxlot_pnl(100, negative_price)
    print(f"PnL selling 100 contracts @ ${negative_price:.2f}: ${negative_pnl:.2f}")
    
    print("\n3. Extreme Volatility (99% Drop + 1000% Recovery):")
    print("-" * 60)
    position = Position()
    
    # Simulate extreme meme stock volatility
    volatility_trades = [
        (100, 1000.0, "Peak bubble price"),
        (200, 10.0, "99% crash - buy the dip"),
        (500, 1.0, "Further crash - double down"),
        (100, 0.1, "Near penny stock"),
    ]
    
    for qty, price, desc in volatility_trades:
        position.update(qty, price, datetime.now())
        print(f"{desc}: {qty} shares @ ${price:.2f}")
    
    print(f"Average cost: ${position.price:.2f}")
    
    # Recovery scenarios
    recovery_prices = [10.0, 100.0, 1000.0, 10000.0]
    for recovery_price in recovery_prices:
        pnl = position.get_taxlot_pnl(200, recovery_price)
        print(f"PnL selling 200 @ ${recovery_price:.2f}: ${pnl:.2f}")
    
    print("\n4. Circuit Breaker Gaps:")
    print("-" * 60)
    position = Position()
    
    # Normal trading
    position.update(100, 100.0, datetime(2023, 1, 1, 9, 30))
    
    # Market opens with 20% gap down (circuit breaker triggered)
    gap_price = 80.0
    gap_pnl = position.get_taxlot_pnl(50, gap_price)
    print(f"PnL on 20% gap down: ${gap_pnl:.2f}")
    
    # Followed by limit up recovery
    limit_up_price = 120.0
    recovery_pnl = position.get_taxlot_pnl(50, limit_up_price)
    print(f"PnL on limit up recovery: ${recovery_pnl:.2f}")


def demo_high_frequency_timing_edge_cases():
    """Test high-frequency trading and timing edge cases"""
    
    print("\n" + "=" * 70)
    print("DEMO 10: High-Frequency Trading & Timing Edge Cases")
    print("=" * 70)
    
    print("\n1. Millisecond-Level Trading:")
    print("-" * 60)
    position = Position()
    
    # Simulate high-frequency trading with microsecond precision
    base_time = datetime(2023, 1, 1, 9, 30, 0, 0)
    
    for i in range(10):
        # Trades every 100 milliseconds
        trade_time = base_time + timedelta(microseconds=i * 100000)
        qty = 1000 + (i * 100)  # Varying quantities
        price = 100.0 + (i * 0.01)  # Micro price movements
        position.update(qty, price, trade_time)
        print(f"Trade {i+1}: {qty} @ ${price:.3f} at {trade_time.strftime('%H:%M:%S.%f')}")
    
    # Calculate PnL on rapid exit
    exit_pnl = position.get_taxlot_pnl(5000, 100.05)
    print(f"Rapid exit PnL: ${exit_pnl:.2f}")
    
    print("\n2. Exact Same Timestamp Trades:")
    print("-" * 60)
    position = Position()
    
    # Multiple trades at exactly the same time
    same_time = datetime(2023, 1, 1, 14, 30, 0, 123456)
    
    trades_same_time = [
        (100, 50.0),
        (200, 50.1),
        (150, 49.9),
        (75, 50.05),
    ]
    
    for qty, price in trades_same_time:
        position.update(qty, price, same_time)
        print(f"Same timestamp: {qty} @ ${price:.2f}")
    
    print(f"Final position from simultaneous trades: {position.size} @ ${position.price:.3f}")
    
    print("\n3. Time Zone Edge Cases:")
    print("-" * 60)
    position = Position()
    
    # Trades across different time zones
    utc_time = datetime(2023, 3, 12, 6, 0, 0)  # UTC
    est_time = datetime(2023, 3, 12, 2, 0, 0)  # EST (same moment, different TZ)
    pst_time = datetime(2023, 3, 11, 23, 0, 0)  # PST (same moment, different TZ)
    
    position.update(100, 100.0, utc_time)
    position.update(100, 101.0, est_time)
    position.update(100, 102.0, pst_time)
    
    print(f"Cross-timezone position: {position.size} @ ${position.price:.2f}")
    
    print("\n4. Leap Second Scenario:")
    print("-" * 60)
    # Simulate trading around leap second
    leap_second_time = datetime(2023, 12, 31, 23, 59, 59, 999999)
    post_leap = datetime(2024, 1, 1, 0, 0, 0, 0)
    
    position = Position()
    position.update(1000, 50.0, leap_second_time)
    position.update(1000, 50.1, post_leap)
    
    print(f"Leap second trading handled: {position.size} shares")


def demo_memory_stress_scalability():
    """Test memory stress and scalability limits"""
    
    print("\n" + "=" * 70)
    print("DEMO 11: Memory Stress & Scalability Limits")
    print("=" * 70)
    
    import time
    import sys
    
    print("\n1. Million Micro-Lots Stress Test:")
    print("-" * 60)
    position = Position()
    
    print("Creating 100,000 micro tax lots...")
    start_time = time.time()
    start_memory = sys.getsizeof(position.taxlots)
    
    # Create many tiny lots
    for i in range(100000):
        if i % 10000 == 0:
            print(f"  Progress: {i:,} lots created...")
        
        qty = 0.01  # Tiny quantities
        price = 100.0 + (i % 1000) * 0.001  # Micro price variations
        dt = datetime(2023, 1, 1) + timedelta(seconds=i)
        position.update(qty, price, dt)
    
    creation_time = time.time() - start_time
    end_memory = sys.getsizeof(position.taxlots)
    
    print(f"Created {len(position.taxlots):,} tax lots in {creation_time:.2f} seconds")
    print(f"Memory usage: {end_memory - start_memory:,} bytes")
    print(f"Average memory per lot: {(end_memory - start_memory) / len(position.taxlots):.2f} bytes")
    
    # Test PnL calculation performance on large dataset
    print("\nTesting PnL calculation on large dataset...")
    start_time = time.time()
    pnl = position.get_taxlot_pnl(500, 101.0)  # Sell 500 units
    calc_time = time.time() - start_time
    
    print(f"PnL calculation took {calc_time*1000:.2f} ms")
    print(f"Result: ${pnl:.2f}")
    
    print("\n2. Extreme Position Size:")
    print("-" * 60)
    position = Position()
    
    # Simulate institutional-level position
    billion_share_position = [
        (*********0, 50.0, "Billion share initial"),  # 1 billion shares
        (*********, 49.5, "Half billion more"),      # 500 million more
    ]
    
    for qty, price, desc in billion_share_position:
        position.update(qty, price, datetime.now())
        print(f"{desc}: {qty:,} shares @ ${price:.2f}")
    
    print(f"Mega position: {position.size:,} shares worth ${position.size * position.price:,.2f}")
    
    # Test PnL on partial sale
    mega_sale = *********  # 100 million share sale
    mega_pnl = position.get_taxlot_pnl(mega_sale, 51.0)
    print(f"PnL selling {mega_sale:,} shares: ${mega_pnl:,.2f}")


def demo_mathematical_edge_cases():
    """Test mathematical edge cases and error handling"""
    
    print("\n" + "=" * 70)
    print("DEMO 12: Mathematical Edge Cases & Error Handling")
    print("=" * 70)
    
    print("\n1. Floating Point Precision Limits:")
    print("-" * 60)
    position = Position()
    
    # Test with very large numbers approaching float limits
    huge_qty = 1e15  # 1 quadrillion
    tiny_price = 1e-10  # 0.0000000001
    
    position.update(huge_qty, tiny_price, datetime.now())
    position.update(1, 1e10, datetime.now())  # 10 billion price
    
    print(f"Extreme numbers position: {position.size:.2e} @ ${position.price:.2e}")
    
    pnl = position.get_taxlot_pnl(1e14, 1e-9)
    print(f"Extreme PnL calculation: ${pnl:.2e}")
    
    print("\n2. Division by Zero Protection:")
    print("-" * 60)
    position = Position()
    
    # Test edge cases that might cause division by zero
    position.update(0, 100.0, datetime.now())  # Zero quantity (should be handled)
    pnl = position.get_taxlot_pnl(0, 100.0)  # Zero sale quantity
    print(f"Zero quantity PnL: ${pnl:.2f} (should be 0)")
    
    print("\n3. NaN and Infinity Handling:")
    print("-" * 60)
    import math
    
    position = Position()
    position.update(100, 50.0, datetime.now())
    
    # Test with problematic values
    try:
        pnl_inf = position.get_taxlot_pnl(50, float('inf'))
        print(f"Infinity price PnL: ${pnl_inf}")
    except (ValueError, OverflowError) as e:
        print(f"Infinity price handled: {e}")
    
    try:
        pnl_nan = position.get_taxlot_pnl(50, float('nan'))
        print(f"NaN price PnL: ${pnl_nan}")
    except (ValueError, TypeError) as e:
        print(f"NaN price handled: {e}")
    
    print("\n4. Overflow/Underflow Conditions:")
    print("-" * 60)
    position = Position()
    
    # Test conditions that might cause overflow
    max_float = 1.7976931348623157e+308  # Near max float64
    min_float = 2.2250738585072014e-308  # Near min positive float64
    
    try:
        position.update(max_float, min_float, datetime.now())
        pnl = position.get_taxlot_pnl(max_float/2, min_float*2)
        print(f"Extreme range PnL: ${pnl:.2e}")
    except (OverflowError, ValueError) as e:
        print(f"Overflow condition handled: {e}")


def demo_corporate_actions_simulation():
    """Simulate corporate actions affecting tax lots"""
    
    print("\n" + "=" * 70)
    print("DEMO 13: Corporate Actions & Position Adjustments")
    print("=" * 70)
    
    print("\n1. Stock Split Simulation (2:1):")
    print("-" * 60)
    position = Position()
    
    # Pre-split position
    position.update(100, 100.0, datetime(2023, 1, 1))
    position.update(50, 120.0, datetime(2023, 2, 1))
    
    print("Pre-split position:")
    for i, lot in enumerate(position.taxlots):
        print(f"  Lot {i+1}: {lot.remaining_qty} @ ${lot.price:.2f}")
    
    # Simulate 2:1 split (double shares, halve price)
    print("\nSimulating 2:1 stock split...")
    for lot in position.taxlots:
        lot.qty *= 2
        lot.remaining_qty *= 2
        lot.price /= 2
    
    # Update position totals
    position.size *= 2
    position.price /= 2
    
    print("Post-split position:")
    for i, lot in enumerate(position.taxlots):
        print(f"  Lot {i+1}: {lot.remaining_qty} @ ${lot.price:.2f}")
    
    # Test PnL calculation post-split
    post_split_pnl = position.get_taxlot_pnl(100, 55.0)
    print(f"PnL selling 100 post-split shares @ $55: ${post_split_pnl:.2f}")
    
    print("\n2. Reverse Split Simulation (1:10):")
    print("-" * 60)
    position = Position()
    
    # Build position in struggling stock
    position.update(1000, 5.0, datetime(2023, 1, 1))
    position.update(2000, 3.0, datetime(2023, 3, 1))
    position.update(5000, 1.0, datetime(2023, 6, 1))
    
    print(f"Pre-reverse-split: {position.size} shares @ ${position.price:.2f}")
    
    # Simulate 1:10 reverse split
    for lot in position.taxlots:
        lot.qty /= 10
        lot.remaining_qty /= 10
        lot.price *= 10
    
    position.size /= 10
    position.price *= 10
    
    print(f"Post-reverse-split: {position.size} shares @ ${position.price:.2f}")
    
    print("\n3. Spin-off Simulation:")
    print("-" * 60)
    position = Position()
    
    # Original company position
    position.update(100, 200.0, datetime(2023, 1, 1))
    print(f"Original position: {position.size} shares @ ${position.price:.2f}")
    
    # Spin-off creates new position (simulate by adjusting cost basis)
    spinoff_value = 50.0  # $50 of value spun off per share
    
    print(f"Spin-off value: ${spinoff_value} per share")
    
    # Adjust original position cost basis
    for lot in position.taxlots:
        lot.price -= spinoff_value
    
    position.price -= spinoff_value
    
    print(f"Adjusted original: {position.size} shares @ ${position.price:.2f}")
    print("(Spin-off company would be tracked separately)")
    
    print("\n4. Merger Simulation (Cash + Stock):")
    print("-" * 60)
    position = Position()
    
    # Target company position
    position.update(100, 80.0, datetime(2023, 1, 1))
    position.update(50, 90.0, datetime(2023, 3, 1))
    
    print("Target company position before merger:")
    for i, lot in enumerate(position.taxlots):
        print(f"  Lot {i+1}: {lot.remaining_qty} @ ${lot.price:.2f}")
    
    # Merger terms: $50 cash + 0.5 shares of acquirer @ $100
    cash_per_share = 50.0
    stock_ratio = 0.5
    acquirer_price = 100.0
    
    merger_value = cash_per_share + (stock_ratio * acquirer_price)
    print(f"Merger value per share: ${merger_value:.2f}")
    
    # Calculate PnL as if selling at merger value
    merger_pnl = position.get_taxlot_pnl(position.size, merger_value)
    print(f"Total merger PnL: ${merger_pnl:.2f}")


def demo_regulatory_compliance_edge_cases():
    """Test regulatory and compliance edge cases"""
    
    print("\n" + "=" * 70)
    print("DEMO 14: Regulatory & Compliance Edge Cases")
    print("=" * 70)
    
    print("\n1. Pattern Day Trader Scenario:")
    print("-" * 60)
    position = Position()
    
    # Simulate day trading pattern (4+ day trades in 5 days)
    day_trades = [
        (1000, 100.0, "Day 1 - Morning buy"),
        (-1000, 101.0, "Day 1 - Afternoon sell"),
        (800, 99.0, "Day 2 - Buy dip"),
        (-800, 102.0, "Day 2 - Quick exit"),
        (1200, 98.0, "Day 3 - Larger position"),
        (-1200, 103.0, "Day 3 - Profit taking"),
        (500, 97.0, "Day 4 - Small trade"),
        (-500, 104.0, "Day 4 - Day trade #4"),
    ]
    
    for i, (qty, price, desc) in enumerate(day_trades):
        dt = datetime(2023, 1, 1) + timedelta(days=i//2, hours=(i%2)*6)
        if qty > 0:
            position.update(qty, price, dt)
            print(f"{desc}: BUY {qty}")
        else:
            sale_qty = abs(qty)
            pnl = position.get_taxlot_pnl(sale_qty, price)
            position.update(qty, price, dt)
            print(f"{desc}: SELL {sale_qty}, PnL: ${pnl:.2f}")
    
    print("Pattern day trading compliance: 4+ day trades completed")
    
    print("\n2. Wash Sale Violation Simulation:")
    print("-" * 60)
    position = Position()
    
    # Build losing position
    position.update(100, 100.0, datetime(2023, 1, 1))
    position.update(100, 90.0, datetime(2023, 1, 15))
    
    # Sell at loss
    loss_date = datetime(2023, 2, 1)
    loss_pnl = position.get_taxlot_pnl(150, 80.0)
    print(f"Loss sale on {loss_date.strftime('%Y-%m-%d')}: ${loss_pnl:.2f}")
    
    # Buy back within 30 days (potential wash sale)
    rebuy_date = datetime(2023, 2, 15)  # 14 days later
    days_between = (rebuy_date - loss_date).days
    
    print(f"Rebuy on {rebuy_date.strftime('%Y-%m-%d')} ({days_between} days later)")
    if days_between < 31:
        print("⚠️  POTENTIAL WASH SALE VIOLATION (< 31 days)")
    else:
        print("✓ No wash sale violation")
    
    print("\n3. Short Sale Uptick Rule:")
    print("-" * 60)
    # Simulate short sale restrictions
    prices = [100.0, 99.5, 99.0, 98.5]  # Declining prices
    
    for i, price in enumerate(prices):
        if i == 0:
            print(f"Tick {i+1}: ${price:.2f} (initial)")
        else:
            prev_price = prices[i-1]
            if price > prev_price:
                tick = "uptick ✓"
            elif price < prev_price:
                tick = "downtick ⚠️"
            else:
                tick = "no change"
            
            print(f"Tick {i+1}: ${price:.2f} ({tick})")
            
            if tick == "downtick ⚠️":
                print("  Short sale restricted - uptick rule in effect")
    
    print("\n4. Position Limit Compliance:")
    print("-" * 60)
    position = Position()
    
    # Simulate approaching position limits
    position_limit = 10000  # shares
    
    large_trades = [
        (3000, 50.0, "Initial large position"),
        (4000, 52.0, "Increase position"),
        (2500, 48.0, "Near limit"),
    ]
    
    for qty, price, desc in large_trades:
        position.update(qty, price, datetime.now())
        print(f"{desc}: {qty} shares, Total: {position.size}")
        
        if position.size > position_limit * 0.9:  # 90% of limit
            print(f"  ⚠️  Approaching position limit ({position.size}/{position_limit})")
        
        if position.size > position_limit:
            print(f"  🚫 POSITION LIMIT EXCEEDED!")


def demo_cross_asset_multi_currency():
    """Test cross-asset and multi-currency scenarios"""
    
    print("\n" + "=" * 70)
    print("DEMO 15: Cross-Asset & Multi-Currency Scenarios")
    print("=" * 70)
    
    print("\n1. Multi-Currency Position (USD/EUR/JPY):")
    print("-" * 60)
    
    # Simulate trading same stock in different currencies
    positions = {
        'USD': Position(),
        'EUR': Position(),
        'JPY': Position(),
    }
    
    # Exchange rates (to USD)
    fx_rates = {
        'USD': 1.0,
        'EUR': 1.0847,  # EUR/USD
        'JPY': 0.0067,  # JPY/USD (1 JPY = 0.0067 USD)
    }
    
    # Trades in different currencies
    trades = [
        ('USD', 100, 100.0, "US market"),
        ('EUR', 100, 92.19, "European market (€92.19)"),  # ~$100 USD equivalent
        ('JPY', 100, 14925, "Japanese market (¥14,925)"),  # ~$100 USD equivalent
    ]
    
    total_usd_value = 0
    for currency, qty, price, desc in trades:
        positions[currency].update(qty, price, datetime.now())
        usd_equivalent = price * fx_rates[currency]
        total_usd_value += qty * usd_equivalent
        print(f"{desc}: {qty} @ {price} {currency} (${usd_equivalent:.2f} USD equiv)")
    
    print(f"Total USD equivalent value: ${total_usd_value:.2f}")
    
    # Calculate PnL in different currencies
    print("\nPnL calculations in local currencies:")
    sale_prices = {'USD': 105.0, 'EUR': 96.80, 'JPY': 15671}  # 5% gains
    
    for currency in positions:
        pnl = positions[currency].get_taxlot_pnl(50, sale_prices[currency])
        usd_pnl = pnl * fx_rates[currency]
        print(f"{currency}: {pnl:.2f} {currency} (${usd_pnl:.2f} USD)")
    
    print("\n2. Options Assignment Creating Stock Position:")
    print("-" * 60)
    position = Position()
    
    # Simulate option assignment creating stock position
    option_strike = 95.0
    option_premium = 3.50
    
    # Effective cost basis = strike + premium for call assignment
    effective_cost = option_strike + option_premium
    
    position.update(100, effective_cost, datetime.now())
    print(f"Call option assigned: 100 shares @ ${effective_cost:.2f} effective cost")
    print(f"(Strike: ${option_strike:.2f} + Premium: ${option_premium:.2f})")
    
    # Calculate PnL if selling immediately
    current_price = 102.0
    assignment_pnl = position.get_taxlot_pnl(100, current_price)
    print(f"PnL if sold @ ${current_price:.2f}: ${assignment_pnl:.2f}")
    
    print("\n3. Margin Call Forced Liquidation:")
    print("-" * 60)
    position = Position()
    
    # Build leveraged position
    position.update(1000, 100.0, datetime(2023, 1, 1))
    position.update(500, 80.0, datetime(2023, 2, 1))   # Averaging down
    position.update(1000, 60.0, datetime(2023, 3, 1))  # More averaging down
    
    print(f"Leveraged position: {position.size} shares @ ${position.price:.2f}")
    
    # Market drops, triggering margin call
    margin_call_price = 45.0
    forced_sale_qty = 1500  # Forced to sell 1500 shares
    
    forced_pnl = position.get_taxlot_pnl(forced_sale_qty, margin_call_price)
    print(f"Forced liquidation PnL @ ${margin_call_price:.2f}: ${forced_pnl:.2f}")
    
    # Show which lots were liquidated (FIFO)
    print("FIFO lots liquidated:")
    remaining = forced_sale_qty
    for i, lot in enumerate(position.taxlots):
        if remaining <= 0:
            break
        qty_sold = min(lot.remaining_qty, remaining)
        lot_pnl = (margin_call_price - lot.price) * qty_sold
        remaining -= qty_sold
        print(f"  Lot {i+1}: {qty_sold} shares @ ${lot.price:.2f} = ${lot_pnl:.2f}")
    
    print("\n4. Portfolio Rebalancing Scenario:")
    print("-" * 60)
    
    # Simulate portfolio with multiple positions
    stocks = ['AAPL', 'GOOGL', 'MSFT', 'AMZN']
    portfolio = {}
    
    for stock in stocks:
        portfolio[stock] = Position()
        # Different entry points and sizes
        portfolio[stock].update(100, 150.0, datetime(2023, 1, 1))
        portfolio[stock].update(50, 160.0, datetime(2023, 2, 1))
    
    print("Portfolio before rebalancing:")
    total_value = 0
    current_prices = {'AAPL': 170.0, 'GOOGL': 140.0, 'MSFT': 165.0, 'AMZN': 155.0}
    
    for stock in stocks:
        pos = portfolio[stock]
        value = pos.size * current_prices[stock]
        total_value += value
        print(f"{stock}: {pos.size} shares @ ${pos.price:.2f} avg, value: ${value:,.2f}")
    
    print(f"Total portfolio value: ${total_value:,.2f}")
    
    # Rebalance to equal weights (25% each)
    target_value_per_stock = total_value / 4
    
    print(f"\nRebalancing to ${target_value_per_stock:,.2f} per stock:")
    
    for stock in stocks:
        pos = portfolio[stock]
        current_value = pos.size * current_prices[stock]
        target_shares = target_value_per_stock / current_prices[stock]
        
        if current_value > target_value_per_stock:
            # Need to sell
            shares_to_sell = pos.size - target_shares
            pnl = pos.get_taxlot_pnl(shares_to_sell, current_prices[stock])
            print(f"{stock}: SELL {shares_to_sell:.0f} shares, PnL: ${pnl:.2f}")
        else:
            # Need to buy
            shares_to_buy = target_shares - pos.size
            print(f"{stock}: BUY {shares_to_buy:.0f} shares")


if __name__ == '__main__':
    demo_basic_pnl_comparison()
    demo_edge_cases()
    demo_position_reversal()
    demo_complex_trading_scenarios()
    demo_tax_optimization()
    demo_broker_integration()
    demo_performance_stress_test()
    demo_extreme_precision_edge_cases()
    demo_market_crisis_scenarios()
    demo_high_frequency_timing_edge_cases()
    demo_memory_stress_scalability()
    demo_mathematical_edge_cases()
    demo_corporate_actions_simulation()
    demo_regulatory_compliance_edge_cases()
    demo_cross_asset_multi_currency()
    
    print("\n" + "=" * 70)
    print("ALL DEMOS COMPLETE - COMPREHENSIVE EDGE CASE TESTING")
    print("=" * 70)
    print("\nExotic Edge Cases Covered:")
    print("• Extreme precision & rounding errors")
    print("• Market crisis & negative prices")
    print("• High-frequency & timing anomalies")
    print("• Memory stress & scalability limits")
    print("• Mathematical edge cases & error handling")
    print("• Corporate actions & position adjustments")
    print("• Regulatory compliance scenarios")
    print("• Multi-currency & cross-asset situations")
    print("\nKey Benefits of Tax Lot Aware PnL:")
    print("• More accurate cost basis tracking")
    print("• Better tax optimization opportunities")
    print("• FIFO compliance for tax reporting")
    print("• Detailed lot-level PnL analysis")
    print("• Backward compatible with existing code")
    print("• Handles complex trading scenarios")
    print("• Robust edge case handling")
    print("• Performance optimized for large portfolios")
    print("• Production-ready for institutional use") 