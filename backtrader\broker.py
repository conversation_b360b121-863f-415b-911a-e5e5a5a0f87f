#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
###############################################################################
#
# Copyright (C) 2015-2023 <PERSON>
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
#
###############################################################################
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

from backtrader.comminfo import CommInfoBase
from backtrader.metabase import MetaParams
from backtrader.utils.py3 import with_metaclass

from . import fillers as fillers
from . import fillers as filler


class MetaBroker(MetaParams):
    def __init__(cls, name, bases, dct):
        '''
        Class has already been created ... fill missing methods if needed be
        '''
        # Initialize the class
        super(MetaBroker, cls).__init__(name, bases, dct)
        translations = {
            'get_cash': 'getcash',
            'get_value': 'getvalue',
        }

        for attr, trans in translations.items():
            if not hasattr(cls, attr):
                setattr(cls, name, getattr(cls, trans))


class BrokerBase(with_metaclass(MetaBroker, object)):
    '''
    Base class for brokers.

    Any broker implementation has to override the ``buy``, ``sell``, ``cancel``
    and ``get_orders_open`` methods

    '''
    params = (
        ('commission', CommInfoBase(percabs=True)),
    )

    def __init__(self):
        self.comminfo = dict()
        self.orders = list()  # will only be appending
        self.notifs = list()  # will only be appending
        self.submitted = list()  # will only be appending
        self.init()

    def init(self):
        # called from init and from start
        if None not in self.comminfo:
            self.comminfo = dict({None: self.p.commission})

    def start(self):
        '''Called before any data is received'''
        self.init()

    def stop(self):
        '''Called after any data has been received'''
        pass

    def add_order_history(self, orders, notify=False):
        '''Add order history. See cerebro for details'''
        raise NotImplementedError

    def set_fund_history(self, fund):
        '''Add fund history. See cerebro for details'''
        raise NotImplementedError

    def getcommissioninfo(self, data):
        '''Retrieves the ``CommissionInfo`` scheme associated with the given
        ``data``'''
        if data._name in self.comminfo:
            return self.comminfo[data._name]

        return self.comminfo[None]

    def setcommission(self,
                      commission=0.0, margin=None, mult=1.0,
                      commtype=None, percabs=True, stocklike=False,
                      interest=0.0, interest_long=False, leverage=1.0,
                      automargin=False,
                      name=None):

        '''This method sets a `` CommissionInfo`` object for assets managed in
        the broker with the parameters. Consult the reference for
        ``CommInfoBase``

        If name is ``None``, this will be the default for assets for which no
        other ``CommissionInfo`` scheme can be found
        '''

        comm = CommInfoBase(commission=commission, margin=margin, mult=mult,
                            commtype=commtype, stocklike=stocklike,
                            percabs=percabs,
                            interest=interest, interest_long=interest_long,
                            leverage=leverage, automargin=automargin)
        self.comminfo[name] = comm

    def addcommissioninfo(self, comminfo, name=None):
        '''Adds a ``CommissionInfo`` object that will be the default for all assets if
        ``name`` is ``None``'''
        self.comminfo[name] = comminfo

    def getcash(self):
        '''Returns the available cash'''
        raise NotImplementedError

    def getvalue(self, datas=None):
        '''Returns the value of the broker positions'''
        raise NotImplementedError

    def get_fundshares(self):
        '''Returns the current number of shares in the fund-like mode'''
        return 1.0  # the abstract mode has only 1 share

    fundshares = property(get_fundshares)

    def get_fundvalue(self):
        return self.getvalue()

    fundvalue = property(get_fundvalue)

    def set_fundmode(self, fundmode, fundstartval=None):
        '''Set the actual fundmode (True or False)

        If the argument fundstartval is not ``None``, it will used
        '''
        pass  # do nothing, not all brokers can support this

    def get_fundmode(self):
        '''Returns the actual fundmode (True or False)'''
        return False

    fundmode = property(get_fundmode, set_fundmode)

    def getposition(self, data):
        raise NotImplementedError

    def submit(self, order):
        raise NotImplementedError

    def cancel(self, order):
        '''Receives an order instance and cancels it'''
        raise NotImplementedError

    def buy(self, owner, data, size, price=None, plimit=None,
            exectype=None, valid=None, tradeid=0, oco=None,
            trailamount=None, trailpercent=None,
            parent=None, transmit=True,
            **kwargs):
        '''
        To buy size contracts of data at price.

        Parameters:

          - ``owner``: the owner of the order (a ``Strategy`` instance)

          - ``data``: the ``Data`` instance the order refers to

          - ``size``: the size (positive) to buy

          - ``price``: the price to pay for the order

          - ``plimit``: the limit price for the order (if applicable)

          - ``exectype``: the execution type for the order

          - ``valid``: the validity for the order

          - ``tradeid``: the tradeid for the order

          - ``oco``: the order to use as the other side of an OCO order

          - ``trailamount``: the trail amount for a trailing stop order

          - ``trailpercent``: the trail percent for a trailing stop order

          - ``parent``: the parent order for a bracket order

          - ``transmit``: whether to transmit the order

        Returns:
          - An order instance

        '''
        raise NotImplementedError

    def sell(self, owner, data, size, price=None, plimit=None,
             exectype=None, valid=None, tradeid=0, oco=None,
             trailamount=None, trailpercent=None,
             parent=None, transmit=True,
             **kwargs):
        '''
        To sell size contracts of data at price.

        Parameters:

          - ``owner``: the owner of the order (a ``Strategy`` instance)

          - ``data``: the ``Data`` instance the order refers to

          - ``size``: the size (positive) to sell

          - ``price``: the price to pay for the order

          - ``plimit``: the limit price for the order (if applicable)

          - ``exectype``: the execution type for the order

          - ``valid``: the validity for the order

          - ``tradeid``: the tradeid for the order

          - ``oco``: the order to use as the other side of an OCO order

          - ``trailamount``: the trail amount for a trailing stop order

          - ``trailpercent``: the trail percent for a trailing stop order

          - ``parent``: the parent order for a bracket order

          - ``transmit``: whether to transmit the order

        Returns:
          - An order instance

        '''
        raise NotImplementedError

    def get_orders_open(self, safe=False):
        '''
        Returns an iterable with the orders which are still open (either not
        executed or partially executed

        The orders returned must not be touched.

        If order manipulation is needed, set the parameter ``safe`` to True
        '''
        raise NotImplementedError

    def get_notification(self):
        '''Returns the next notification in the queue. Returns None if no
        notification is available'''
        try:
            return self.notifs.pop(0)
        except IndexError:
            pass

        return None

    def next(self):
        '''Called to notify the broker that the next prices are available'''
        pass

# __all__ = ['BrokerBase', 'fillers', 'filler']


class TaxAwareBrokerMixin:
    """
    Mixin class that provides tax lot aware PnL calculation methods for brokers.
    
    This mixin can be added to any broker implementation to enable tax lot
    aware profit and loss calculations using FIFO cost basis instead of
    average position price.
    
    Usage:
        class MyTaxAwareBroker(TaxAwareBrokerMixin, BackBroker):
            pass
    """
    
    def get_taxlot_pnl(self, position, sale_qty, sale_price):
        """
        Calculate PnL using tax lot specific costs (FIFO) instead of average price.
        
        This method provides tax lot aware PnL calculation that can be used
        instead of the standard comminfo.profitandloss() method.
        
        Args:
            position: Position object with tax lot information
            sale_qty (float): Quantity being sold (positive number)
            sale_price (float): Price at which shares are being sold
            
        Returns:
            float: Tax lot aware PnL for the sale
            
        Example:
            # In broker execution method:
            if hasattr(position, 'get_taxlot_pnl'):
                pnl = self.get_taxlot_pnl(position, abs(closed), price)
            else:
                pnl = comminfo.profitandloss(-closed, pprice_orig, price)
        """
        if hasattr(position, 'get_taxlot_pnl'):
            return position.get_taxlot_pnl(sale_qty, sale_price)
        else:
            # Fallback to standard PnL calculation if no tax lots
            return 0.0
    
    def get_realized_unrealized_pnl(self, position, current_price):
        """
        Calculate both realized and unrealized PnL using tax lot information.
        
        Args:
            position: Position object with tax lot information
            current_price (float): Current market price
            
        Returns:
            tuple: (realized_pnl, unrealized_pnl)
                - realized_pnl: PnL from closed positions (historical)
                - unrealized_pnl: PnL from current open position using tax lots
        """
        if not hasattr(position, 'taxlots') or not position.taxlots:
            # No tax lots available, use standard calculation
            if position.size != 0:
                unrealized_pnl = position.size * (current_price - position.price)
                return 0.0, unrealized_pnl
            return 0.0, 0.0
        
        # Calculate unrealized PnL using tax lot costs
        unrealized_pnl = 0.0
        for lot in position.taxlots:
            if lot.remaining_qty > 0:
                lot_unrealized = (current_price - lot.price) * lot.remaining_qty
                unrealized_pnl += lot_unrealized
        
        # Realized PnL would need to be tracked separately (future enhancement)
        realized_pnl = 0.0
        
        return realized_pnl, unrealized_pnl
    
    def is_taxlot_enabled(self, position):
        """
        Check if tax lot tracking is enabled for a position.
        
        Args:
            position: Position object to check
            
        Returns:
            bool: True if tax lot tracking is available and enabled
        """
        return hasattr(position, 'taxlots') and hasattr(position, 'get_taxlot_pnl')
