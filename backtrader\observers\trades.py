#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
###############################################################################
#
# Copyright (C) 2015-2023 <PERSON>
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
#
###############################################################################
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import uuid
import datetime

from .. import Observer
from ..utils.py3 import with_metaclass

from ..trade import Trade


class Trades(Observer):
    '''This observer keeps track of full trades and plot the PnL level achieved
    when a trade is closed.

    A trade is open when a position goes from 0 (or crossing over 0) to X and
    is then closed when it goes back to 0 (or crosses over 0 in the opposite
    direction)

    Params:
      - ``pnlcomm`` (def: ``True``)

        Show net/profit and loss, i.e.: after commission. If set to ``False``
        if will show the result of trades before commission
        
      - ``use_taxlot_pnl`` (def: ``False``)
      
        Use tax lot aware P&L calculations instead of average trade P&L.
        When enabled, uses actual FIFO lot costs for more accurate P&L reporting.
        
      - ``show_lot_details`` (def: ``False``)
      
        Include tax lot allocation details in trade reporting.
        Shows which specific lots were used for each closed trade.
        
      - ``tax_efficiency_metrics`` (def: ``False``)
      
        Calculate and display tax efficiency metrics such as tax drag,
        holding period analysis, and lot-level performance indicators.

    Enhanced Features (when tax lot parameters are enabled):
      - Uses actual FIFO lot costs instead of average trade prices
      - Shows tax lot allocation details for each closed trade
      - Calculates tax efficiency metrics and lot-level analysis
      - Enhanced plotting with tax-specific visual indicators
      - European market compliance (no US holding period distinctions)
    '''
    _stclock = True

    lines = ('pnlplus', 'pnlminus', 'taxlot_pnlplus', 'taxlot_pnlminus')

    params = (
        ('pnlcomm', True),
        ('use_taxlot_pnl', False),
        ('show_lot_details', False),
        ('tax_efficiency_metrics', False),
    )

    plotinfo = dict(plot=True, subplot=True,
                    plotname='Trades - Net Profit/Loss',
                    plotymargin=0.10,
                    plothlines=[0.0])

    plotlines = dict(
        pnlplus=dict(_name='Positive',
                     ls='', marker='o', color='blue',
                     markersize=8.0, fillstyle='full'),
        pnlminus=dict(_name='Negative',
                      ls='', marker='o', color='red',
                      markersize=8.0, fillstyle='full'),
        taxlot_pnlplus=dict(_name='TaxLot Positive',
                           ls='', marker='s', color='lightblue',
                           markersize=6.0, fillstyle='full'),
        taxlot_pnlminus=dict(_name='TaxLot Negative',
                            ls='', marker='s', color='lightcoral',
                            markersize=6.0, fillstyle='full')
    )

    def __init__(self, *args, **kwargs):
        # Call parent class init to handle standard Backtrader parameters
        super(Trades, self).__init__(*args, **kwargs)
        
        # Basic trade statistics (backward compatible)
        self.trades = 0
        self.trades_long = 0
        self.trades_short = 0
        self.trades_plus = 0
        self.trades_minus = 0
        self.trades_plus_gross = 0
        self.trades_minus_gross = 0
        self.trades_win = 0
        self.trades_win_max = 0
        self.trades_win_min = 0
        self.trades_loss = 0
        self.trades_loss_max = 0
        self.trades_loss_min = 0
        self.trades_length = 0
        self.trades_length_max = 0
        self.trades_length_min = 0
        
        # Enhanced tax lot statistics (when enabled)
        if self.p.use_taxlot_pnl or self.p.show_lot_details or self.p.tax_efficiency_metrics:
            self.taxlot_trades = 0
            self.taxlot_pnl_total = 0.0
            self.taxlot_pnl_average = 0.0
            self.lot_details_history = []
            self.tax_efficiency_data = {
                'fifo_pnl_total': 0.0,
                'avg_pnl_total': 0.0,
                'tax_drag': 0.0,
                'lot_count_total': 0,
                'avg_days_held': 0.0
            }

    def _get_current_datetime(self):
        '''Get current backtest datetime (never use system time!)'''
        try:
            if hasattr(self.strategy, 'datetime') and hasattr(self.strategy.datetime, 'datetime'):
                return self.strategy.datetime.datetime()
            elif len(self.strategy.datas) > 0 and hasattr(self.strategy.datas[0], 'datetime'):
                return self.strategy.datas[0].datetime.datetime()
        except (AttributeError, IndexError):
            pass
        return None

    def _get_taxlot_trade_pnl(self, trade):
        '''Calculate P&L using tax lot FIFO costs instead of average trade price'''
        if not (self.p.use_taxlot_pnl or self.p.show_lot_details or self.p.tax_efficiency_metrics):
            return None, None
            
        try:
            # Get position from broker
            position = getattr(self._owner.broker, 'positions', {}).get(trade.data, None)
            if not position or not hasattr(position, 'get_taxlots'):
                return None, None
                
            # For closed trades, we need to calculate the P&L using FIFO lot costs
            if hasattr(position, 'get_taxlot_pnl'):
                # Use the enhanced position method for tax lot P&L
                closed_qty = abs(trade.size)
                sale_price = trade.price
                
                fifo_pnl = position.get_taxlot_pnl(closed_qty, sale_price)
                
                # Get lot details if requested
                lot_details = None
                if self.p.show_lot_details:
                    lot_details = self._extract_lot_details(position, closed_qty, sale_price)
                
                return fifo_pnl, lot_details
        except Exception:
            # If any error occurs, gracefully fall back to None
            pass
            
        return None, None

    def _extract_lot_details(self, position, closed_qty, sale_price):
        '''Extract detailed information about which tax lots were used'''
        try:
            current_dt = self._get_current_datetime()
            if not current_dt:
                return None
                
            lot_details = {
                'lots_used': [],
                'total_lots': 0,
                'avg_cost_basis': 0.0,
                'avg_days_held': 0.0,
                'realized_pnl': 0.0
            }
            
            taxlots = position.get_taxlots()
            remaining_qty = closed_qty
            total_cost_weighted = 0.0
            total_days_weighted = 0.0
            
            for lot in taxlots:
                if lot.remaining_qty > 0 and remaining_qty > 0:
                    lot_qty_used = min(lot.remaining_qty, remaining_qty)
                    weight = lot_qty_used / closed_qty
                    
                    # Calculate days held for this lot
                    days_held = 0
                    if lot.datetime and current_dt:
                        if hasattr(lot.datetime, 'date'):
                            lot_dt = lot.datetime
                        elif hasattr(lot.datetime, 'year'):
                            lot_dt = lot.datetime
                        else:
                            lot_dt = datetime.datetime.combine(lot.datetime, datetime.time())
                        days_held = (current_dt - lot_dt).days
                    
                    lot_info = {
                        'qty_used': lot_qty_used,
                        'cost_basis': lot.price,
                        'acquisition_date': lot.datetime,
                        'days_held': days_held,
                        'lot_pnl': lot_qty_used * (sale_price - lot.price)
                    }
                    
                    lot_details['lots_used'].append(lot_info)
                    lot_details['total_lots'] += 1
                    total_cost_weighted += lot.price * weight
                    total_days_weighted += days_held * weight
                    lot_details['realized_pnl'] += lot_info['lot_pnl']
                    
                    remaining_qty -= lot_qty_used
                    if remaining_qty <= 0:
                        break
            
            lot_details['avg_cost_basis'] = total_cost_weighted
            lot_details['avg_days_held'] = total_days_weighted
            
            return lot_details
            
        except Exception:
            return None

    def _update_tax_efficiency_metrics(self, fifo_pnl, trade_pnl, lot_details):
        '''Update tax efficiency metrics for analysis'''
        if not self.p.tax_efficiency_metrics:
            return
            
        try:
            self.tax_efficiency_data['fifo_pnl_total'] += fifo_pnl or 0.0
            self.tax_efficiency_data['avg_pnl_total'] += trade_pnl or 0.0
            
            if lot_details:
                self.tax_efficiency_data['lot_count_total'] += lot_details['total_lots']
                
                # Update average days held (weighted by number of trades)
                if self.taxlot_trades > 0:
                    current_avg = self.tax_efficiency_data['avg_days_held']
                    new_avg = lot_details['avg_days_held']
                    weight = 1.0 / (self.taxlot_trades + 1)
                    self.tax_efficiency_data['avg_days_held'] = current_avg * (1 - weight) + new_avg * weight
                else:
                    self.tax_efficiency_data['avg_days_held'] = lot_details['avg_days_held']
            
            # Calculate tax drag (difference between FIFO and average method)
            if self.tax_efficiency_data['avg_pnl_total'] != 0:
                fifo_total = self.tax_efficiency_data['fifo_pnl_total']
                avg_total = self.tax_efficiency_data['avg_pnl_total']
                self.tax_efficiency_data['tax_drag'] = (avg_total - fifo_total) / abs(avg_total)
                
        except Exception:
            # Gracefully handle any calculation errors
            pass

    def next(self):
        for trade in self._owner._tradespending:
            if trade.data not in self.ddatas:
                continue

            if not trade.isclosed:
                continue

            # Standard P&L calculation (backward compatible)
            pnl = trade.pnlcomm if self.p.pnlcomm else trade.pnl

            # Enhanced tax lot P&L calculation
            fifo_pnl, lot_details = self._get_taxlot_trade_pnl(trade)
            
            # Use tax lot P&L if available and enabled, otherwise fall back to standard
            display_pnl = fifo_pnl if (fifo_pnl is not None and self.p.use_taxlot_pnl) else pnl

            # Plot standard P&L (backward compatible)
            if pnl >= 0.0:
                self.lines.pnlplus[0] = pnl
            else:
                self.lines.pnlminus[0] = pnl
                
            # Plot enhanced tax lot P&L if available
            if fifo_pnl is not None:
                if fifo_pnl >= 0.0:
                    self.lines.taxlot_pnlplus[0] = fifo_pnl
                else:
                    self.lines.taxlot_pnlminus[0] = fifo_pnl
                    
                # Update enhanced statistics
                self.taxlot_trades += 1
                self.taxlot_pnl_total += fifo_pnl
                self.taxlot_pnl_average = self.taxlot_pnl_total / self.taxlot_trades
                
                # Store lot details for analysis
                if self.p.show_lot_details and lot_details:
                    lot_details['trade_info'] = {
                        'date': self._get_current_datetime(),
                        'size': trade.size,
                        'price': trade.price,
                        'standard_pnl': pnl,
                        'fifo_pnl': fifo_pnl
                    }
                    self.lot_details_history.append(lot_details)
                
                # Update tax efficiency metrics
                self._update_tax_efficiency_metrics(fifo_pnl, pnl, lot_details)

    def get_taxlot_analysis(self):
        '''Return comprehensive tax lot analysis results'''
        if not (self.p.use_taxlot_pnl or self.p.show_lot_details or self.p.tax_efficiency_metrics):
            return None
            
        analysis = {
            'summary': {
                'total_taxlot_trades': self.taxlot_trades,
                'taxlot_pnl_total': self.taxlot_pnl_total,
                'taxlot_pnl_average': self.taxlot_pnl_average,
                'analysis_datetime': self._get_current_datetime()
            }
        }
        
        if self.p.show_lot_details:
            analysis['lot_details'] = self.lot_details_history
            
        if self.p.tax_efficiency_metrics:
            analysis['tax_efficiency'] = self.tax_efficiency_data.copy()
            
        return analysis


class MetaDataTrades(Observer.__class__):
    def donew(cls, *args, **kwargs):
        _obj, args, kwargs = super(MetaDataTrades, cls).donew(*args, **kwargs)

        # Recreate the lines dynamically
        if _obj.params.usenames:
            lnames = tuple(x._name for x in _obj.datas)
        else:
            lnames = tuple('data{}'.format(x) for x in range(len(_obj.datas)))

        # Generate a new lines class
        linescls = cls.lines._derive(uuid.uuid4().hex, lnames, 0, ())

        # Instantiate lines
        _obj.lines = linescls()

        # Generate plotlines info
        markers = ['o', 'v', '^', '<', '>', '1', '2', '3', '4', '8', 's', 'p',
                   '*', 'h', 'H', '+', 'x', 'D', 'd']

        colors = ['b', 'g', 'r', 'c', 'm', 'y', 'k', 'b', 'g', 'r', 'c', 'm',
                  'y', 'k', 'b', 'g', 'r', 'c', 'm']

        basedict = dict(ls='', markersize=8.0, fillstyle='full')

        plines = dict()
        for lname, marker, color in zip(lnames, markers, colors):
            plines[lname] = d = basedict.copy()
            d.update(marker=marker, color=color)

        plotlines = cls.plotlines._derive(
            uuid.uuid4().hex, plines, [], recurse=True)
        _obj.plotlines = plotlines()

        return _obj, args, kwargs  # return the instantiated object and args


class DataTrades(with_metaclass(MetaDataTrades, Observer)):
    _stclock = True

    params = (('usenames', True),)

    plotinfo = dict(plot=True, subplot=True, plothlines=[0.0],
                    plotymargin=0.10)

    plotlines = dict()

    def next(self):
        for trade in self._owner._tradespending:
            if trade.data not in self.ddatas:
                continue

            if not trade.isclosed:
                continue

            self.lines[trade.data._id - 1][0] = trade.pnl
