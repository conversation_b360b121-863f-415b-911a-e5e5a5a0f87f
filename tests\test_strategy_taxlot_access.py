#!/usr/bin/env python
# -*- coding: utf-8; py-indent-offset:4 -*-
"""
Test Strategy Tax Lot Access - Phase 2.1 Implementation

This test verifies that strategies can access tax lot information
through the new get_position_lots() methods with comprehensive edge case coverage.
"""

import unittest
import datetime
import backtrader as bt
from backtrader import Strategy


class TestTaxLotStrategy(Strategy):
    """Test strategy that uses tax lot access methods"""
    
    def __init__(self):
        self.lots_accessed = []
        self.lots_by_name_accessed = []
        self.order_count = 0
    
    def next(self):
        if self.order_count == 0:
            # Buy some shares to create tax lots
            self.buy(size=100)
            self.order_count += 1
        elif self.order_count == 1 and len(self.data) > 5:
            # Buy more shares to create additional tax lots
            self.buy(size=50)
            self.order_count += 1
        elif self.order_count == 2 and len(self.data) > 10:
            # Access tax lots using our new methods
            lots = self.get_position_lots()
            self.lots_accessed = lots
            
            # Also test by name access
            lots_by_name = self.get_position_lots_by_name()
            self.lots_by_name_accessed = lots_by_name


class NoTradeStrategy(Strategy):
    """Strategy that doesn't trade, used to test empty lots"""
    def __init__(self):
        self.lots_checked = False
        self.empty_lots = []
    
    def next(self):
        if not self.lots_checked:
            lots = self.get_position_lots()
            self.empty_lots = lots  # Should be empty
            self.lots_checked = True


class MultiDataStrategy(Strategy):
    """Strategy that works with multiple data feeds"""
    def __init__(self):
        self.data0_lots = []
        self.data1_lots = []
        self.trades_made = 0
    
    def next(self):
        if self.trades_made == 0 and len(self.data0) > 5:
            # Buy data0
            self.buy(data=self.data0, size=100)
            self.trades_made += 1
        elif self.trades_made == 1 and len(self.data1) > 5:
            # Buy data1
            self.buy(data=self.data1, size=75)
            self.trades_made += 1
        elif self.trades_made == 2 and len(self.data0) > 10:
            # Check lots for both data feeds
            self.data0_lots = self.get_position_lots(self.data0)
            self.data1_lots = self.get_position_lots(self.data1)


class PartialSellStrategy(Strategy):
    """Strategy that does partial sells to test lot reduction"""
    def __init__(self):
        self.lots_before_sell = []
        self.lots_after_sell = []
        self.order_count = 0
        self.sell_done = False
    
    def next(self):
        if self.order_count == 0:
            # Initial buy
            self.buy(size=100)
            self.order_count += 1
        elif self.order_count == 1 and len(self.data) > 5:
            # Second buy to create multiple lots
            self.buy(size=50)
            self.order_count += 1
        elif self.order_count == 2 and len(self.data) > 10 and not self.sell_done:
            # Capture lots before sell
            self.lots_before_sell = self.get_position_lots()
            # Partial sell (should reduce first lot due to FIFO)
            self.sell(size=30)
            self.sell_done = True
        elif self.sell_done and len(self.data) > 15:
            # Capture lots after sell
            self.lots_after_sell = self.get_position_lots()


class PositionReversalStrategy(Strategy):
    """Strategy that tests position reversal scenarios"""
    def __init__(self):
        self.long_lots = []
        self.short_lots = []
        self.after_reversal_lots = []
        self.phase = 0
    
    def next(self):
        if self.phase == 0:
            # Go long
            self.buy(size=100)
            self.phase += 1
        elif self.phase == 1 and len(self.data) > 5:
            # Capture long lots
            self.long_lots = self.get_position_lots()
            # Reverse to short (sell more than position)
            self.sell(size=200)  # Will close long and open short
            self.phase += 1
        elif self.phase == 2 and len(self.data) > 10:
            # Capture short position lots (should be empty for short)
            self.short_lots = self.get_position_lots()
            # Close short position
            self.buy(size=100)
            self.phase += 1
        elif self.phase == 3 and len(self.data) > 15:
            # Capture lots after closing short
            self.after_reversal_lots = self.get_position_lots()


class ErrorConditionStrategy(Strategy):
    """Strategy to test error conditions and edge cases"""
    def __init__(self):
        self.error_tests_done = False
        self.none_data_result = None
        self.invalid_name_result = None
        self.none_broker_result = None
    
    def next(self):
        if not self.error_tests_done:
            # Test with None data (should use default data)
            self.none_data_result = self.get_position_lots(data=None)
            
            # Test with invalid name (should raise exception or handle gracefully)
            try:
                self.invalid_name_result = self.get_position_lots_by_name("INVALID_NAME")
            except:
                self.invalid_name_result = "ERROR_HANDLED"
            
            # Test with None broker (should use default broker)
            self.none_broker_result = self.get_position_lots(broker=None)
            
            self.error_tests_done = True


class TestStrategyTaxLotAccess(unittest.TestCase):
    
    def test_strategy_can_access_tax_lots(self):
        """Test that strategies can access tax lots through new methods"""
        cerebro = bt.Cerebro()
        
        # Create test data
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 12, 31)
        )
        cerebro.adddata(data)
        
        # Add our test strategy
        cerebro.addstrategy(TestTaxLotStrategy)
        
        # Set up broker
        cerebro.broker.setcash(10000)
        
        # Run the strategy
        results = cerebro.run()
        strategy = results[0]
        
        # Verify that tax lots were accessed
        self.assertTrue(hasattr(strategy, 'lots_accessed'))
        self.assertIsInstance(strategy.lots_accessed, list)
        self.assertTrue(hasattr(strategy, 'lots_by_name_accessed'))
        self.assertIsInstance(strategy.lots_by_name_accessed, list)
        
        # Should have tax lots since we made purchases
        if strategy.lots_accessed:
            # Verify that the lots are actual TaxLot objects
            from backtrader.position import TaxLot
            for lot in strategy.lots_accessed:
                self.assertIsInstance(lot, TaxLot)
                self.assertGreater(lot.qty, 0)
                self.assertGreater(lot.price, 0)
            
            # Verify both methods return the same data
            self.assertEqual(len(strategy.lots_accessed), len(strategy.lots_by_name_accessed))
    
    def test_get_position_lots_method_exists(self):
        """Test that the new methods exist on Strategy class"""
        # We need to test this through cerebro since Strategy requires it
        cerebro = bt.Cerebro()
        
        # Create minimal test data
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 1, 2)
        )
        cerebro.adddata(data)
        cerebro.addstrategy(NoTradeStrategy)
        cerebro.broker.setcash(10000)
        
        # Run and get strategy instance
        results = cerebro.run()
        strategy = results[0]
        
        # Check that methods exist
        self.assertTrue(hasattr(strategy, 'get_position_lots'))
        self.assertTrue(hasattr(strategy, 'get_position_lots_by_name'))
        
        # Check that they're callable
        self.assertTrue(callable(strategy.get_position_lots))
        self.assertTrue(callable(strategy.get_position_lots_by_name))
    
    def test_get_position_lots_with_no_position(self):
        """Test that get_position_lots returns empty list when no position exists"""
        cerebro = bt.Cerebro()
        
        # Create minimal test data
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 1, 5)
        )
        cerebro.adddata(data)
        cerebro.addstrategy(NoTradeStrategy)
        cerebro.broker.setcash(10000)
        
        # Run and verify
        results = cerebro.run()
        strategy = results[0]
        
        self.assertTrue(strategy.lots_checked)
        self.assertEqual(len(strategy.empty_lots), 0)  # Should be empty

    def test_multi_data_tax_lot_access(self):
        """Test tax lot access with multiple data feeds"""
        cerebro = bt.Cerebro()
        
        # Add two different data feeds
        data0 = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 3, 31)
        )
        data1 = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-002.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 3, 31)
        )
        cerebro.adddata(data0)
        cerebro.adddata(data1)
        
        cerebro.addstrategy(MultiDataStrategy)
        cerebro.broker.setcash(20000)
        
        results = cerebro.run()
        strategy = results[0]
        
        # Should have separate lots for each data feed
        self.assertIsInstance(strategy.data0_lots, list)
        self.assertIsInstance(strategy.data1_lots, list)
        
        # If trades were executed, lots should be different for each data
        if strategy.data0_lots and strategy.data1_lots:
            # Lots should be independent for each data feed
            self.assertNotEqual(strategy.data0_lots, strategy.data1_lots)

    def test_partial_sell_lot_reduction(self):
        """Test that partial sells properly reduce tax lots using FIFO"""
        cerebro = bt.Cerebro()
        
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 6, 30)
        )
        cerebro.adddata(data)
        cerebro.addstrategy(PartialSellStrategy)
        cerebro.broker.setcash(15000)
        
        results = cerebro.run()
        strategy = results[0]
        
        # Verify we captured lots before and after sell
        if strategy.lots_before_sell and strategy.lots_after_sell:
            # Should have fewer total shares after sell
            total_before = sum(lot.qty for lot in strategy.lots_before_sell)
            total_after = sum(lot.qty for lot in strategy.lots_after_sell)
            self.assertGreater(total_before, total_after)
            
            # Should have reduced by 30 shares (the sell amount)
            self.assertEqual(total_before - total_after, 30)

    def test_position_reversal_scenarios(self):
        """Test tax lot behavior during position reversals (long to short)"""
        cerebro = bt.Cerebro()
        
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 6, 30)
        )
        cerebro.adddata(data)
        cerebro.addstrategy(PositionReversalStrategy)
        cerebro.broker.setcash(15000)
        
        results = cerebro.run()
        strategy = results[0]
        
        # Long position should have had tax lots
        if strategy.long_lots:
            from backtrader.position import TaxLot
            for lot in strategy.long_lots:
                self.assertIsInstance(lot, TaxLot)
                self.assertGreater(lot.qty, 0)
        
        # Short position should have empty tax lots (shorts don't track lots)
        self.assertEqual(len(strategy.short_lots), 0)
        
        # After closing short, should be back to empty lots
        self.assertEqual(len(strategy.after_reversal_lots), 0)

    def test_error_conditions_and_edge_cases(self):
        """Test error conditions and edge cases"""
        cerebro = bt.Cerebro()
        
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 1, 10)
        )
        cerebro.adddata(data)
        cerebro.addstrategy(ErrorConditionStrategy)
        cerebro.broker.setcash(10000)
        
        results = cerebro.run()
        strategy = results[0]
        
        self.assertTrue(strategy.error_tests_done)
        
        # None data should work (use default data)
        self.assertIsInstance(strategy.none_data_result, list)
        
        # Invalid name should be handled gracefully
        self.assertIsNotNone(strategy.invalid_name_result)
        
        # None broker should work (use default broker)
        self.assertIsInstance(strategy.none_broker_result, list)

    def test_tax_lot_data_integrity(self):
        """Test that tax lot data maintains integrity across operations"""
        cerebro = bt.Cerebro()
        
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 12, 31)
        )
        cerebro.adddata(data)
        
        class IntegrityTestStrategy(Strategy):
            def __init__(self):
                self.integrity_checks = []
                self.trade_count = 0
            
            def next(self):
                if self.trade_count < 5 and len(self.data) % 10 == 0:
                    # Make periodic trades
                    if self.trade_count % 2 == 0:
                        self.buy(size=100)
                    else:
                        position = self.getposition()
                        if position.size > 0:
                            self.sell(size=min(50, position.size))
                    self.trade_count += 1
                
                # Check integrity every few days
                if len(self.data) % 20 == 0:
                    lots = self.get_position_lots()
                    position = self.getposition()
                    
                    # Total lots quantity should equal position size
                    total_lot_qty = sum(lot.qty for lot in lots)
                    
                    if position.size > 0:  # Only check for long positions
                        self.integrity_checks.append({
                            'day': len(self.data),
                            'position_size': position.size,
                            'total_lot_qty': total_lot_qty,
                            'lots_count': len(lots),
                            'integrity_ok': total_lot_qty == position.size
                        })
        
        cerebro.addstrategy(IntegrityTestStrategy)
        cerebro.broker.setcash(20000)
        
        results = cerebro.run()
        strategy = results[0]
        
        # Verify integrity checks
        if strategy.integrity_checks:
            for check in strategy.integrity_checks:
                if check['position_size'] > 0:  # Only check long positions
                    self.assertTrue(check['integrity_ok'], 
                                  f"Integrity failed on day {check['day']}: "
                                  f"position={check['position_size']}, "
                                  f"lots_total={check['total_lot_qty']}")

    def test_concurrent_access_safety(self):
        """Test that tax lot access is safe when called multiple times"""
        cerebro = bt.Cerebro()
        
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 3, 31)
        )
        cerebro.adddata(data)
        
        class ConcurrentAccessStrategy(Strategy):
            def __init__(self):
                self.access_results = []
            
            def next(self):
                if len(self.data) == 10:
                    self.buy(size=100)
                elif len(self.data) > 15:
                    # Multiple rapid accesses
                    lots1 = self.get_position_lots()
                    lots2 = self.get_position_lots()
                    lots3 = self.get_position_lots_by_name()
                    
                    # All should return identical results
                    self.access_results = [lots1, lots2, lots3]
        
        cerebro.addstrategy(ConcurrentAccessStrategy)
        cerebro.broker.setcash(10000)
        
        results = cerebro.run()
        strategy = results[0]
        
        if strategy.access_results and len(strategy.access_results) == 3:
            lots1, lots2, lots3 = strategy.access_results
            
            # All accesses should return the same number of lots
            self.assertEqual(len(lots1), len(lots2))
            self.assertEqual(len(lots2), len(lots3))
            
            # If lots exist, they should have identical data
            if lots1:
                for i in range(len(lots1)):
                    self.assertEqual(lots1[i].qty, lots2[i].qty)
                    self.assertEqual(lots1[i].price, lots2[i].price)
                    self.assertEqual(lots1[i].qty, lots3[i].qty)

    def test_large_number_of_lots(self):
        """Test that tax lot access works correctly with trading activity"""
        cerebro = bt.Cerebro()
        
        data = bt.feeds.BacktraderCSVData(
            dataname='../datas/2006-day-001.txt',
            fromdate=datetime.datetime(2006, 1, 1),
            todate=datetime.datetime(2006, 3, 31)
        )
        cerebro.adddata(data)
        
        class LotsTestStrategy(Strategy):
            def __init__(self):
                self.lots_snapshots = []
                self.trade_count = 0
            
            def next(self):
                # Make a few trades and capture lots after each
                if self.trade_count == 0 and len(self.data) > 10:
                    self.buy(size=100)
                    self.trade_count += 1
                elif self.trade_count == 1 and len(self.data) > 20:
                    lots = self.get_position_lots()
                    self.lots_snapshots.append(('after_first_buy', len(lots), lots))
                    self.buy(size=50)  # Second buy
                    self.trade_count += 1
                elif self.trade_count == 2 and len(self.data) > 30:
                    lots = self.get_position_lots()
                    self.lots_snapshots.append(('after_second_buy', len(lots), lots))
                    self.sell(size=25)  # Partial sell
                    self.trade_count += 1
                elif self.trade_count == 3 and len(self.data) > 40:
                    lots = self.get_position_lots()
                    self.lots_snapshots.append(('after_partial_sell', len(lots), lots))
        
        cerebro.addstrategy(LotsTestStrategy)
        cerebro.broker.setcash(50000)
        
        results = cerebro.run()
        strategy = results[0]
        
        # Verify we captured snapshots and they make sense
        self.assertGreater(len(strategy.lots_snapshots), 0)
        
        for snapshot_name, lot_count, lots in strategy.lots_snapshots:
            # Each snapshot should have valid data
            self.assertIsInstance(lots, list)
            self.assertEqual(len(lots), lot_count)
            
            # If lots exist, they should be valid TaxLot objects
            if lots:
                from backtrader.position import TaxLot
                for lot in lots:
                    self.assertIsInstance(lot, TaxLot)
                    self.assertGreater(lot.qty, 0)
                    self.assertGreater(lot.price, 0)


if __name__ == '__main__':
    unittest.main() 
